<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Services\Frontend\HomeService;
use App\Services\Frontend\ConferenceStatisticsService;
use App\Services\AdvertisementService;
use App\Exceptions\CategoryNotFoundException;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

use App\Services\ConferenceCardService;

class HomeController extends Controller
{
    protected HomeService $homeService;
    protected ConferenceCardService $conferenceCardService;
    protected ConferenceStatisticsService $statisticsService;
    protected AdvertisementService $advertisementService;

    /**
     * 构造函数，注入首页服务
     *
     * @param HomeService $homeService
     * @param ConferenceCardService $conferenceCardService
     * @param ConferenceStatisticsService $statisticsService
     */
    public function __construct(
        HomeService $homeService,
        ConferenceCardService $conferenceCardService,
        ConferenceStatisticsService $statisticsService,
        AdvertisementService $advertisementService
    ) {
        $this->homeService = $homeService;
        $this->conferenceCardService = $conferenceCardService;
        $this->statisticsService = $statisticsService;
        $this->advertisementService = $advertisementService;
    }

    /**
     * 显示网站首页
     *
     * @return View
     */
    public function index(): View
    {
        // 获取首页所需的所有数据
        $data = $this->homeService->getHomePageData();

        // 获取最受欢迎的五个国家
        $data['topCountries'] = $this->statisticsService->getTopCountriesByConferences(5);

        // 获取统计数据
        $totalConferences = $this->statisticsService->getTotalConferencesCount();
        $activeCountries = $this->statisticsService->getActiveCountriesCount();
        $academicFields = $totalConferences * 35;

        $data['statistics'] = [
            'totalConferences' => $totalConferences,
            'activeCountries' => $activeCountries,
            'academicFields' => $academicFields,
        ];

        // 获取所有大洲的会议数据，用于JavaScript
        $data['allContinentData'] = $this->statisticsService->getAllContinentConferenceDataForJs();

        return view('frontend.home.index', $data);
    }

    /**
     * 显示分类会议列表页
     *
     * @param string $url 分类URL标识
     * @return View
     * @throws NotFoundHttpException 如果分类不存在
     */
    public function lists(string $url): View
    {
        try {
            // 获取分类会议列表数据
            $data = $this->homeService->getCategoryConferences($url);

            // 获取该分类的广告
            $data['advertisements'] = $this->advertisementService->getActiveAdvertisements($data['category']->id, 5);

            return view('frontend.home.lists', $data);
        } catch (\Exception $e) {
            // 记录详细错误信息
            \Log::error('Category list page error', [
                'url' => $url,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 只有当明确是分类不存在时才返回404
            if ($e instanceof CategoryNotFoundException) {
                abort(404, '分类不存在或已被删除');
            }

            // 其他错误返回500错误页面，而不是404
            abort(500, '服务器内部错误，请稍后重试');
        }
    }

    /**
     * 显示所有会议列表页
     *
     * @param Request $request
     * @return View
     */
    public function conferences(Request $request): View
    {
        $featuredConferences = $this->conferenceCardService->getFeaturedConferences();

        // 获取即将召开的会议
        $upcomingConferences = $this->conferenceCardService->getUpcomingConferences();

        return view('frontend.home.conferences', compact('featuredConferences', 'upcomingConferences'));
    }
    
    /**
     * 显示按地点筛选的会议列表页
     *
     * @param string $url 地点URL标识
     * @return View
     * @throws NotFoundHttpException 如果地点不存在
     */
    public function venueConferences(string $url): View
    {
        try {
            // 获取按地点筛选的会议列表数据
            $data = $this->homeService->getVenueConferences($url);
            
            return view('frontend.home.venue_conferences', $data);
        } catch (\Exception $e) {
            // 记录详细错误信息
            \Log::error('Venue conferences page error', [
                'url' => $url,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            abort(404, '地点不存在或已被删除');
        }
    }
}
