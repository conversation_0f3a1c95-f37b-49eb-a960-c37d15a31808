<?php

namespace App\Filament\Member\Pages\Auth;

use App\Models\Category;
use App\Models\Member;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\View;
use Filament\Forms\Form;
use Filament\Pages\Auth\Register as BaseRegister;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\ValidationException;

class Register extends BaseRegister
{
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                $this->getEmailFormComponent(),
                $this->getUsernameFormComponent(),
                $this->getAreaFormComponent(),
                $this->getPasswordFormComponent(),
                $this->getPasswordConfirmationFormComponent(),
                $this->getCaptchaDisplayComponent(),
                $this->getCaptchaFormComponent(),
            ])
            ->statePath('data');
    }

    protected function getEmailFormComponent(): Component
    {
        return TextInput::make('email')
            ->label('Email Address')
            ->email()
            ->required()
            ->maxLength(100)
            ->unique(Member::class)
            ->autocomplete()
            ->autofocus()
            ->extraInputAttributes(['tabindex' => 1])
            ->placeholder('Enter your email address')
            ->helperText('This will be your login email.');
    }

    protected function getUsernameFormComponent(): Component
    {
        return TextInput::make('username')
            ->label('Username')
            ->required()
            ->maxLength(100)
            ->unique(Member::class)
            ->extraInputAttributes(['tabindex' => 2])
            ->placeholder('Enter your username')
            ->helperText('This will be your display name.');
    }

    protected function getAreaFormComponent(): Component
    {
        return Select::make('area')
            ->label('Area of Interest')
            ->required()
            ->options(function () {
                return Category::where('fid', 0)
                    ->orderBy('listorder')
                    ->pluck('name', 'id');
            })
            ->searchable()
            ->preload()
            ->extraInputAttributes(['tabindex' => 3])
            ->placeholder('Select your area of interest')
            ->helperText('Select the conference field you are most interested in.');
    }

    protected function getPasswordFormComponent(): Component
    {
        return TextInput::make('password')
            ->label('Password')
            ->password()
            ->required()
            ->minLength(6)
            ->maxLength(15)
            ->extraInputAttributes(['tabindex' => 4])
            ->placeholder('Enter your password (6-15 characters)')
            ->helperText('Password must be 6-15 characters long.');
    }

    protected function getPasswordConfirmationFormComponent(): Component
    {
        return TextInput::make('password_confirmation')
            ->label('Confirm Password')
            ->password()
            ->required()
            ->same('password')
            ->extraInputAttributes(['tabindex' => 5])
            ->placeholder('Enter your password again');
    }

    protected function getCaptchaDisplayComponent(): Component
    {
        return View::make('filament.forms.components.captcha-display');
    }

    protected function getCaptchaFormComponent(): Component
    {
        return TextInput::make('captcha')
            ->label('Captcha')
            ->required()
            ->minLength(4)
            ->maxLength(4)
            ->extraInputAttributes([
                'tabindex' => 6,
                'autocomplete' => 'off',
                'class' => 'captcha-input',
                'data-validate-url' => route('validate.captcha'),
            ])
            ->placeholder('Enter the captcha code')
            ->helperText('The captcha is case-sensitive.')
            ->live()
            ->afterStateUpdated(function ($state, $component) {
                if (strlen($state) === 4) {
                    $component->state($state);
                }
            });
    }

    protected function handleRegistration(array $data): Model
    {
        // Check rate limiting
        $this->checkRateLimit();

        // Validate captcha
        $this->validateCaptcha($data['captcha']);

        // Create user
        return Member::create([
            'email' => $data['email'],
            'username' => $data['username'],
            'area' => $data['area'],
            'password' => Hash::make($data['password']),
            'pwd_md5' => 0, // New users use bcrypt directly
            'vip' => false,
            'status' => true, // Activate directly, skip email verification
            'regtime' => time(),
            'ip' => request()->ip(),
        ]);
    }

    protected function checkRateLimit(): void
    {
        $key = 'register:' . request()->ip();
        
        if (RateLimiter::tooManyAttempts($key, 5)) {
            $seconds = RateLimiter::availableIn($key);
            throw ValidationException::withMessages([
                'email' => "Too many registration attempts. Please try again in {$seconds} seconds.",
            ]);
        }

        RateLimiter::hit($key, 60); // Max 5 attempts per minute
    }

    protected function validateCaptcha(string $captcha): void
    {
        if (!captcha_check($captcha)) {
            // 刷新验证码，避免用户继续使用错误的验证码
            session()->flash('refresh_captcha', true);
            
            throw ValidationException::withMessages([
                'captcha' => 'The captcha is incorrect. Please try again.',
            ]);
        }
    }

    protected function hasFullWidthFormActions(): bool
    {
        return true;
    }

    protected function getFormActions(): array
    {
        return [
            $this->getRegisterFormAction(),
            $this->getLoginAction(),
        ];
    }

    protected function getLoginAction(): \Filament\Actions\Action
    {
        return \Filament\Actions\Action::make('login')
            ->label('Already have an account? Sign in')
            ->url(filament()->getLoginUrl())
            ->color('gray');
    }
}
