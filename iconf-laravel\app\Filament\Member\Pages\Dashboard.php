<?php

namespace App\Filament\Member\Pages;

use Filament\Pages\Dashboard as BaseDashboard;
use Filament\Widgets\AccountWidget;

class Dashboard extends BaseDashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-home';
    
    // Temporarily use the default view
    // protected static string $view = 'filament.member.pages.dashboard';
    
    protected static ?string $title = 'iConf Member Center';
    
    protected static ?string $navigationLabel = 'Home';
    
    protected static ?int $navigationSort = 1;

    /**
     * Get the page widgets
     */
    public function getWidgets(): array
    {
        return [
            \App\Filament\Member\Widgets\MemberStatsWidget::class,

            \App\Filament\Member\Widgets\MyEventsWidget::class,
            \App\Filament\Member\Widgets\RecentActivitiesWidget::class,
            \App\Filament\Member\Widgets\FocusAreaEventsWidget::class,
        ];
    }

    /**
     * Get the page header actions
     */
    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('create_event')
                ->label('Publish Conference')
                ->icon('heroicon-o-plus-circle')
                ->color('primary')
                ->url(fn(): string => route('filament.member.resources.events.create')),

            \Filament\Actions\Action::make('my_events')
                ->label('My Events')
                ->icon('heroicon-o-calendar-days')
                ->color('gray')
                ->url(fn(): string => route('filament.member.resources.events.index')),
        ];
    }

    /**
     * Get the page data
     */
    public function mount(): void
    {
        // Page data can be loaded here
        // parent::mount(); // BaseDashboard has no mount method, remove this line
    }
}
