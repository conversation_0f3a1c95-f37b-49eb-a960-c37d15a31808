<?php

/**
 * 测试分类会议列表优化后的代码
 * 
 * 使用方法：
 * php artisan tinker
 * include 'test_category_optimization.php';
 */

use App\Services\Frontend\HomeService;
use App\Exceptions\CategoryNotFoundException;
use Illuminate\Support\Facades\Log;

echo "=== 分类会议列表优化测试 ===\n\n";

// 获取HomeService实例
$homeService = app(HomeService::class);

// 测试用例
$testCases = [
    // 测试存在的分类URL
    'existing_category' => 'computer-science', // 请替换为实际存在的分类URL
    
    // 测试不存在的分类URL
    'non_existing_category' => 'non-existing-category-url',
    
    // 测试空字符串
    'empty_string' => '',
    
    // 测试带空格的URL
    'url_with_spaces' => ' computer-science ',
    
    // 测试URL编码的情况
    'encoded_url' => 'computer%2Dscience',
];

foreach ($testCases as $testName => $url) {
    echo "--- 测试: {$testName} (URL: '{$url}') ---\n";
    
    try {
        $startTime = microtime(true);
        $result = $homeService->getCategoryConferences($url, 5);
        $endTime = microtime(true);
        
        $executionTime = round(($endTime - $startTime) * 1000, 2);
        
        echo "✅ 成功获取数据\n";
        echo "   执行时间: {$executionTime}ms\n";
        echo "   分类名称: " . ($result['category']->name ?? 'N/A') . "\n";
        echo "   会议数量: " . ($result['conferences']->count() ?? 0) . "\n";
        echo "   总会议数: " . ($result['conferences']->total() ?? 0) . "\n";
        echo "   特色会议: " . ($result['featuredConferences']->count() ?? 0) . "\n";
        echo "   广告数量: " . ($result['advertisements']->count() ?? 0) . "\n";
        
    } catch (CategoryNotFoundException $e) {
        echo "❌ 分类不存在: " . $e->getMessage() . "\n";
        echo "   分类URL: " . $e->getCategoryUrl() . "\n";
        
    } catch (\Exception $e) {
        echo "💥 其他错误: " . $e->getMessage() . "\n";
        echo "   错误类型: " . get_class($e) . "\n";
    }
    
    echo "\n";
}

// 性能测试
echo "=== 性能测试 ===\n";
$performanceTestUrl = 'computer-science'; // 请替换为实际存在的分类URL

$times = [];
for ($i = 0; $i < 5; $i++) {
    try {
        $startTime = microtime(true);
        $result = $homeService->getCategoryConferences($performanceTestUrl, 10);
        $endTime = microtime(true);
        
        $times[] = ($endTime - $startTime) * 1000;
        echo "第" . ($i + 1) . "次: " . round($times[$i], 2) . "ms\n";
        
    } catch (\Exception $e) {
        echo "第" . ($i + 1) . "次: 错误 - " . $e->getMessage() . "\n";
    }
}

if (!empty($times)) {
    $avgTime = array_sum($times) / count($times);
    $minTime = min($times);
    $maxTime = max($times);
    
    echo "\n性能统计:\n";
    echo "  平均时间: " . round($avgTime, 2) . "ms\n";
    echo "  最短时间: " . round($minTime, 2) . "ms\n";
    echo "  最长时间: " . round($maxTime, 2) . "ms\n";
}

echo "\n=== 测试完成 ===\n";

// 检查日志
echo "\n=== 最近的日志记录 ===\n";
echo "请检查 storage/logs/laravel.log 文件中的相关日志记录\n";
echo "搜索关键词: 'Finding category by URL', 'Conferences loaded', 'Category found'\n";
