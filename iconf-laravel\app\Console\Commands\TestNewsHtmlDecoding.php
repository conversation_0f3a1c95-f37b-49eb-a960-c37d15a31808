<?php

namespace App\Console\Commands;

use App\Models\News;
use Illuminate\Console\Command;

class TestNewsHtmlDecoding extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:news-html-decoding';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试新闻HTML解码功能';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 测试新闻HTML解码功能...');
        $this->newLine();

        // 查找包含HTML实体的新闻
        $newsWithHtmlEntities = News::where('content', 'LIKE', '%&lt;%')
            ->orWhere('content', 'LIKE', '%&gt;%')
            ->orWhere('content', 'LIKE', '%&quot;%')
            ->limit(3)
            ->get();

        if ($newsWithHtmlEntities->isEmpty()) {
            $this->warn('没有找到包含HTML实体的新闻');
            return;
        }

        foreach ($newsWithHtmlEntities as $news) {
            $this->info("📰 新闻ID: {$news->id}");
            $this->info("📝 标题: {$news->title}");
            $this->newLine();

            // 显示原始内容（截取前200字符）
            $originalContent = mb_substr($news->content, 0, 200);
            $this->line("🔤 原始内容:");
            $this->line($originalContent . '...');
            $this->newLine();

            // 显示解码后的内容（截取前200字符）
            $decodedContent = mb_substr($news->getDecodedContent(), 0, 200);
            $this->line("✨ 解码后内容:");
            $this->line($decodedContent . '...');
            $this->newLine();

            // 检查是否包含HTML标签
            $hasHtmlTags = preg_match('/<[^>]+>/', $news->getDecodedContent());
            $this->line("🏷️  包含HTML标签: " . ($hasHtmlTags ? '是' : '否'));
            
            $this->line(str_repeat('-', 80));
            $this->newLine();
        }

        $this->info('✅ HTML解码功能测试完成！');
    }
}
