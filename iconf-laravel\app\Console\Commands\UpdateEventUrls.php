<?php

namespace App\Console\Commands;

use App\Services\EventUrlService;
use Illuminate\Console\Command;

class UpdateEventUrls extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'events:update-urls 
                            {--limit=100 : 每次处理的记录数量}
                            {--dry-run : 仅显示将要更新的内容，不实际执行}
                            {--force : 强制更新所有URL，包括已有有效URL的记录}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '批量更新会议URL标识符';

    /**
     * Execute the console command.
     */
    public function handle(EventUrlService $urlService)
    {
        $limit = (int) $this->option('limit');
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');

        $this->info('开始批量更新会议URL标识符...');
        
        if ($dryRun) {
            $this->warn('⚠️  这是预览模式，不会实际更新数据');
        }

        if ($force) {
            $this->warn('⚠️  强制模式：将更新所有会议的URL');
        }

        // 执行批量更新
        if ($force) {
            $result = $this->forceUpdateAllUrls($urlService, $limit, $dryRun);
        } else {
            $result = $urlService->batchUpdateUrls($limit);
        }

        // 显示结果
        $this->displayResults($result, $dryRun);

        return Command::SUCCESS;
    }

    /**
     * 强制更新所有会议的URL
     */
    private function forceUpdateAllUrls(EventUrlService $urlService, int $limit, bool $dryRun): array
    {
        $updated = 0;
        $skipped = 0;
        $errors = 0;

        $events = \App\Models\Event::limit($limit)->get();

        foreach ($events as $event) {
            try {
                if (empty($event->event)) {
                    $skipped++;
                    continue;
                }

                $newUrl = $urlService->generateUniqueUrl($event->event, $event->id);
                
                if ($dryRun) {
                    $this->line("会议 #{$event->id}: '{$event->title}' -> URL: '{$event->url}' => '{$newUrl}'");
                    $updated++;
                } else {
                    $event->update(['url' => $newUrl]);
                    $updated++;
                }
            } catch (\Exception $e) {
                $errors++;
                $this->error("更新会议 #{$event->id} 失败: " . $e->getMessage());
            }
        }

        return [
            'updated' => $updated,
            'skipped' => $skipped,
            'errors' => $errors,
            'total_processed' => $events->count(),
        ];
    }

    /**
     * 显示处理结果
     */
    private function displayResults(array $result, bool $dryRun): void
    {
        $this->newLine();
        $this->info('📊 处理结果统计：');
        
        $headers = ['项目', '数量'];
        $rows = [
            ['总处理数量', $result['total_processed']],
            [$dryRun ? '将要更新' : '成功更新', $result['updated']],
            ['跳过', $result['skipped']],
            ['错误', $result['errors']],
        ];

        $this->table($headers, $rows);

        if ($result['updated'] > 0) {
            if ($dryRun) {
                $this->info("✅ 预览完成！发现 {$result['updated']} 个会议需要更新URL");
                $this->info("💡 运行不带 --dry-run 参数的命令来实际执行更新");
            } else {
                $this->info("✅ 成功更新了 {$result['updated']} 个会议的URL");
            }
        }

        if ($result['errors'] > 0) {
            $this->error("❌ 有 {$result['errors']} 个会议更新失败，请检查日志");
        }

        if ($result['skipped'] > 0) {
            $this->warn("⚠️  跳过了 {$result['skipped']} 个没有会议简称的记录");
        }
    }
}
