# 图表导出使用指南

## 概述

本项目的架构图表使用Mermaid语法编写，可以通过多种方式查看和导出为图片格式。本指南将详细介绍各种使用方法。

## 1. 在线查看和导出

### 1.1 Mermaid Live Editor (推荐)
**网址**: https://mermaid.live/

**使用步骤**:
1. 打开 Mermaid Live Editor
2. 将图表代码粘贴到左侧编辑器
3. 右侧会实时显示图表
4. 点击右上角的下载按钮导出为PNG/SVG

**优点**:
- 无需安装软件
- 实时预览
- 支持多种导出格式
- 可以分享链接

### 1.2 GitHub/GitLab
**使用方法**:
- 直接在GitHub或GitLab的Markdown文件中查看
- 图表会自动渲染显示

**优点**:
- 版本控制
- 团队协作
- 无需额外工具

## 2. 本地工具

### 2.1 VS Code + Mermaid扩展

**安装步骤**:
1. 安装VS Code
2. 安装"Mermaid Markdown Syntax Highlighting"扩展
3. 安装"Markdown Preview Mermaid Support"扩展

**使用方法**:
1. 打开包含Mermaid图表的Markdown文件
2. 按`Ctrl+Shift+V`预览
3. 右键图表选择"Export"导出

**优点**:
- 集成开发环境
- 支持语法高亮
- 可以直接编辑

### 2.2 Typora (推荐)

**安装**: 下载Typora编辑器

**使用方法**:
1. 打开Markdown文件
2. 图表自动渲染
3. 右键图表选择"复制为图片"或"导出"

**优点**:
- 所见即所得
- 导出质量高
- 操作简单

### 2.3 命令行工具

**安装Mermaid CLI**:
```bash
npm install -g @mermaid-js/mermaid-cli
```

**使用方法**:
```bash
# 导出为PNG
mmdc -i input.mmd -o output.png

# 导出为SVG
mmdc -i input.mmd -o output.svg

# 指定主题
mmdc -i input.mmd -o output.png -t dark
```

**优点**:
- 批量处理
- 自动化集成
- 高度可定制

## 3. 各图表的具体导出方法

### 3.1 项目架构图

**文件位置**: `doc/项目架构图表文档.md` (第一个图表)

**导出建议**:
- **格式**: PNG (用于文档) 或 SVG (用于演示)
- **尺寸**: 1200x800像素
- **主题**: default 或 neutral

**使用场景**:
- 项目介绍文档
- 技术方案演示
- 团队培训材料

### 3.2 数据库关系图

**文件位置**: `doc/项目架构图表文档.md` (第二个图表)

**导出建议**:
- **格式**: PNG 或 PDF
- **尺寸**: 1400x1000像素
- **主题**: default

**使用场景**:
- 数据库设计文档
- 开发参考手册
- 系统分析报告

### 3.3 业务流程图

**文件位置**: `doc/项目架构图表文档.md` (第三个图表)

**导出建议**:
- **格式**: PNG 或 SVG
- **尺寸**: 1600x1200像素
- **主题**: default

**使用场景**:
- 业务需求文档
- 用户手册
- 培训材料

## 4. 图表代码提取

### 4.1 架构图代码
```mermaid
graph TB
    subgraph "前端展示层"
        A[用户访问] --> B[Bootstrap + jQuery界面]
        B --> C[响应式设计]
    end
    
    subgraph "应用层 - ThinkPHP 3.2"
        D[Home模块 - 前台] --> E[IndexController]
        D --> F[MemberController]
        D --> G[CheckloginController]
        
        H[AdminICF模块 - 后台] --> I[IndexController]
        H --> J[LoginController]
        H --> K[CheckAdminController]
        
        L[Common模块 - 公共] --> M[function.php]
        L --> N[config.php]
        L --> O[seo_config.php]
    end
    
    subgraph "业务逻辑层"
        P[EventModel] --> Q[会议管理]
        R[MemberModel] --> S[用户管理]
        T[CategoryModel] --> U[分类管理]
        V[NewsModel] --> W[新闻管理]
    end
    
    subgraph "数据存储层"
        X[(MySQL数据库)] --> Y[event - 会议表]
        X --> Z[member - 用户表]
        X --> AA[category - 分类表]
        X --> BB[news - 新闻表]
        X --> CC[ad_txt - 广告表]
        X --> DD[country - 地区表]
        X --> EE[list - 关联表]
        
        FF[文件存储] --> GG[Uploads目录]
        GG --> HH[按日期分目录]
        HH --> II[图片文件]
        HH --> JJ[文档文件]
    end
    
    subgraph "核心功能模块"
        KK[会议发布] --> LL[用户提交]
        LL --> MM[管理员审核]
        MM --> NN[前台展示]
        
        OO[分类管理] --> PP[二级分类]
        PP --> QQ[多分类归属]
        
        RR[用户系统] --> SS[注册登录]
        SS --> TT[权限控制]
        TT --> UU[VIP机制]
        
        VV[内容管理] --> WW[新闻发布]
        VV --> XX[广告管理]
        VV --> YY[视频管理]
    end
    
    A --> D
    A --> H
    E --> P
    F --> R
    I --> P
    I --> T
    I --> V
    
    Q --> Y
    S --> Z
    U --> AA
    W --> BB
    
    style A fill:#e1f5fe
    style X fill:#f3e5f5
    style D fill:#e8f5e8
    style H fill:#fff3e0
```

### 4.2 数据库关系图代码
```mermaid
erDiagram
    event {
        int id PK
        varchar cid "分类ID(多个)"
        int uid FK
        int venue FK
        varchar city
        varchar hotel
        varchar title
        varchar event
        varchar url
        int start_date
        int end_date
        int sub_date
        varchar email
        varchar web
        varchar tel
        text content
        varchar pic
        tinyint ding "置顶"
        tinyint push "推荐"
        tinyint status "状态"
        int addtime
        varchar summary
        tinyint is_featured "特色推荐"
    }
    
    category {
        int id PK
        int listorder
        int fid "父分类ID"
        varchar name
        varchar url
    }
    
    list {
        int id PK
        int cid FK
        int eid FK
    }
    
    member {
        int id PK
        varchar email
        varchar username
        smallint area
        varchar password
        varchar ip
        varchar regtime
        tinyint vip
        tinyint status
    }
    
    country {
        int id PK
        int listorder
        varchar venue
        varchar url
        int fid "父级ID"
    }
    
    news {
        int id PK
        varchar title
        text content
        timestamp publish_time
        int column_id FK
        varchar cover
        text summary
        int click_rate
        tinyint is_featured
    }
    
    news_type {
        int id PK
        varchar column_name
        int column_sort
    }
    
    ad_txt {
        int id PK
        int listorder
        varchar title
        int cid FK
        varchar link
        varchar pic
        text content
        int endtime
    }
    
    admin {
        int id PK
        varchar username
        varchar password
    }
    
    page {
        int id PK
        varchar title
        text content
    }
    
    vod {
        int id PK
        varchar title
        varchar file_path
        varchar external_link
        text video_code
        varchar cover_image
        int views
        tinyint is_recommended
        varchar vod_type
        timestamp publish_time
    }
    
    %% 关系定义
    event ||--o{ list : "会议分类关联"
    category ||--o{ list : "分类会议关联"
    category ||--o{ category : "父子分类"
    member ||--o{ event : "用户发布会议"
    country ||--o{ event : "会议地点"
    country ||--o{ country : "父子地区"
    news_type ||--o{ news : "新闻分类"
    category ||--o{ ad_txt : "广告分类"
```

## 5. 导出质量优化

### 5.1 图片质量设置
- **DPI**: 300 (用于打印) 或 150 (用于屏幕)
- **格式选择**: 
  - PNG: 适合包含文字的图表
  - SVG: 适合需要缩放的矢量图
  - PDF: 适合文档嵌入

### 5.2 主题选择
Mermaid支持多种主题:
- `default`: 默认主题，适合大多数场景
- `neutral`: 中性主题，适合正式文档
- `dark`: 深色主题，适合演示
- `forest`: 绿色主题
- `base`: 基础主题

### 5.3 自定义样式
可以在图表代码末尾添加样式定义:
```mermaid
style A fill:#e1f5fe
style B fill:#f3e5f5
```

## 6. 常见问题解决

### 6.1 图表不显示
**可能原因**:
- Mermaid语法错误
- 编辑器不支持Mermaid
- 网络连接问题

**解决方法**:
- 检查语法是否正确
- 使用支持Mermaid的编辑器
- 使用离线工具

### 6.2 导出图片模糊
**解决方法**:
- 增加导出分辨率
- 使用SVG格式
- 调整缩放比例

### 6.3 中文字体显示问题
**解决方法**:
- 确保系统安装中文字体
- 在导出时指定字体
- 使用在线工具导出

## 7. 推荐工作流程

### 7.1 日常使用
1. **编辑**: 使用VS Code或Typora编辑
2. **预览**: 实时查看效果
3. **版本控制**: 提交到Git仓库
4. **分享**: 通过GitHub链接分享

### 7.2 文档制作
1. **设计**: 在Mermaid Live Editor中设计
2. **优化**: 调整布局和样式
3. **导出**: 导出高质量图片
4. **嵌入**: 插入到最终文档中

### 7.3 演示准备
1. **主题**: 选择适合的主题
2. **尺寸**: 导出大尺寸图片
3. **格式**: 使用SVG保证清晰度
4. **备份**: 保留源代码以便修改

---

**文档版本**: v1.0  
**创建日期**: 2025-06-24  
**维护人员**: 开发团队

## 附录: 快速参考

### Mermaid在线工具
- [Mermaid Live Editor](https://mermaid.live/)
- [Mermaid Chart](https://www.mermaidchart.com/)

### 本地编辑器
- [Typora](https://typora.io/)
- [VS Code](https://code.visualstudio.com/)
- [Mark Text](https://marktext.app/)

### 命令行工具
```bash
npm install -g @mermaid-js/mermaid-cli
```
