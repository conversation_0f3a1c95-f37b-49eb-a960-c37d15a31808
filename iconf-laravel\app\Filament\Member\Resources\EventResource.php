<?php

namespace App\Filament\Member\Resources;

use App\Enums\ConferenceStatus;
use App\Filament\Member\Resources\EventResource\Pages;
use App\Models\Event;
use App\Models\Country;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class EventResource extends Resource
{
    protected static ?string $model = Event::class;

    protected static ?string $navigationIcon = 'heroicon-o-calendar-days';

    protected static ?string $navigationLabel = 'My Conferences';

    protected static ?string $modelLabel = 'Conference';

    protected static ?string $pluralModelLabel = 'Conferences';

    protected static ?string $navigationGroup = null;

    protected static ?int $navigationSort = 1;
    
    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    /**
     * Get the currently logged-in member user
     */
    protected static function getCurrentMember()
    {
        return Auth::guard('member')->user();
    }

    /**
     * Check if the current user is a VIP
     */
    protected static function isCurrentUserVip(): bool
    {
        $member = static::getCurrentMember();
        return $member && $member->vip;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema(static::getFormSchema());
    }

    /**
     * Get form field configuration
     */
    protected static function getFormSchema(): array
    {
        $isVip = static::isCurrentUserVip();

        return [
                // Conference Category
                Forms\Components\Section::make('Conference Category')
                    ->description('Please select the subject category of the conference')
                    ->schema([
                        Forms\Components\Select::make('categories')
                            ->label('Select Category')
                            ->relationship('categories', 'name')
                            ->multiple()
                            ->searchable()
                            ->preload()
                            ->options(function () {
                                $categories = \App\Models\Category::all();
                                $options = [];

                                // Get parent categories as groups
                                $parentCategories = $categories->where('fid', 0)->sortBy('listorder');

                                foreach ($parentCategories as $parent) {
                                    // Get second-level categories under this parent category
                                    $children = $categories->where('fid', $parent->id)->sortBy('listorder');

                                    if ($children->isNotEmpty()) {
                                        // If there are sub-categories, create a group
                                        $groupOptions = [];
                                        foreach ($children as $child) {
                                            $groupOptions[$child->id] = $child->name . ' (ID: ' . $child->id . ')';
                                        }
                                        $options[$parent->name] = $groupOptions;
                                    }
                                }

                                return $options;
                            })
                            ->required()
                            ->helperText('First-level categories are for grouping only, please select specific second-level categories')
                            ->validationMessages([
                                'required' => 'Please select at least one category',
                            ])
                            ->rules([
                                function () {
                                    return function (string $attribute, $value, \Closure $fail) {
                                        if (is_array($value)) {
                                            $parentCategoryIds = \App\Models\Category::where('fid', 0)->pluck('id')->toArray();
                                            $selectedParentCategories = array_intersect($value, $parentCategoryIds);

                                            if (!empty($selectedParentCategories)) {
                                                $fail('Cannot select first-level category, please select specific second-level categories');
                                            }
                                        }
                                    };
                                }
                            ]),
                    ])
                    ->collapsible(),

                // Basic Information
                Forms\Components\Section::make('Basic Information')
                    ->description('Fill in the basic information of the conference')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('title')
                                    ->label('Conference Title')
                                    ->required()
                                    ->maxLength(500)
                                    ->columnSpanFull()
                                    ->helperText('Please enter the full title of the conference'),

                                Forms\Components\TextInput::make('event')
                                    ->label('Short Name')
                                    ->maxLength(30)
                                    ->helperText('Short name or abbreviation of the conference'),

                                Forms\Components\TextInput::make('url')
                                    ->label('URL Alias')
                                    ->maxLength(50)
                                    ->helperText('Leave blank to auto-generate, used for conference detail page URL'),

                                // Hidden field: automatically set the publishing user
                                Forms\Components\Hidden::make('uid')
                                    ->default(fn () => static::getCurrentMember()?->id),

                                // Hidden field: automatically set the status
                                Forms\Components\Hidden::make('status')
                                    ->default(fn () => $isVip ? ConferenceStatus::Published : ConferenceStatus::Pending),

                                // Hidden field: automatically set the add time
                                Forms\Components\Hidden::make('addtime')
                                    ->default(fn () => time()),
                            ]),
                    ]),

                // Time Information
                Forms\Components\Section::make('Time Information')
                    ->description('Set important time nodes for the conference')
                    ->schema([
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\DateTimePicker::make('start_date')
                                    ->label('Start Time')
                                    ->required()
                                    ->native(false)
                                    ->displayFormat('Y-m-d H:i')
                                    ->minDate(now())
                                    ->helperText('Date and time when the conference starts'),

                                Forms\Components\DateTimePicker::make('end_date')
                                    ->label('End Time')
                                    ->required()
                                    ->native(false)
                                    ->displayFormat('Y-m-d H:i')
                                    ->after('start_date')
                                    ->minDate(now())
                                    ->helperText('Date and time when the conference ends'),

                                Forms\Components\DateTimePicker::make('sub_date')
                                    ->label('Submission Deadline')
                                    ->native(false)
                                    ->displayFormat('Y-m-d H:i')
                                    ->before('start_date')
                                    ->helperText('Deadline for paper submission (optional)'),
                            ]),
                    ]),

                // Location Information
                Forms\Components\Section::make('Location Information')
                    ->description('Set the location information for the conference')
                    ->schema([
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\Select::make('venue')
                                    ->label('Venue')
                                    ->required()
                                    ->options(function () {
                                        return Country::where('fid', 0)
                                            ->with(['children' => function ($query) {
                                                $query->orderBy('listorder');
                                            }])
                                            ->orderBy('listorder')
                                            ->get()
                                            ->flatMap(function ($parent) {
                                                $options = [];
                                                // Add parent region
                                                $options[$parent->id] = $parent->venue;
                                                // Add child regions
                                                foreach ($parent->children as $child) {
                                                    $options[$child->id] = $parent->venue . ' - ' . $child->venue;
                                                }
                                                return $options;
                                            })
                                            ->toArray();
                                    })
                                    ->searchable()
                                    ->helperText('Select the country or region where the conference will be held'),

                                Forms\Components\TextInput::make('city')
                                    ->label('City')
                                    ->maxLength(50)
                                    ->helperText('Name of the city'),

                                Forms\Components\TextInput::make('hotel')
                                    ->label('Venue/Hotel')
                                    ->maxLength(300)
                                    ->helperText('Specific hotel or conference venue'),
                            ]),
                    ]),

                // Contact Information
                Forms\Components\Section::make('Contact Information')
                    ->description('Fill in the contact information for the conference')
                    ->schema([
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('email')
                                    ->label('Contact Email')
                                    ->email()
                                    ->maxLength(100)
                                    ->rule('email')
                                    ->helperText('Contact email of the conference organizer'),

                                Forms\Components\TextInput::make('web')
                                    ->label('Official Website')
                                    ->url()
                                    ->maxLength(200)
                                    ->rule('url')
                                    ->helperText('Official website of the conference'),

                                Forms\Components\TextInput::make('tel')
                                    ->label('Phone Number')
                                    ->tel()
                                    ->maxLength(50)
                                    ->helperText('Contact phone number of the conference organizer'),
                            ]),
                    ]),

                // Content Information - Display different editors based on VIP status
                Forms\Components\Section::make('Conference Description')
                    ->description($isVip ? 'VIP users can use rich text editor and upload images' : 'Please provide detailed description of the conference')
                    ->schema([
                        Forms\Components\Textarea::make('summary')
                            ->label('Summary')
                            ->rows(3)
                            ->maxLength(1000)
                            ->columnSpanFull()
                            ->live(onBlur: true)
                            ->helperText('Brief summary of the conference, used for listing display (max 1000 characters)'),

                        // Display different editors based on user type
                        Forms\Components\Group::make()
                            ->schema([
                                // VIP user: Rich Text Editor
                                Forms\Components\RichEditor::make('content')
                                    ->label('Detailed Description')
                                    ->columnSpanFull()
                                    ->visible($isVip)
                                    ->toolbarButtons([
                                        'attachFiles',
                                        'blockquote',
                                        'bold',
                                        'bulletList',
                                        'codeBlock',
                                        'h2',
                                        'h3',
                                        'italic',
                                        'link',
                                        'orderedList',
                                        'redo',
                                        'strike',
                                        'underline',
                                        'undo',
                                    ])
                                    ->helperText('Detailed conference introduction, agenda, etc. (VIP feature)'),

                                // Regular user: Basic Text Area
                                Forms\Components\Textarea::make('content')
                                    ->label('Detailed Description')
                                    ->rows(8)
                                    ->maxLength(5000)
                                    ->columnSpanFull()
                                    ->visible(!$isVip)
                                    ->live(onBlur: true)
                                    ->helperText('Please describe the conference content, agenda, etc. in detail (supports line breaks, max 5000 characters)'),
                            ])
                            ->columnSpanFull(),
                    ]),

                // Image Settings - Visible only to VIP users
                Forms\Components\Section::make('Conference Images')
                    ->description('Upload promotional images for the conference (VIP feature)')
                    ->schema([
                        Forms\Components\FileUpload::make('pic')
                            ->label('Conference Image')
                            ->image()
                            ->disk('public')
                            ->directory('events')
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/gif'])
                            ->maxSize(2048) // 2MB
                            ->getUploadedFileNameForStorageUsing(
                                fn ($file): string => (string) str($file->hashName())
                            )
                            ->helperText('Supports JPG, PNG, GIF formats, max 2MB'),
                    ])
                    ->visible($isVip),

                // User Information Prompt
                Forms\Components\Section::make('Publishing Information')
                    ->description($isVip ? 'Conferences published by VIP users will be automatically approved' : 'Conferences published by regular users require admin approval before being displayed')
                    ->schema([
                        Forms\Components\Placeholder::make('user_info')
                            ->label('Publisher')
                            ->content(fn () => static::getCurrentMember()?->username ?? 'Unknown User'),

                        Forms\Components\Placeholder::make('user_type')
                            ->label('User Type')
                            ->content(fn () => $isVip ? 'VIP User (Conference will be published automatically)' : 'Regular User (Requires approval)'),

                        Forms\Components\Placeholder::make('status_info')
                            ->label('Publishing Status')
                            ->content(fn () => $isVip ? 'Takes effect immediately after publishing' : 'Waiting for admin approval after publishing'),
                    ])
                    ->columns(3),
        ];
    }

    public static function table(Table $table): Table
    {
        // Add model query modifier to preload related data
        $table->modifyQueryUsing(function (Builder $query) {
            return $query->with(['country', 'categories']);
        });
        
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('pic')
                    ->label('Image')
                    ->circular()
                    ->size(50)
                    ->defaultImageUrl(asset('images/default-event.png')),

                Tables\Columns\TextColumn::make('title')
                    ->label('Conference Title')
                    ->searchable()
                    ->sortable()
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 50 ? $state : null;
                    }),

                Tables\Columns\TextColumn::make('event')
                    ->label('Short Name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('country.venue')
                    ->label('Venue')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('city')
                    ->label('City')
                    ->toggleable(),

                Tables\Columns\TextColumn::make('start_date')
                    ->label('Start Time')
                    ->formatStateUsing(fn ($state) => $state ? date('Y-m-d H:i', $state) : '-')
                    ->sortable(),

                Tables\Columns\TextColumn::make('end_date')
                    ->label('End Time')
                    ->formatStateUsing(fn ($state) => $state ? date('Y-m-d H:i', $state) : '-')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->formatStateUsing(fn (ConferenceStatus $state): string => $state->label())
                    ->badge()
                    ->color(fn (ConferenceStatus $state): string => $state->color())
                    ->sortable(),

                Tables\Columns\TextColumn::make('addtime')
                    ->label('Publish Time')
                    ->formatStateUsing(fn ($state) => $state ? date('Y-m-d H:i', $state) : '-')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        ConferenceStatus::Draft->value => ConferenceStatus::Draft->label(),
                        ConferenceStatus::Pending->value => ConferenceStatus::Pending->label(),
                        ConferenceStatus::Published->value => ConferenceStatus::Published->label(),
                        ConferenceStatus::Rejected->value => ConferenceStatus::Rejected->label(),
                    ]),

                Tables\Filters\SelectFilter::make('venue')
                    ->label('Venue')
                    ->relationship('country', 'venue'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation()
                    ->modalHeading('Delete Conference')
                    ->modalDescription('Are you sure you want to delete this conference? This action cannot be undone.'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->requiresConfirmation(),
                ]),
            ])
            ->defaultSort('addtime', 'desc')
            ->emptyStateHeading('No conferences have been published yet')
            ->emptyStateDescription('Click the "Create Conference" button to publish your first conference')
            ->emptyStateIcon('heroicon-o-calendar-days');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEvents::route('/'),
            'create' => Pages\CreateEvent::route('/create'),
            'edit' => Pages\EditEvent::route('/{record}/edit'),
            'view' => Pages\ViewEvent::route('/{record}'),
        ];
    }

    /**
     * Limit query scope: only show current user's conferences
     */
    public static function getEloquentQuery(): Builder
    {
        $member = static::getCurrentMember();

        return parent::getEloquentQuery()
            ->where('uid', $member?->id ?? 0)
            ->where('creator_type', 'member')
            ->with(['user', 'country', 'categories']);
    }

    /**
     * Check if user can view the record
     */
    public static function canView($record): bool
    {
        $member = static::getCurrentMember();
        return $member && $record->uid === $member->id;
    }

    /**
     * Check if user can edit the record
     */
    public static function canEdit($record): bool
    {
        $member = static::getCurrentMember();
        return $member && $record->uid === $member->id;
    }

    /**
     * Check if user can delete the record
     */
    public static function canDelete($record): bool
    {
        $member = static::getCurrentMember();
        return $member && $record->uid === $member->id;
    }

    /**
     * Check if user can create a record
     */
    public static function canCreate(): bool
    {
        return static::getCurrentMember() !== null;
    }

    /**
     * Get navigation badge
     */
    public static function getNavigationBadge(): ?string
    {
        $member = static::getCurrentMember();
        if (!$member) {
            return null;
        }

        $count = static::getModel()::where('uid', $member->id)->where('creator_type', 'member')->count();
        return $count > 0 ? (string) $count : null;
    }

    /**
     * Get navigation badge color
     */
    public static function getNavigationBadgeColor(): ?string
    {
        return 'primary';
    }
}
