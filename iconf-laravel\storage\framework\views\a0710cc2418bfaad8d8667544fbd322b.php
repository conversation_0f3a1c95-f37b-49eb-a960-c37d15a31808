<?php $__env->startSection('content'); ?>
<!-- Full-Width Hero Section -->
<section class="hero-section relative w-full bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900 text-white overflow-hidden">
    <!-- Geometric Background Elements -->
    <div class="absolute inset-0">
        <!-- Curved Lines -->
        <svg class="absolute top-0 left-0 w-full h-full opacity-10" viewBox="0 0 1200 800" preserveAspectRatio="none">
            <path d="M0,200 Q300,100 600,200 T1200,200 L1200,0 L0,0 Z" fill="rgba(59, 130, 246, 0.3)"/>
            <path d="M0,400 Q400,300 800,400 T1200,400 L1200,200 L0,200 Z" fill="rgba(37, 99, 235, 0.2)"/>
            <path d="M0,600 Q500,500 1000,600 T1200,600 L1200,400 L0,400 Z" fill="rgba(29, 78, 216, 0.1)"/>
        </svg>

        <!-- Floating Circles - Subtle Background Decoration -->
        <div class="absolute top-20 left-10 w-24 h-24 bg-blue-400 rounded-full opacity-5 animate-pulse"></div>
        <div class="absolute top-40 right-20 w-20 h-20 bg-cyan-400 rounded-full opacity-8 animate-pulse" style="animation-delay: 1s;"></div>
        <div class="absolute bottom-20 left-1/4 w-16 h-16 bg-indigo-400 rounded-full opacity-6 animate-pulse" style="animation-delay: 2s;"></div>
        <div class="absolute bottom-40 right-1/3 w-12 h-12 bg-blue-300 rounded-full opacity-7 animate-pulse" style="animation-delay: 0.5s;"></div>

        <!-- Grid Pattern -->
        <div class="absolute inset-0 opacity-5" style="background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0); background-size: 40px 40px;"></div>

        <!-- Diagonal Lines -->
        <div class="absolute inset-0 opacity-5">
            <div class="absolute top-0 left-0 w-full h-full transform rotate-12 bg-gradient-to-r from-transparent via-white to-transparent" style="background: repeating-linear-gradient(90deg, transparent, transparent 98px, rgba(255,255,255,0.1) 100px);"></div>
        </div>
    </div>

    <!-- Content -->
    <div class="relative z-10 container mx-auto px-4 py-16 md:py-20">
        <div class="text-center">
            <!-- Breadcrumb -->
            <div class="mb-6 text-sm font-medium text-blue-200">
                <a href="/" class="hover:text-white transition-colors">Home</a>
                <span class="mx-2">•</span>
                <a href="/conferences" class="hover:text-white transition-colors">Conferences</a>
                <span class="mx-2">•</span>
                <span class="text-white"><?php echo e($category->name); ?></span>
            </div>

            <!-- Category Title -->
            <h1 class="text-3xl md:text-4xl lg:text-5xl font-heading font-extrabold tracking-tight mb-4 leading-tight text-white drop-shadow-lg">
                <?php echo e($category->name); ?> Conferences
            </h1>

            <!-- Category Description -->
            <?php if($category->description): ?>
            <h2 class="text-lg md:text-xl font-light text-blue-100 mb-8 max-w-4xl mx-auto leading-relaxed">
                <?php echo e($category->description); ?>

            </h2>
            <?php endif; ?>

            <!-- Statistics Cards -->
            <div class="flex flex-wrap justify-center items-center gap-6 mb-8">
                <div class="flex items-center gap-3 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
                    <div class="w-8 h-8 bg-cyan-400/30 rounded-full flex items-center justify-center">
                        <i class="fas fa-calendar-alt text-cyan-300 text-sm"></i>
                    </div>
                    <span class="text-sm font-medium"><?php echo e(count($conferences) + count($featuredConferences)); ?> Conferences</span>
                </div>

                <div class="flex items-center gap-3 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
                    <div class="w-8 h-8 bg-emerald-400/30 rounded-full flex items-center justify-center">
                        <i class="fas fa-star text-emerald-300 text-sm"></i>
                    </div>
                    <span class="text-sm font-medium"><?php echo e(count($featuredConferences)); ?> Featured</span>
                </div>

                <div class="flex items-center gap-3 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
                    <div class="w-8 h-8 bg-amber-400/30 rounded-full flex items-center justify-center">
                        <i class="fas fa-globe text-amber-300 text-sm"></i>
                    </div>
                    <span class="text-sm font-medium">Global Events</span>
                </div>
            </div>

            <!-- Category Tags -->
            <div class="flex flex-wrap justify-center gap-2">
                <div class="category-tags conference-hero-tags">
                    <div class="flex flex-wrap gap-2">
                        <a href="<?php echo e(route('categories.lists', $category->url)); ?>"
                           class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/15 text-white border border-white/25 backdrop-blur-sm hover:bg-white/25 transition-all duration-300">
                            <?php echo $category->name; ?>

                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Wave -->
    <div class="absolute bottom-0 left-0 w-full overflow-hidden">
        <svg class="relative block w-full h-16" viewBox="0 0 1200 120" preserveAspectRatio="none">
            <path d="M0,60 Q300,0 600,60 T1200,60 L1200,120 L0,120 Z" fill="rgba(248, 250, 252, 1)"/>
        </svg>
    </div>
</section>

<!-- Main Content Area -->
<div class="container mx-auto px-4 py-12 md:py-16">
    <div class="grid grid-cols-1 lg:grid-cols-3 lg:gap-12">
        <!-- Left Column: Main Content -->
        <div class="lg:col-span-2 space-y-12">
            <!-- Featured Conferences Section -->
            <?php if(count($featuredConferences) > 0): ?>
            <section>
                <?php if (isset($component)) { $__componentOriginal41e61dba6d3b8ac74ec5b3e728290a0a = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal41e61dba6d3b8ac74ec5b3e728290a0a = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.conference.creative-grid','data' => ['conferences' => $featuredConferences,'title' => 'Featured Conferences','subtitle' => 'Discover high-quality academic conferences in '.e($category->name).'','type' => 'featured']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('conference.creative-grid'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['conferences' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($featuredConferences),'title' => 'Featured Conferences','subtitle' => 'Discover high-quality academic conferences in '.e($category->name).'','type' => 'featured']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal41e61dba6d3b8ac74ec5b3e728290a0a)): ?>
<?php $attributes = $__attributesOriginal41e61dba6d3b8ac74ec5b3e728290a0a; ?>
<?php unset($__attributesOriginal41e61dba6d3b8ac74ec5b3e728290a0a); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal41e61dba6d3b8ac74ec5b3e728290a0a)): ?>
<?php $component = $__componentOriginal41e61dba6d3b8ac74ec5b3e728290a0a; ?>
<?php unset($__componentOriginal41e61dba6d3b8ac74ec5b3e728290a0a); ?>
<?php endif; ?>
            </section>
            <?php endif; ?>

            <!-- All Conferences Section -->
            <section>
                <div class="bg-white rounded-xl shadow-lg border border-slate-200/50 overflow-hidden">
                    <div class="bg-gradient-to-r from-slate-50 to-blue-50 p-6 border-b border-slate-200">
                        <h2 class="text-3xl font-bold text-slate-800 mb-2 border-l-4 border-purple-500 pl-4">
                            All <?php echo e($category->name); ?> Conferences
                        </h2>
                        <p class="text-slate-600">Browse all conferences in this category</p>
                    </div>

                    <?php if(count($conferences) > 0): ?>
                    <div class="p-6">
                        <div class="space-y-8">
                            <?php $__currentLoopData = $conferences; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $conference): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <article class="group relative bg-gradient-to-r from-white to-slate-50 rounded-xl border border-slate-200 p-6 hover:shadow-xl hover:border-purple-300 transition-all duration-300 hover:-translate-y-1">
                                <!-- Conference Header -->
                                <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4 mb-4">
                                    <div class="flex-1">
                                        <h3 class="text-2xl font-bold text-slate-800 mb-2 group-hover:text-purple-600 transition-colors">
                                            <a href="<?php echo e(route('conference.show', $conference->url)); ?>" class="hover:underline">
                                                <?php echo e($conference->event); ?>

                                            </a>
                                        </h3>
                                        <p class="text-lg text-slate-600 leading-relaxed"><?php echo e($conference->title); ?></p>
                                    </div>

                                    <!-- Status Badge -->
                                    <div class="flex items-center gap-2">
                                        <?php if($conference->end_date > time()): ?>
                                            <span class="px-3 py-1 bg-green-100 text-green-800 text-sm font-semibold rounded-full border border-green-200">
                                                <i class="fas fa-clock mr-1"></i>Upcoming
                                            </span>
                                        <?php else: ?>
                                            <span class="px-3 py-1 bg-gray-100 text-gray-600 text-sm font-semibold rounded-full border border-gray-200">
                                                <i class="fas fa-check-circle mr-1"></i>Completed
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Conference Details -->
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                    <div class="flex items-center gap-3 text-slate-600">
                                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-calendar-alt text-blue-600"></i>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-slate-500">Conference Dates</p>
                                            <p class="font-semibold">
                                                <?php echo e(date('M j, Y', $conference->start_date)); ?> - <?php echo e(date('M j, Y', $conference->end_date)); ?>

                                            </p>
                                        </div>
                                    </div>

                                    <div class="flex items-center gap-3 text-slate-600">
                                        <div class="w-10 h-10 bg-emerald-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-map-marker-alt text-emerald-600"></i>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-slate-500">Location</p>
                                            <p class="font-semibold">
                                                <?php echo e($conference->city); ?>, <?php echo e($conference->country->venue ?? ''); ?>

                                                <?php if($conference->hotel): ?>
                                                    <br><span class="text-sm text-slate-500"><?php echo e($conference->hotel); ?></span>
                                                <?php endif; ?>
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Conference Summary -->
                                <?php if($conference->summary): ?>
                                <div class="mb-4">
                                    <p class="text-slate-600 leading-relaxed line-clamp-2"><?php echo e($conference->summary); ?></p>
                                </div>
                                <?php endif; ?>

                                <!-- Action Buttons -->
                                <div class="flex items-center justify-between pt-4 border-t border-slate-200">
                                    <div class="flex items-center gap-4">
                                        <?php if (isset($component)) { $__componentOriginal4687d8aa834a263e8fd0541ffa73028c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4687d8aa834a263e8fd0541ffa73028c = $attributes; } ?>
<?php $component = App\View\Components\Category\ConferenceTags::resolve(['conference' => $conference,'showTitle' => false,'class' => 'conference-list-tags'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('category.conference-tags'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Category\ConferenceTags::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4687d8aa834a263e8fd0541ffa73028c)): ?>
<?php $attributes = $__attributesOriginal4687d8aa834a263e8fd0541ffa73028c; ?>
<?php unset($__attributesOriginal4687d8aa834a263e8fd0541ffa73028c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4687d8aa834a263e8fd0541ffa73028c)): ?>
<?php $component = $__componentOriginal4687d8aa834a263e8fd0541ffa73028c; ?>
<?php unset($__componentOriginal4687d8aa834a263e8fd0541ffa73028c); ?>
<?php endif; ?>
                                    </div>

                                    <div class="flex items-center gap-3">
                                        <?php if($conference->sub_date && $conference->sub_date > time()): ?>
                                        <span class="text-sm text-amber-600 font-medium">
                                            <i class="fas fa-clock mr-1"></i>
                                            Deadline: <?php echo e(date('M j, Y', $conference->sub_date)); ?>

                                        </span>
                                        <?php endif; ?>

                                        <a href="<?php echo e(route('conference.show', $conference->url)); ?>"
                                           class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300 hover:shadow-lg hover:scale-105">
                                            Learn More
                                            <i class="fas fa-arrow-right ml-2"></i>
                                        </a>
                                    </div>
                                </div>

                                <!-- Subtle Geometric Background Elements -->
                                <div class="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-purple-50 to-blue-50 rounded-full transform translate-x-8 -translate-y-8 opacity-20 group-hover:opacity-30 transition-all duration-300"></div>
                                <div class="absolute bottom-0 left-0 w-12 h-12 bg-gradient-to-tr from-blue-25 to-purple-25 transform rotate-45 -translate-x-6 translate-y-6 opacity-15 group-hover:opacity-25 transition-all duration-300"></div>
                            </article>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>

                        <!-- Enhanced Pagination -->
                        <div class="mt-12 flex justify-center">
                            <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-4">
                                <?php echo e($conferences->links()); ?>

                            </div>
                        </div>
                    </div>
                    <?php else: ?>
                    <div class="p-12 text-center">
                        <div class="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-calendar-times text-slate-400 text-3xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-slate-600 mb-2">No Conferences Found</h3>
                        <p class="text-slate-500">There are currently no conferences available in this category.</p>
                        <a href="/conferences" class="inline-flex items-center mt-4 px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-search mr-2"></i>
                            Browse All Conferences
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </section>
        </div>

        <!-- Right Column: Sticky Sidebar -->
        <aside class="lg:col-span-1 mt-12 lg:mt-0">
            <div class="sticky top-28 space-y-6">

                <!-- Category Navigation -->
                <div class="bg-white rounded-xl shadow-lg border border-slate-200/50 overflow-hidden">
                    <div class="bg-gradient-to-r from-purple-500 to-blue-600 p-4">
                        <h3 class="text-lg font-bold text-white flex items-center gap-2">
                            <i class="fas fa-list-alt"></i>
                            Category Navigation
                        </h3>
                    </div>
                    <div class="p-4">
                        <?php if (isset($component)) { $__componentOriginal12657cf99d9d294214e6c1d171872452 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12657cf99d9d294214e6c1d171872452 = $attributes; } ?>
<?php $component = App\View\Components\Category\Accordion::resolve(['active' => $category->url,'activeType' => 'url','title' => '','class' => ''] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('category.accordion'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Category\Accordion::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12657cf99d9d294214e6c1d171872452)): ?>
<?php $attributes = $__attributesOriginal12657cf99d9d294214e6c1d171872452; ?>
<?php unset($__attributesOriginal12657cf99d9d294214e6c1d171872452); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12657cf99d9d294214e6c1d171872452)): ?>
<?php $component = $__componentOriginal12657cf99d9d294214e6c1d171872452; ?>
<?php unset($__componentOriginal12657cf99d9d294214e6c1d171872452); ?>
<?php endif; ?>
                    </div>
                </div>



                <!-- 广告区块 -->
                <?php if (isset($component)) { $__componentOriginale2f88d9d611696248221bc470383ba2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale2f88d9d611696248221bc470383ba2c = $attributes; } ?>
<?php $component = App\View\Components\Advertisement::resolve(['categoryId' => $category->id] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('advertisement'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Advertisement::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale2f88d9d611696248221bc470383ba2c)): ?>
<?php $attributes = $__attributesOriginale2f88d9d611696248221bc470383ba2c; ?>
<?php unset($__attributesOriginale2f88d9d611696248221bc470383ba2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale2f88d9d611696248221bc470383ba2c)): ?>
<?php $component = $__componentOriginale2f88d9d611696248221bc470383ba2c; ?>
<?php unset($__componentOriginale2f88d9d611696248221bc470383ba2c); ?>
<?php endif; ?>




            </div>
        </aside>
    </div>
</div>

<!-- Custom Styles -->
<style>
/* Ensure full-width hero section */
body {
    margin: 0;
    padding: 0;
}

main {
    margin: 0;
    padding: 0;
}

/* Hero section full-width styling */
.hero-section {
    margin-left: calc(-50vw + 50%);
    margin-right: calc(-50vw + 50%);
    width: 100vw;
    position: relative;
}

/* Hero tags styling */
.conference-hero-tags .category-tags {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
}

.conference-hero-tags .category-tags a {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.25);
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    text-decoration: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.conference-hero-tags .category-tags a:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* List tags styling */
.conference-list-tags .category-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.conference-list-tags .category-tags a {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    background: #f1f5f9;
    color: #475569;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    border: 1px solid #e2e8f0;
}

.conference-list-tags .category-tags a:hover {
    background: #e2e8f0;
    color: #7c3aed;
    transform: translateY(-1px);
    border-color: #7c3aed;
}

/* Enhanced animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.animate-float {
    animation: float 6s ease-in-out infinite;
}

/* Subtle geometric decorations */
.hero-section .absolute.rounded-full {
    pointer-events: none;
    filter: blur(0.5px);
}

.hero-section .absolute.rounded-full:hover {
    filter: blur(0px);
    transition: filter 0.3s ease;
}

/* Conference card decorations */
.conference-card .absolute.rounded-full {
    pointer-events: none;
    z-index: 0;
}

.conference-card:hover .absolute.rounded-full {
    transform: scale(1.1);
    transition: transform 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .hero-section {
        margin-left: 0;
        margin-right: 0;
        width: 100%;
    }
}

/* Print styles */
@media print {
    .sticky {
        position: static !important;
    }

    .shadow-lg, .shadow-xl {
        box-shadow: none !important;
    }

    .hero-section {
        margin-left: 0;
        margin-right: 0;
        width: 100%;
    }
}
</style>

<!-- Conference Cards JavaScript -->
<script src="<?php echo e(asset('js/conference-cards.js')); ?>"></script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\phpEnv\www\iconf_org_by_laravel\iconf-laravel\resources\views/frontend/home/<USER>/ ?>