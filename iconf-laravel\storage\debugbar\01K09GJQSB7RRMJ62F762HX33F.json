{"__meta": {"id": "01K09GJQSB7RRMJ62F762HX33F", "datetime": "2025-07-16 11:40:38", "utime": **********.059845, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.832991, "end": **********.059853, "duration": 0.22686219215393066, "duration_str": "227ms", "measures": [{"label": "Booting", "start": **********.832991, "relative_start": 0, "end": **********.99572, "relative_end": **********.99572, "duration": 0.*****************, "duration_str": "163ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.995728, "relative_start": 0.*****************, "end": **********.059854, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "64.13ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.002811, "relative_start": 0.*****************, "end": **********.003763, "relative_end": **********.003763, "duration": 0.0009520053863525391, "duration_str": "952μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: livewire.conference-filter", "start": **********.052138, "relative_start": 0.****************, "end": **********.052138, "relative_end": **********.052138, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.053959, "relative_start": 0.*****************, "end": **********.053959, "relative_end": **********.053959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.054259, "relative_start": 0.********70324707, "end": **********.054259, "relative_end": **********.054259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.054481, "relative_start": 0.22149014472961426, "end": **********.054481, "relative_end": **********.054481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.054669, "relative_start": 0.2216780185699463, "end": **********.054669, "relative_end": **********.054669, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.05486, "relative_start": 0.22186923027038574, "end": **********.05486, "relative_end": **********.05486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.055045, "relative_start": 0.22205400466918945, "end": **********.055045, "relative_end": **********.055045, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.055247, "relative_start": 0.22225618362426758, "end": **********.055247, "relative_end": **********.055247, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.055437, "relative_start": 0.22244620323181152, "end": **********.055437, "relative_end": **********.055437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.055617, "relative_start": 0.2226262092590332, "end": **********.055617, "relative_end": **********.055617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.05581, "relative_start": 0.22281908988952637, "end": **********.05581, "relative_end": **********.05581, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire::tailwind", "start": **********.056157, "relative_start": 0.22316622734069824, "end": **********.056157, "relative_end": **********.056157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.058546, "relative_start": 0.2255551815032959, "end": **********.059026, "relative_end": **********.059026, "duration": 0.0004799365997314453, "duration_str": "480μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 44477984, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.4.2", "Environment": "local", "Debug Mode": "Enabled", "URL": "iconf.lv", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 12, "nb_templates": 12, "templates": [{"name": "livewire.conference-filter", "param_count": null, "params": [], "start": **********.052126, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/livewire/conference-filter.blade.phplivewire.conference-filter", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Flivewire%2Fconference-filter.blade.php&line=1", "ajax": false, "filename": "conference-filter.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.053951, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.054252, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.054475, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.054663, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.054855, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.055039, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.055242, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.055431, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.055612, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.055804, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "livewire::tailwind", "param_count": null, "params": [], "start": **********.05615, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination/views/tailwind.blade.phplivewire::tailwind", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2Fviews%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}}]}, "queries": {"count": 12, "nb_statements": 11, "nb_visible_statements": 12, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.025449999999999997, "accumulated_duration_str": "25.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.007937, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'n81JYPLEP0cENw7ZqI8BBaufrhZyV3CgHX5ObznX' limit 1", "type": "query", "params": [], "bindings": ["n81JYPLEP0cENw7ZqI8BBaufrhZyV3CgHX5ObznX"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.0084329, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 0, "width_percent": 3.497}, {"sql": "select * from `category` where `fid` = 142 order by `listorder` asc", "type": "query", "params": [], "bindings": [142], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.0157852, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ConferenceFilter.php:66", "source": {"index": 15, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FLivewire%2FConferenceFilter.php&line=66", "ajax": false, "filename": "ConferenceFilter.php", "line": "66"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 3.497, "width_percent": 1.061}, {"sql": "select * from `category` where `category`.`id` = 142 limit 1", "type": "query", "params": [], "bindings": [142], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 69}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.0166361, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "ConferenceFilter.php:69", "source": {"index": 20, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FLivewire%2FConferenceFilter.php&line=69", "ajax": false, "filename": "ConferenceFilter.php", "line": "69"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 4.558, "width_percent": 0.354}, {"sql": "select * from `category` where `url` = 'environment' limit 1", "type": "query", "params": [], "bindings": ["environment"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 177}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.018092, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "ConferenceFilter.php:177", "source": {"index": 16, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 177}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FLivewire%2FConferenceFilter.php&line=177", "ajax": false, "filename": "ConferenceFilter.php", "line": "177"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 4.912, "width_percent": 0.629}, {"sql": "select `id` from `category` where `fid` = 142", "type": "query", "params": [], "bindings": [142], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 182}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.018599, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "ConferenceFilter.php:182", "source": {"index": 14, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 182}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FLivewire%2FConferenceFilter.php&line=182", "ajax": false, "filename": "ConferenceFilter.php", "line": "182"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 5.54, "width_percent": 0.354}, {"sql": "select count(*) as aggregate from `event` inner join `list` on `event`.`id` = `list`.`eid` where `status` = 1 and `list`.`cid` in (142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157)", "type": "query", "params": [], "bindings": [1, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 231}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.019294, "duration": 0.0044, "duration_str": "4.4ms", "memory": 0, "memory_str": null, "filename": "ConferenceFilter.php:231", "source": {"index": 16, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 231}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FLivewire%2FConferenceFilter.php&line=231", "ajax": false, "filename": "ConferenceFilter.php", "line": "231"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 5.894, "width_percent": 17.289}, {"sql": "select distinct `event`.* from `event` inner join `list` on `event`.`id` = `list`.`eid` where `status` = 1 and `list`.`cid` in (142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157) order by `start_date` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [1, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 231}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.024071, "duration": 0.01886, "duration_str": "18.86ms", "memory": 0, "memory_str": null, "filename": "ConferenceFilter.php:231", "source": {"index": 16, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 231}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FLivewire%2FConferenceFilter.php&line=231", "ajax": false, "filename": "ConferenceFilter.php", "line": "231"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 23.183, "width_percent": 74.106}, {"sql": "select * from `country` where `country`.`id` in (12, 14, 15, 100)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 231}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.043941, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "ConferenceFilter.php:231", "source": {"index": 21, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 231}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FLivewire%2FConferenceFilter.php&line=231", "ajax": false, "filename": "ConferenceFilter.php", "line": "231"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 97.289, "width_percent": 0.629}, {"sql": "select `category`.*, `list`.`eid` as `pivot_eid`, `list`.`cid` as `pivot_cid` from `category` inner join `list` on `category`.`id` = `list`.`cid` where `list`.`eid` in (5196, 5393, 5394, 5409, 5410, 5440, 5453, 5454, 5491, 5492)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 231}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.045765, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ConferenceFilter.php:231", "source": {"index": 20, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 231}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FLivewire%2FConferenceFilter.php&line=231", "ajax": false, "filename": "ConferenceFilter.php", "line": "231"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 97.917, "width_percent": 1.297}, {"sql": "select * from `category` where `fid` = 0 order by `listorder` asc", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 234}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.047353, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "ConferenceFilter.php:234", "source": {"index": 15, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 234}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FLivewire%2FConferenceFilter.php&line=234", "ajax": false, "filename": "ConferenceFilter.php", "line": "234"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 99.214, "width_percent": 0.472}, {"sql": "select * from `country` where `fid` = 0 order by `listorder` asc", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 235}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.047809, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "ConferenceFilter.php:235", "source": {"index": 15, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 235}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FLivewire%2FConferenceFilter.php&line=235", "ajax": false, "filename": "ConferenceFilter.php", "line": "235"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 99.686, "width_percent": 0.314}]}, "models": {"data": {"App\\Models\\Category": {"value": 56, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Event": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "App\\Models\\Country": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FCountry.php&line=1", "ajax": false, "filename": "Country.php", "line": "?"}}}, "count": 76, "is_counter": true}, "livewire": {"data": {"conference-filter #jdtPTVm0Q7J5gccW4FRp": "array:4 [\n  \"data\" => array:9 [\n    \"keyword\" => \"\"\n    \"selectedCategory\" => \"environment\"\n    \"selectedCountry\" => null\n    \"selectedYear\" => null\n    \"selectedTopCategoryId\" => 142\n    \"selectedContinentId\" => null\n    \"subCategories\" => Illuminate\\Database\\Eloquent\\Collection {#2851\n      #items: array:15 [\n        0 => App\\Models\\Category {#2741\n          #connection: \"mysql\"\n          #table: \"category\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 143\n            \"listorder\" => 143\n            \"fid\" => 142\n            \"name\" => \"Agriculture\"\n            \"url\" => \"agriculture\"\n          ]\n          #original: array:5 [\n            \"id\" => 143\n            \"listorder\" => 143\n            \"fid\" => 142\n            \"name\" => \"Agriculture\"\n            \"url\" => \"agriculture\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:2 [\n            \"fid\" => \"integer\"\n            \"listorder\" => \"integer\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:4 [\n            0 => \"fid\"\n            1 => \"name\"\n            2 => \"url\"\n            3 => \"listorder\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        1 => App\\Models\\Category {#2775\n          #connection: \"mysql\"\n          #table: \"category\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 144\n            \"listorder\" => 144\n            \"fid\" => 142\n            \"name\" => \"Chemistry\"\n            \"url\" => \"chemistry_1\"\n          ]\n          #original: array:5 [\n            \"id\" => 144\n            \"listorder\" => 144\n            \"fid\" => 142\n            \"name\" => \"Chemistry\"\n            \"url\" => \"chemistry_1\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:2 [\n            \"fid\" => \"integer\"\n            \"listorder\" => \"integer\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:4 [\n            0 => \"fid\"\n            1 => \"name\"\n            2 => \"url\"\n            3 => \"listorder\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        2 => App\\Models\\Category {#2774\n          #connection: \"mysql\"\n          #table: \"category\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 145\n            \"listorder\" => 145\n            \"fid\" => 142\n            \"name\" => \"Energy and Resources\"\n            \"url\" => \"energy_and_resources\"\n          ]\n          #original: array:5 [\n            \"id\" => 145\n            \"listorder\" => 145\n            \"fid\" => 142\n            \"name\" => \"Energy and Resources\"\n            \"url\" => \"energy_and_resources\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:2 [\n            \"fid\" => \"integer\"\n            \"listorder\" => \"integer\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:4 [\n            0 => \"fid\"\n            1 => \"name\"\n            2 => \"url\"\n            3 => \"listorder\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        3 => App\\Models\\Category {#2773\n          #connection: \"mysql\"\n          #table: \"category\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 146\n            \"listorder\" => 146\n            \"fid\" => 142\n            \"name\" => \"Environment Sciences and Technology\"\n            \"url\" => \"environment_sciences_and_technology\"\n          ]\n          #original: array:5 [\n            \"id\" => 146\n            \"listorder\" => 146\n            \"fid\" => 142\n            \"name\" => \"Environment Sciences and Technology\"\n            \"url\" => \"environment_sciences_and_technology\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:2 [\n            \"fid\" => \"integer\"\n            \"listorder\" => \"integer\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:4 [\n            0 => \"fid\"\n            1 => \"name\"\n            2 => \"url\"\n            3 => \"listorder\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        4 => App\\Models\\Category {#2772\n          #connection: \"mysql\"\n          #table: \"category\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 147\n            \"listorder\" => 147\n            \"fid\" => 142\n            \"name\" => \"Food Sciences\"\n            \"url\" => \"food_sciences\"\n          ]\n          #original: array:5 [\n            \"id\" => 147\n            \"listorder\" => 147\n            \"fid\" => 142\n            \"name\" => \"Food Sciences\"\n            \"url\" => \"food_sciences\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:2 [\n            \"fid\" => \"integer\"\n            \"listorder\" => \"integer\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:4 [\n            0 => \"fid\"\n            1 => \"name\"\n            2 => \"url\"\n            3 => \"listorder\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        5 => App\\Models\\Category {#2771\n          #connection: \"mysql\"\n          #table: \"category\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 148\n            \"listorder\" => 148\n            \"fid\" => 142\n            \"name\" => \"Geophysics\"\n            \"url\" => \"geophysics\"\n          ]\n          #original: array:5 [\n            \"id\" => 148\n            \"listorder\" => 148\n            \"fid\" => 142\n            \"name\" => \"Geophysics\"\n            \"url\" => \"geophysics\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:2 [\n            \"fid\" => \"integer\"\n            \"listorder\" => \"integer\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:4 [\n            0 => \"fid\"\n            1 => \"name\"\n            2 => \"url\"\n            3 => \"listorder\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        6 => App\\Models\\Category {#2770\n          #connection: \"mysql\"\n          #table: \"category\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 149\n            \"listorder\" => 149\n            \"fid\" => 142\n            \"name\" => \"Biosciences and Technologies\"\n            \"url\" => \"biosciences_and_technologies\"\n          ]\n          #original: array:5 [\n            \"id\" => 149\n            \"listorder\" => 149\n            \"fid\" => 142\n            \"name\" => \"Biosciences and Technologies\"\n            \"url\" => \"biosciences_and_technologies\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:2 [\n            \"fid\" => \"integer\"\n            \"listorder\" => \"integer\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:4 [\n            0 => \"fid\"\n            1 => \"name\"\n            2 => \"url\"\n            3 => \"listorder\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        7 => App\\Models\\Category {#2769\n          #connection: \"mysql\"\n          #table: \"category\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 150\n            \"listorder\" => 150\n            \"fid\" => 142\n            \"name\" => \"Civil, architecture and Construction\"\n            \"url\" => \"civil,_architecture_and_construction\"\n          ]\n          #original: array:5 [\n            \"id\" => 150\n            \"listorder\" => 150\n            \"fid\" => 142\n            \"name\" => \"Civil, architecture and Construction\"\n            \"url\" => \"civil,_architecture_and_construction\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:2 [\n            \"fid\" => \"integer\"\n            \"listorder\" => \"integer\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:4 [\n            0 => \"fid\"\n            1 => \"name\"\n            2 => \"url\"\n            3 => \"listorder\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        8 => App\\Models\\Category {#2768\n          #connection: \"mysql\"\n          #table: \"category\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 151\n            \"listorder\" => 151\n            \"fid\" => 142\n            \"name\" => \"Earth Science\"\n            \"url\" => \"earth_science\"\n          ]\n          #original: array:5 [\n            \"id\" => 151\n            \"listorder\" => 151\n            \"fid\" => 142\n            \"name\" => \"Earth Science\"\n            \"url\" => \"earth_science\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:2 [\n            \"fid\" => \"integer\"\n            \"listorder\" => \"integer\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:4 [\n            0 => \"fid\"\n            1 => \"name\"\n            2 => \"url\"\n            3 => \"listorder\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        9 => App\\Models\\Category {#2767\n          #connection: \"mysql\"\n          #table: \"category\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 152\n            \"listorder\" => 152\n            \"fid\" => 142\n            \"name\" => \"Forestry\"\n            \"url\" => \"forestry\"\n          ]\n          #original: array:5 [\n            \"id\" => 152\n            \"listorder\" => 152\n            \"fid\" => 142\n            \"name\" => \"Forestry\"\n            \"url\" => \"forestry\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:2 [\n            \"fid\" => \"integer\"\n            \"listorder\" => \"integer\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:4 [\n            0 => \"fid\"\n            1 => \"name\"\n            2 => \"url\"\n            3 => \"listorder\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        10 => App\\Models\\Category {#2766\n          #connection: \"mysql\"\n          #table: \"category\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 153\n            \"listorder\" => 153\n            \"fid\" => 142\n            \"name\" => \"Meteorology\"\n            \"url\" => \"meteorology\"\n          ]\n          #original: array:5 [\n            \"id\" => 153\n            \"listorder\" => 153\n            \"fid\" => 142\n            \"name\" => \"Meteorology\"\n            \"url\" => \"meteorology\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:2 [\n            \"fid\" => \"integer\"\n            \"listorder\" => \"integer\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:4 [\n            0 => \"fid\"\n            1 => \"name\"\n            2 => \"url\"\n            3 => \"listorder\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        11 => App\\Models\\Category {#2765\n          #connection: \"mysql\"\n          #table: \"category\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 154\n            \"listorder\" => 154\n            \"fid\" => 142\n            \"name\" => \"Oceanography\"\n            \"url\" => \"oceanography\"\n          ]\n          #original: array:5 [\n            \"id\" => 154\n            \"listorder\" => 154\n            \"fid\" => 142\n            \"name\" => \"Oceanography\"\n            \"url\" => \"oceanography\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:2 [\n            \"fid\" => \"integer\"\n            \"listorder\" => \"integer\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:4 [\n            0 => \"fid\"\n            1 => \"name\"\n            2 => \"url\"\n            3 => \"listorder\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        12 => App\\Models\\Category {#2764\n          #connection: \"mysql\"\n          #table: \"category\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 155\n            \"listorder\" => 155\n            \"fid\" => 142\n            \"name\" => \"GIS\"\n            \"url\" => \"gis\"\n          ]\n          #original: array:5 [\n            \"id\" => 155\n            \"listorder\" => 155\n            \"fid\" => 142\n            \"name\" => \"GIS\"\n            \"url\" => \"gis\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:2 [\n            \"fid\" => \"integer\"\n            \"listorder\" => \"integer\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:4 [\n            0 => \"fid\"\n            1 => \"name\"\n            2 => \"url\"\n            3 => \"listorder\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        13 => App\\Models\\Category {#2763\n          #connection: \"mysql\"\n          #table: \"category\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 156\n            \"listorder\" => 156\n            \"fid\" => 142\n            \"name\" => \"Soil Science\"\n            \"url\" => \"soil_science\"\n          ]\n          #original: array:5 [\n            \"id\" => 156\n            \"listorder\" => 156\n            \"fid\" => 142\n            \"name\" => \"Soil Science\"\n            \"url\" => \"soil_science\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:2 [\n            \"fid\" => \"integer\"\n            \"listorder\" => \"integer\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:4 [\n            0 => \"fid\"\n            1 => \"name\"\n            2 => \"url\"\n            3 => \"listorder\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        14 => App\\Models\\Category {#2762\n          #connection: \"mysql\"\n          #table: \"category\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 157\n            \"listorder\" => 157\n            \"fid\" => 142\n            \"name\" => \"sustainable development\"\n            \"url\" => \"sustainable_development\"\n          ]\n          #original: array:5 [\n            \"id\" => 157\n            \"listorder\" => 157\n            \"fid\" => 142\n            \"name\" => \"sustainable development\"\n            \"url\" => \"sustainable_development\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:2 [\n            \"fid\" => \"integer\"\n            \"listorder\" => \"integer\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:4 [\n            0 => \"fid\"\n            1 => \"name\"\n            2 => \"url\"\n            3 => \"listorder\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"countriesInContinent\" => Illuminate\\Database\\Eloquent\\Collection {#2960\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"conference-filter\"\n  \"component\" => \"App\\Livewire\\ConferenceFilter\"\n  \"id\" => \"jdtPTVm0Q7J5gccW4FRp\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://iconf.lv/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Livewire\\ConferenceFilter@selectTopCategory<a href=\"phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FLivewire%2FConferenceFilter.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FLivewire%2FConferenceFilter.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Livewire/ConferenceFilter.php:51-80</a>", "middleware": "web", "duration": "227ms", "peak_memory": "48MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-781763451 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-781763451\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1340679975 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KzVnHwsb1j7aFLoepnAgBRMhnc1xQgVO8Wwk7tZ4</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"670 characters\">{&quot;data&quot;:{&quot;keyword&quot;:&quot;&quot;,&quot;selectedCategory&quot;:null,&quot;selectedCountry&quot;:null,&quot;selectedYear&quot;:null,&quot;selectedTopCategoryId&quot;:null,&quot;selectedContinentId&quot;:null,&quot;subCategories&quot;:[null,{&quot;keys&quot;:[],&quot;class&quot;:&quot;Illuminate\\\\Database\\\\Eloquent\\\\Collection&quot;,&quot;modelClass&quot;:null,&quot;s&quot;:&quot;elcln&quot;}],&quot;countriesInContinent&quot;:[null,{&quot;keys&quot;:[],&quot;class&quot;:&quot;Illuminate\\\\Database\\\\Eloquent\\\\Collection&quot;,&quot;modelClass&quot;:null,&quot;s&quot;:&quot;elcln&quot;}],&quot;paginators&quot;:[{&quot;page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;jdtPTVm0Q7J5gccW4FRp&quot;,&quot;name&quot;:&quot;conference-filter&quot;,&quot;path&quot;:&quot;conferences&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;81c69fd45afab601dfbb8d141fc12484632b1ea325d0fee55df7978df74c9cee&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"17 characters\">selectTopCategory</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-num>142</span>\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1340679975\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-598476710 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"719 characters\">XSRF-TOKEN=eyJpdiI6IkFYZC9VWDJUOGd0TnIyRXdpNnIzZ1E9PSIsInZhbHVlIjoiMEhaaUlFUmoya3FDTXkwYVJxamlQWjFLQ2tES1N1VG02WHpFMEtxUWVySjFvSGdlWURiVFl0QjZmRXNxa09rb1dKVmFUTCtkUC9aYnVOZEw3NkZjU21RZmJaMzBZZytabjNhbUlzaU9aQlQ4N0VWNzY0WHh3aWxjaVFSSzhDU0YiLCJtYWMiOiJkZjczN2ZjMDQzMGY3YjA3NTYyNDg2MTI2NTk4NWU3OThkZGNiMDU4MjAxNGMxYzVhYjI1Y2QzMjc1ZjA5MWMwIiwidGFnIjoiIn0%3D; iconf_meeting_session=eyJpdiI6IkNLT2RuaXR2aHArNXpxRHVLdXdreFE9PSIsInZhbHVlIjoidkpQdm0xazNVTU1YRHZMTGYzV0wxMksvRGdNTEVIZnF1N2dUczl2WEJxemZPckZ6Z3E1NDYzL0ZPYmZhUnhXbXJiSEFzTHh5aUVUZWZ0aHlveDQ3TGZTRkwrUEc1OGNDcGI0MFM5ZkRTemR4amYycVMwR2dERGNVYUhKdFcycDAiLCJtYWMiOiJhNTExOGE4NTE0OGJjZGFlYmE0ZGRiZjZjMWJkY2Y3OGY5NzEyYWY2YzNlYjhhY2QwODNjNTgyNjI2MmFmNmVhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">http://iconf.lv/conferences?category=&amp;country=&amp;year=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">http://iconf.lv</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">931</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">iconf.lv</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-598476710\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1208494986 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KzVnHwsb1j7aFLoepnAgBRMhnc1xQgVO8Wwk7tZ4</span>\"\n  \"<span class=sf-dump-key>iconf_meeting_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">n81JYPLEP0cENw7ZqI8BBaufrhZyV3CgHX5ObznX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1208494986\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-752479678 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 16 Jul 2025 11:40:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-752479678\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-811086310 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KzVnHwsb1j7aFLoepnAgBRMhnc1xQgVO8Wwk7tZ4</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://iconf.lv/conferences</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$i.a5gP7/Z07pk3CeR6vNfe9lGm.BtthHlLi1.olj0JJDfdNlCRY8K</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-811086310\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://iconf.lv/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}