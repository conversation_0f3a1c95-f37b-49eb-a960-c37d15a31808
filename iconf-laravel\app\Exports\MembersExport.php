<?php

namespace App\Exports;

use App\Models\Member;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Font;

class MembersExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $query;

    public function __construct($query = null)
    {
        $this->query = $query;
    }

    /**
     * 获取数据集合
     */
    public function collection()
    {
        $query = $this->query ?: Member::query();
        
        return $query->with(['events'])->get();
    }

    /**
     * 定义表头
     */
    public function headings(): array
    {
        return [
            'ID',
            '邮箱',
            '用户名',
            'VIP状态',
            '账户状态',
            '注册时间',
            '最后登录时间',
            '最后登录IP',
            '发布会议总数',
            '已发布会议数',
            '待审核会议数',
            '被拒绝会议数',
        ];
    }

    /**
     * 数据映射
     */
    public function map($member): array
    {
        $totalEvents = $member->events->count();
        $publishedEvents = $member->events->where('status', 1)->count();
        $pendingEvents = $member->events->where('status', 0)->count();
        $rejectedEvents = $member->events->where('status', 2)->count();

        return [
            $member->id,
            $member->email,
            $member->username,
            $member->vip ? 'VIP用户' : '普通用户',
            $member->status ? '正常' : '禁用',
            $member->regtime ? date('Y-m-d H:i:s', $member->regtime) : '',
            $member->logintime ? date('Y-m-d H:i:s', $member->logintime) : '',
            $member->loginip ?: '',
            $totalEvents,
            $publishedEvents,
            $pendingEvents,
            $rejectedEvents,
        ];
    }

    /**
     * 样式设置
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // 表头样式
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 12,
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => [
                        'argb' => 'FFE2E2E2',
                    ],
                ],
            ],
            // 数据行样式
            'A:L' => [
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
            ],
        ];
    }
}
