<?php

namespace App\Console\Commands;

use App\Models\News;
use Illuminate\Console\Command;

class TestTrixContent extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:trix-content';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试Trix编辑器内容处理';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 测试Trix编辑器内容处理...');
        $this->newLine();

        // 查找包含Trix编辑器内容的新闻
        $trixNews = News::where('content', 'LIKE', '%data-trix-%')
            ->orWhere('content', 'LIKE', '%attachment%')
            ->limit(3)
            ->get();

        if ($trixNews->isEmpty()) {
            $this->warn('没有找到包含Trix编辑器内容的新闻');
            return;
        }

        foreach ($trixNews as $news) {
            $this->info("📰 新闻ID: {$news->id}");
            $this->info("📝 标题: {$news->title}");
            $this->newLine();

            // 显示原始内容（截取前300字符）
            $originalContent = mb_substr($news->content, 0, 300);
            $this->line("🔤 原始内容:");
            $this->line($originalContent . '...');
            $this->newLine();

            // 显示解码后的内容（截取前300字符）
            $decodedContent = mb_substr($news->getDecodedContent(), 0, 300);
            $this->line("✨ 解码后内容:");
            $this->line($decodedContent . '...');
            $this->newLine();

            // 显示清理后的显示内容（截取前300字符）
            $cleanContent = mb_substr($news->getCleanDisplayContent(), 0, 300);
            $this->line("🧹 清理后显示内容:");
            $this->line($cleanContent . '...');
            $this->newLine();

            // 检查内容类型
            $hasTrixAttachment = str_contains($news->content, 'data-trix-attachment');
            $hasImages = str_contains($news->content, '<img');
            $hasFigure = str_contains($news->content, '<figure');
            
            $this->line("📎 包含Trix附件: " . ($hasTrixAttachment ? '是' : '否'));
            $this->line("🖼️  包含图片: " . ($hasImages ? '是' : '否'));
            $this->line("📐 包含Figure: " . ($hasFigure ? '是' : '否'));
            
            $this->line(str_repeat('-', 80));
            $this->newLine();
        }

        $this->info('✅ Trix编辑器内容处理测试完成！');
    }
}
