@echo off
echo ========================================
echo iConf项目图表导出工具
echo ========================================
echo.

REM 检查是否安装了mermaid-cli
where mmdc >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到mermaid-cli工具
    echo 请先安装: npm install -g @mermaid-js/mermaid-cli
    echo.
    pause
    exit /b 1
)

REM 创建输出目录
if not exist "charts" mkdir charts

echo 开始导出图表...
echo.

REM 创建临时的mermaid文件
echo 1. 导出项目架构图...
(
echo graph TB
echo     subgraph "前端展示层"
echo         A[用户访问] --^> B[Bootstrap + jQuery界面]
echo         B --^> C[响应式设计]
echo     end
echo     
echo     subgraph "应用层 - ThinkPHP 3.2"
echo         D[Home模块 - 前台] --^> E[IndexController]
echo         D --^> F[MemberController]
echo         D --^> G[CheckloginController]
echo         
echo         H[AdminICF模块 - 后台] --^> I[IndexController]
echo         H --^> J[LoginController]
echo         H --^> K[CheckAdminController]
echo         
echo         L[Common模块 - 公共] --^> M[function.php]
echo         L --^> N[config.php]
echo         L --^> O[seo_config.php]
echo     end
echo     
echo     subgraph "业务逻辑层"
echo         P[EventModel] --^> Q[会议管理]
echo         R[MemberModel] --^> S[用户管理]
echo         T[CategoryModel] --^> U[分类管理]
echo         V[NewsModel] --^> W[新闻管理]
echo     end
echo     
echo     subgraph "数据存储层"
echo         X[^(MySQL数据库^)] --^> Y[event - 会议表]
echo         X --^> Z[member - 用户表]
echo         X --^> AA[category - 分类表]
echo         X --^> BB[news - 新闻表]
echo         X --^> CC[ad_txt - 广告表]
echo         X --^> DD[country - 地区表]
echo         X --^> EE[list - 关联表]
echo         
echo         FF[文件存储] --^> GG[Uploads目录]
echo         GG --^> HH[按日期分目录]
echo         HH --^> II[图片文件]
echo         HH --^> JJ[文档文件]
echo     end
echo     
echo     A --^> D
echo     A --^> H
echo     E --^> P
echo     F --^> R
echo     I --^> P
echo     I --^> T
echo     I --^> V
echo     
echo     Q --^> Y
echo     S --^> Z
echo     U --^> AA
echo     W --^> BB
echo     
echo     style A fill:#e1f5fe
echo     style X fill:#f3e5f5
echo     style D fill:#e8f5e8
echo     style H fill:#fff3e0
) > temp_architecture.mmd

mmdc -i temp_architecture.mmd -o charts/项目架构图.png -w 1200 -H 800
if %errorlevel% equ 0 (
    echo    ✓ 项目架构图导出成功: charts/项目架构图.png
) else (
    echo    ✗ 项目架构图导出失败
)

echo.
echo 2. 导出数据库关系图...
(
echo erDiagram
echo     event {
echo         int id PK
echo         varchar cid "分类ID多个"
echo         int uid FK
echo         int venue FK
echo         varchar city
echo         varchar hotel
echo         varchar title
echo         varchar event
echo         varchar url
echo         int start_date
echo         int end_date
echo         int sub_date
echo         varchar email
echo         varchar web
echo         varchar tel
echo         text content
echo         varchar pic
echo         tinyint ding "置顶"
echo         tinyint push "推荐"
echo         tinyint status "状态"
echo         int addtime
echo         varchar summary
echo         tinyint is_featured "特色推荐"
echo     }
echo     
echo     category {
echo         int id PK
echo         int listorder
echo         int fid "父分类ID"
echo         varchar name
echo         varchar url
echo     }
echo     
echo     list {
echo         int id PK
echo         int cid FK
echo         int eid FK
echo     }
echo     
echo     member {
echo         int id PK
echo         varchar email
echo         varchar username
echo         smallint area
echo         varchar password
echo         varchar ip
echo         varchar regtime
echo         tinyint vip
echo         tinyint status
echo     }
echo     
echo     country {
echo         int id PK
echo         int listorder
echo         varchar venue
echo         varchar url
echo         int fid "父级ID"
echo     }
echo     
echo     news {
echo         int id PK
echo         varchar title
echo         text content
echo         timestamp publish_time
echo         int column_id FK
echo         varchar cover
echo         text summary
echo         int click_rate
echo         tinyint is_featured
echo     }
echo     
echo     news_type {
echo         int id PK
echo         varchar column_name
echo         int column_sort
echo     }
echo     
echo     ad_txt {
echo         int id PK
echo         int listorder
echo         varchar title
echo         int cid FK
echo         varchar link
echo         varchar pic
echo         text content
echo         int endtime
echo     }
echo     
echo     admin {
echo         int id PK
echo         varchar username
echo         varchar password
echo     }
echo     
echo     page {
echo         int id PK
echo         varchar title
echo         text content
echo     }
echo     
echo     vod {
echo         int id PK
echo         varchar title
echo         varchar file_path
echo         varchar external_link
echo         text video_code
echo         varchar cover_image
echo         int views
echo         tinyint is_recommended
echo         varchar vod_type
echo         timestamp publish_time
echo     }
echo     
echo     event ||--o{ list : "会议分类关联"
echo     category ||--o{ list : "分类会议关联"
echo     category ||--o{ category : "父子分类"
echo     member ||--o{ event : "用户发布会议"
echo     country ||--o{ event : "会议地点"
echo     country ||--o{ country : "父子地区"
echo     news_type ||--o{ news : "新闻分类"
echo     category ||--o{ ad_txt : "广告分类"
) > temp_database.mmd

mmdc -i temp_database.mmd -o charts/数据库关系图.png -w 1400 -H 1000
if %errorlevel% equ 0 (
    echo    ✓ 数据库关系图导出成功: charts/数据库关系图.png
) else (
    echo    ✗ 数据库关系图导出失败
)

echo.
echo 3. 清理临时文件...
del temp_architecture.mmd 2>nul
del temp_database.mmd 2>nul

echo.
echo ========================================
echo 导出完成！
echo 图表文件保存在 charts/ 目录中
echo ========================================
echo.

REM 打开输出目录
if exist "charts" (
    echo 正在打开输出目录...
    start charts
)

pause
