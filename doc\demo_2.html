<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AcademiaLink - International Academic Website Platform</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts - Inter -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        /* Custom animations */
        @keyframes fade-in-up {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .animate-fade-in-up {
            animation: fade-in-up 0.6s ease-out forwards;
            opacity: 0; /* Start invisible */
        }
        /* Hide mobile menu by default */
        .mobile-nav-hidden {
            display: none;
        }
    </style>
</head>
<body class="antialiased">

    <!-- Header -->
    <header class="bg-white shadow-lg fixed w-full z-50 transition-all duration-300 ease-in-out">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <!-- Logo -->
            <div class="flex items-center">
                <!-- BookOpen Icon SVG -->
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-8 w-8 text-indigo-600 mr-2"><path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path><path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 0 3-3h7z"></path></svg>
                <span class="text-2xl font-bold text-gray-800">AcademiaLink</span>
            </div>

            <!-- Desktop Navigation -->
            <nav class="hidden md:flex items-center space-x-8">
                <a href="#home" class="text-gray-600 hover:text-indigo-600 font-medium transition-colors duration-200">Home</a>
                <a href="#conferences" class="text-gray-600 hover:text-indigo-600 font-medium transition-colors duration-200">Conferences</a>
                <a href="#publications" class="text-gray-600 hover:text-indigo-600 font-medium transition-colors duration-200">Publications</a>
                <a href="#news" class="text-gray-600 hover:text-indigo-600 font-medium transition-colors duration-200">News & Insights</a>
                <a href="#about" class="text-gray-600 hover:text-indigo-600 font-medium transition-colors duration-200">About Us</a>
                <button class="bg-indigo-600 text-white px-6 py-2 rounded-full shadow-md hover:bg-indigo-700 transition-all duration-300 ease-in-out transform hover:scale-105">
                    Login / Register
                </button>
            </nav>

            <!-- Mobile Menu Button -->
            <div class="md:hidden">
                <button id="mobile-menu-button" class="text-gray-600 focus:outline-none focus:text-indigo-600">
                    <!-- Menu Icon SVG (initially) -->
                    <svg id="menu-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-7 w-7"><line x1="4" y1="12" x2="20" y2="12"></line><line x1="4" y1="6" x2="20" y2="6"></line><line x1="4" y1="18" x2="20" y2="18"></line></svg>
                    <!-- X Icon SVG (hidden initially) -->
                    <svg id="x-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-7 w-7 hidden"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                </button>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-nav" class="md:hidden bg-white py-4 shadow-md mobile-nav-hidden">
            <nav class="flex flex-col items-center space-y-4">
                <a href="#home" class="text-gray-700 hover:text-indigo-600 font-medium py-2 mobile-nav-link">Home</a>
                <a href="#conferences" class="text-gray-700 hover:text-indigo-600 font-medium py-2 mobile-nav-link">Conferences</a>
                <a href="#publications" class="text-gray-700 hover:text-indigo-600 font-medium py-2 mobile-nav-link">Publications</a>
                <a href="#news" class="text-gray-700 hover:text-indigo-600 font-medium py-2 mobile-nav-link">News & Insights</a>
                <a href="#about" class="text-gray-700 hover:text-indigo-600 font-medium py-2 mobile-nav-link">About Us</a>
                <button class="bg-indigo-600 text-white px-6 py-2 rounded-full shadow-md hover:bg-indigo-700 transition-all duration-300 ease-in-out transform hover:scale-105 w-3/4">
                    Login / Register
                </button>
            </nav>
        </div>
    </header>

    <main>
        <!-- Hero Section -->
        <section class="relative h-[600px] bg-gradient-to-r from-blue-700 to-indigo-800 flex items-center justify-center text-white overflow-hidden pt-20 md:pt-0">
            <!-- Background Shapes/Pattern -->
            <div class="absolute inset-0 z-0 opacity-20">
                <svg class="w-full h-full" viewBox="0 0 1440 600" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="100" cy="500" r="150" fill="url(#paint0_radial)" />
                    <circle cx="1300" cy="100" r="180" fill="url(#paint1_radial)" />
                    <defs>
                        <radialGradient id="paint0_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(100 500) rotate(90) scale(150)">
                            <stop stop-color="white" stop-opacity="0.8" />
                            <stop offset="1" stop-color="white" stop-opacity="0" />
                        </radialGradient>
                        <radialGradient id="paint1_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(1300 100) rotate(90) scale(180)">
                            <stop stop-color="white" stop-opacity="0.8" />
                            <stop offset="1" stop-color="white" stop-opacity="0" />
                        </radialGradient>
                    </defs>
                </svg>
            </div>

            <div class="relative z-10 text-center max-w-4xl px-6">
                <h1 class="text-5xl md:text-6xl font-extrabold leading-tight mb-6 animate-fade-in-up">
                    Connecting Global Academia, Empowering Future Research
                </h1>
                <p class="text-xl md:text-2xl font-light mb-10 opacity-90 animate-fade-in-up" style="animation-delay: 0.2s;">
                    Explore top conferences, cutting-edge publications, and global collaboration opportunities.
                </p>
                <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6 animate-fade-in-up" style="animation-delay: 0.4s;">
                    <button class="bg-white text-indigo-700 px-8 py-4 rounded-full text-lg font-semibold shadow-xl hover:bg-gray-100 transition-all duration-300 ease-in-out transform hover:scale-105">
                        Explore Conferences
                    </button>
                    <button class="border-2 border-white text-white px-8 py-4 rounded-full text-lg font-semibold shadow-xl hover:bg-white hover:text-indigo-700 transition-all duration-300 ease-in-out transform hover:scale-105">
                        Submit Your Research
                    </button>
                </div>
            </div>
        </section>

        <!-- Featured Conferences Section -->
        <section id="conferences" class="py-16 bg-gray-50">
            <div class="container mx-auto px-4">
                <h2 class="text-4xl font-extrabold text-center text-gray-800 mb-12">
                    <span class="text-indigo-600">Featured</span> Conferences
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Conference Card 1 -->
                    <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out transform hover:-translate-y-2 overflow-hidden">
                        <img src="https://placehold.co/400x250/4F46E5/FFFFFF?text=AI+Conf" alt="International AI Conference 2025" class="w-full h-48 object-cover" onerror="this.onerror=null; this.src='https://placehold.co/400x250/4F46E5/FFFFFF?text=International+AI+Conference+2025';" />
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-800 mb-2">International AI Conference 2025</h3>
                            <div class="flex items-center text-gray-600 text-sm mb-2">
                                <!-- Calendar Icon SVG -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-2 text-indigo-500"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>
                                <span>October 15-17, 2025</span>
                            </div>
                            <div class="flex items-center text-gray-600 text-sm mb-4">
                                <!-- MapPin Icon SVG -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-2 text-indigo-500"><path d="M12 12a5 5 0 1 0 0-10a5 5 1 0 0 0 10z"></path><path d="M12 12v10"></path></svg>
                                <span>San Francisco, USA</span>
                            </div>
                            <p class="text-gray-700 text-base mb-5 line-clamp-3">Explore the latest breakthroughs in Artificial Intelligence, covering machine learning, deep learning, natural language processing, and computer vision.</p>
                            <button class="w-full bg-indigo-600 text-white py-3 rounded-lg font-semibold hover:bg-indigo-700 transition-colors duration-300 ease-in-out">
                                Learn More 
                                <!-- ChevronRight Icon SVG -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="inline-block h-4 w-4 ml-1"><path d="M9 18l6-6-6-6"></path></svg>
                            </button>
                        </div>
                    </div>
                    <!-- Conference Card 2 -->
                    <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out transform hover:-translate-y-2 overflow-hidden">
                        <img src="https://placehold.co/400x250/22C55E/FFFFFF?text=Climate+Summit" alt="Global Climate Change Summit" class="w-full h-48 object-cover" onerror="this.onerror=null; this.src='https://placehold.co/400x250/22C55E/FFFFFF?text=Global+Climate+Change+Summit';" />
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-800 mb-2">Global Climate Change Summit</h3>
                            <div class="flex items-center text-gray-600 text-sm mb-2">
                                <!-- Calendar Icon SVG -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-2 text-indigo-500"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>
                                <span>November 5-7, 2025</span>
                            </div>
                            <div class="flex items-center text-gray-600 text-sm mb-4">
                                <!-- MapPin Icon SVG -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-2 text-indigo-500"><path d="M12 12a5 5 0 1 0 0-10a5 5 1 0 0 0 10z"></path><path d="M12 12v10"></path></svg>
                                <span>Berlin, Germany</span>
                            </div>
                            <p class="text-gray-700 text-base mb-5 line-clamp-3">Focus on climate science, policy making, sustainable development, and energy transition to address global climate challenges.</p>
                            <button class="w-full bg-indigo-600 text-white py-3 rounded-lg font-semibold hover:bg-indigo-700 transition-colors duration-300 ease-in-out">
                                Learn More 
                                <!-- ChevronRight Icon SVG -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="inline-block h-4 w-4 ml-1"><path d="M9 18l6-6-6-6"></path></svg>
                            </button>
                        </div>
                    </div>
                    <!-- Conference Card 3 -->
                    <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out transform hover:-translate-y-2 overflow-hidden">
                        <img src="https://placehold.co/400x250/EF4444/FFFFFF?text=BioMed+Conf" alt="Annual Biomedical Engineering Conference" class="w-full h-48 object-cover" onerror="this.onerror=null; this.src='https://placehold.co/400x250/EF4444/FFFFFF?text=Annual+Biomedical+Engineering+Conference';" />
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-800 mb-2">Annual Biomedical Engineering Conference</h3>
                            <div class="flex items-center text-gray-600 text-sm mb-2">
                                <!-- Calendar Icon SVG -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-2 text-indigo-500"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>
                                <span>January 20-22, 2026</span>
                            </div>
                            <div class="flex items-center text-gray-600 text-sm mb-4">
                                <!-- MapPin Icon SVG -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-2 text-indigo-500"><path d="M12 12a5 5 0 1 0 0-10a5 5 1 0 0 0 10z"></path><path d="M12 12v10"></path></svg>
                                <span>Tokyo, Japan</span>
                            </div>
                            <p class="text-gray-700 text-base mb-5 line-clamp-3">Covering cutting-edge research in biomaterials, medical devices, bioinformatics, and regenerative medicine.</p>
                            <button class="w-full bg-indigo-600 text-white py-3 rounded-lg font-semibold hover:bg-indigo-700 transition-colors duration-300 ease-in-out">
                                Learn More 
                                <!-- ChevronRight Icon SVG -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="inline-block h-4 w-4 ml-1"><path d="M9 18l6-6-6-6"></path></svg>
                            </button>
                        </div>
                    </div>
                    <!-- Conference Card 4 -->
                    <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out transform hover:-translate-y-2 overflow-hidden">
                        <img src="https://placehold.co/400x250/F59E0B/FFFFFF?text=EdTech+Forum" alt="Future of Education Technology Forum" class="w-full h-48 object-cover" onerror="this.onerror=null; this.src='https://placehold.co/400x250/F59E0B/FFFFFF?text=Future+of+Education+Technology+Forum';" />
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-800 mb-2">Future of Education Technology Forum</h3>
                            <div class="flex items-center text-gray-600 text-sm mb-2">
                                <!-- Calendar Icon SVG -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-2 text-indigo-500"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>
                                <span>December 1-3, 2025</span>
                            </div>
                            <div class="flex items-center text-gray-600 text-sm mb-4">
                                <!-- MapPin Icon SVG -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-2 text-indigo-500"><path d="M12 12a5 5 0 1 0 0-10a5 5 1 0 0 0 10z"></path><path d="M12 12v10"></path></svg>
                                <span>London, UK</span>
                            </div>
                            <p class="text-gray-700 text-base mb-5 line-clamp-3">Discussing innovations in educational technology, online learning, personalized education, and future teaching models.</p>
                            <button class="w-full bg-indigo-600 text-white py-3 rounded-lg font-semibold hover:bg-indigo-700 transition-colors duration-300 ease-in-out">
                                Learn More 
                                <!-- ChevronRight Icon SVG -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="inline-block h-4 w-4 ml-1"><path d="M9 18l6-6-6-6"></path></svg>
                            </button>
                        </div>
                    </div>
                    <!-- Conference Card 5 -->
                    <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out transform hover:-translate-y-2 overflow-hidden">
                        <img src="https://placehold.co/400x250/8B5CF6/FFFFFF?text=Digital+Economy" alt="Digital Economy and Social Transformation" class="w-full h-48 object-cover" onerror="this.onerror=null; this.src='https://placehold.co/400x250/8B5CF6/FFFFFF?text=Digital+Economy+and+Social+Transformation';" />
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-800 mb-2">Digital Economy and Social Transformation</h3>
                            <div class="flex items-center text-gray-600 text-sm mb-2">
                                <!-- Calendar Icon SVG -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-2 text-indigo-500"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>
                                <span>February 10-12, 2026</span>
                            </div>
                            <div class="flex items-center text-gray-600 text-sm mb-4">
                                <!-- MapPin Icon SVG -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-2 text-indigo-500"><path d="M12 12a5 5 0 1 0 0-10a5 5 1 0 0 0 10z"></path><path d="M12 12v10"></path></svg>
                                <span>Singapore</span>
                            </div>
                            <p class="text-gray-700 text-base mb-5 line-clamp-3">Analyzing the impact of the digital economy on society, culture, and governance, exploring policies and innovations.</p>
                            <button class="w-full bg-indigo-600 text-white py-3 rounded-lg font-semibold hover:bg-indigo-700 transition-colors duration-300 ease-in-out">
                                Learn More 
                                <!-- ChevronRight Icon SVG -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="inline-block h-4 w-4 ml-1"><path d="M9 18l6-6-6-6"></path></svg>
                            </button>
                        </div>
                    </div>
                    <!-- Conference Card 6 -->
                    <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out transform hover:-translate-y-2 overflow-hidden">
                        <img src="https://placehold.co/400x250/06B6D4/FFFFFF?text=Sustainable+City" alt="Sustainable Urban Development Congress" class="w-full h-48 object-cover" onerror="this.onerror=null; this.src='https://placehold.co/400x250/06B6D4/FFFFFF?text=Sustainable+Urban+Development+Congress';" />
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-800 mb-2">Sustainable Urban Development Congress</h3>
                            <div class="flex items-center text-gray-600 text-sm mb-2">
                                <!-- Calendar Icon SVG -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-2 text-indigo-500"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>
                                <span>March 5-7, 2026</span>
                            </div>
                            <div class="flex items-center text-gray-600 text-sm mb-4">
                                <!-- MapPin Icon SVG -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-2 text-indigo-500"><path d="M12 12a5 5 0 1 0 0-10a5 5 1 0 0 0 10z"></path><path d="M12 12v10"></path></svg>
                                <span>Copenhagen, Denmark</span>
                            </div>
                            <p class="text-gray-700 text-base mb-5 line-clamp-3">Focusing on urban planning, green building, smart transportation, and community resilience for building sustainable cities.</p>
                            <button class="w-full bg-indigo-600 text-white py-3 rounded-lg font-semibold hover:bg-indigo-700 transition-colors duration-300 ease-in-out">
                                Learn More 
                                <!-- ChevronRight Icon SVG -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="inline-block h-4 w-4 ml-1"><path d="M9 18l6-6-6-6"></path></svg>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="text-center mt-12">
                    <button class="bg-indigo-600 text-white px-8 py-4 rounded-full text-lg font-semibold shadow-md hover:bg-indigo-700 transition-all duration-300 ease-in-out transform hover:scale-105">
                        View All Conferences 
                        <!-- ChevronRight Icon SVG -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="inline-block h-5 w-5 ml-2"><path d="M9 18l6-6-6-6"></path></svg>
                    </button>
                </div>
            </div>
        </section>

        <!-- Upcoming Conferences Section -->
        <section class="py-16 bg-white">
            <div class="container mx-auto px-4">
                <h2 class="text-4xl font-extrabold text-center text-gray-800 mb-12">
                    <span class="text-purple-600">Upcoming</span> Conferences
                </h2>
                <div class="max-w-3xl mx-auto space-y-6">
                    <!-- Upcoming Conference 1 -->
                    <div class="flex items-start p-6 bg-gray-50 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
                        <div class="flex-shrink-0 bg-purple-100 text-purple-700 rounded-full p-3 mr-4">
                            <!-- Calendar Icon SVG -->
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 font-semibold mb-1">September 1, 2025</p>
                            <h3 class="text-xl font-semibold text-gray-800 mb-2">Data Science and Machine Learning Workshop</h3>
                            <div class="flex items-center text-gray-600 text-base">
                                <!-- MapPin Icon SVG -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 mr-2 text-purple-500"><path d="M12 12a5 5 0 1 0 0-10a5 5 1 0 0 0 10z"></path><path d="M12 12v10"></path></svg>
                                <span>Online</span>
                            </div>
                        </div>
                    </div>
                    <!-- Upcoming Conference 2 -->
                    <div class="flex items-start p-6 bg-gray-50 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
                        <div class="flex-shrink-0 bg-purple-100 text-purple-700 rounded-full p-3 mr-4">
                            <!-- Calendar Icon SVG -->
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 font-semibold mb-1">September 15, 2025</p>
                            <h3 class="text-xl font-semibold text-gray-800 mb-2">Frontiers in Nanotechnology Forum</h3>
                            <div class="flex items-center text-gray-600 text-base">
                                <!-- MapPin Icon SVG -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 mr-2 text-purple-500"><path d="M12 12a5 5 0 1 0 0-10a5 5 1 0 0 0 10z"></path><path d="M12 12v10"></path></svg>
                                <span>Seoul, South Korea</span>
                            </div>
                        </div>
                    </div>
                    <!-- Upcoming Conference 3 -->
                    <div class="flex items-start p-6 bg-gray-50 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
                        <div class="flex-shrink-0 bg-purple-100 text-purple-700 rounded-full p-3 mr-4">
                            <!-- Calendar Icon SVG -->
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 font-semibold mb-1">October 10, 2025</p>
                            <h3 class="text-xl font-semibold text-gray-800 mb-2">Global Health Innovation Summit</h3>
                            <div class="flex items-center text-gray-600 text-base">
                                <!-- MapPin Icon SVG -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 mr-2 text-purple-500"><path d="M12 12a5 5 0 1 0 0-10a5 5 1 0 0 0 10z"></path><path d="M12 12v10"></path></svg>
                                <span>Geneva, Switzerland</span>
                            </div>
                        </div>
                    </div>
                    <!-- Upcoming Conference 4 -->
                    <div class="flex items-start p-6 bg-gray-50 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
                        <div class="flex-shrink-0 bg-purple-100 text-purple-700 rounded-full p-3 mr-4">
                            <!-- Calendar Icon SVG -->
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 font-semibold mb-1">October 25, 2025</p>
                            <h3 class="text-xl font-semibold text-gray-800 mb-2">Cosmology and Particle Physics Symposium</h3>
                            <div class="flex items-center text-gray-600 text-base">
                                <!-- MapPin Icon SVG -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 mr-2 text-purple-500"><path d="M12 12a5 5 0 1 0 0-10a5 5 1 0 0 0 10z"></path><path d="M12 12v10"></path></svg>
                                <span>Cambridge, UK</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Latest News & Insights Section -->
        <section id="news" class="py-16 bg-gray-50">
            <div class="container mx-auto px-4">
                <h2 class="text-4xl font-extrabold text-center text-gray-800 mb-12">
                    <span class="text-orange-600">Latest</span> News & Insights
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- News Card 1 -->
                    <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out transform hover:-translate-y-1 overflow-hidden">
                        <img src="https://placehold.co/400x250/10B981/FFFFFF?text=AI+Drug+Discovery" alt="Revolutionary Applications of AI in Drug Discovery" class="w-full h-40 object-cover" onerror="this.onerror=null; this.src='https://placehold.co/400x250/10B981/FFFFFF?text=Revolutionary+Applications+of+AI+in+Drug+Discovery';" />
                        <div class="p-5">
                            <h3 class="text-lg font-bold text-gray-800 mb-2 line-clamp-2">Revolutionary Applications of AI in Drug Discovery</h3>
                            <p class="text-sm text-gray-500 mb-3">July 1, 2025</p>
                            <p class="text-gray-700 text-sm mb-4 line-clamp-3">Artificial intelligence is transforming drug discovery at an unprecedented pace, shortening development cycles and increasing success rates.</p>
                            <a href="#" class="text-indigo-600 font-semibold hover:text-indigo-800 transition-colors duration-200 flex items-center">
                                Read More 
                                <!-- ChevronRight Icon SVG -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 ml-1"><path d="M9 18l6-6-6-6"></path></svg>
                            </a>
                        </div>
                    </div>
                    <!-- News Card 2 -->
                    <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out transform hover:-translate-y-1 overflow-hidden">
                        <img src="https://placehold.co/400x250/EF4444/FFFFFF?text=Quantum+Computing" alt="Quantum Computing: A Leap from Theory to Practice" class="w-full h-40 object-cover" onerror="this.onerror=null; this.src='https://placehold.co/400x250/EF4444/FFFFFF?text=Quantum+Computing:+A+Leap+from+Theory+to+Practice';" />
                        <div class="p-5">
                            <h3 class="text-lg font-bold text-gray-800 mb-2 line-clamp-2">Quantum Computing: A Leap from Theory to Practice</h3>
                            <p class="text-sm text-gray-500 mb-3">June 20, 2025</p>
                            <p class="text-gray-700 text-sm mb-4 line-clamp-3">Recent advancements in quantum computing herald a new era of computational power, challenging the limits of traditional computing.</p>
                            <a href="#" class="text-indigo-600 font-semibold hover:text-indigo-800 transition-colors duration-200 flex items-center">
                                Read More 
                                <!-- ChevronRight Icon SVG -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 ml-1"><path d="M9 18l6-6-6-6"></path></svg>
                            </a>
                        </div>
                    </div>
                    <!-- News Card 3 -->
                    <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out transform hover:-translate-y-1 overflow-hidden">
                        <img src="https://placehold.co/400x250/3B82F6/FFFFFF?text=Sustainable+Energy" alt="Global Collaboration for Sustainable Energy Solutions" class="w-full h-40 object-cover" onerror="this.onerror=null; this.src='https://placehold.co/400x250/3B82F6/FFFFFF?text=Global+Collaboration+for+Sustainable+Energy+Solutions';" />
                        <div class="p-5">
                            <h3 class="text-lg font-bold text-gray-800 mb-2 line-clamp-2">Global Collaboration for Sustainable Energy Solutions</h3>
                            <p class="text-sm text-gray-500 mb-3">June 10, 2025</p>
                            <p class="text-gray-700 text-sm mb-4 line-clamp-3">The international community is strengthening cooperation to jointly develop and promote clean, sustainable energy technologies.</p>
                            <a href="#" class="text-indigo-600 font-semibold hover:text-indigo-800 transition-colors duration-200 flex items-center">
                                Read More 
                                <!-- ChevronRight Icon SVG -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 ml-1"><path d="M9 18l6-6-6-6"></path></svg>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="text-center mt-12">
                    <button class="bg-orange-600 text-white px-8 py-4 rounded-full text-lg font-semibold shadow-md hover:bg-orange-700 transition-all duration-300 ease-in-out transform hover:scale-105">
                        View All News 
                        <!-- ChevronRight Icon SVG -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="inline-block h-5 w-5 ml-2"><path d="M9 18l6-6-6-6"></path></svg>
                    </button>
                </div>
            </div>
        </section>

        <!-- Popular Destinations Section -->
        <section class="py-16 bg-white">
            <div class="container mx-auto px-4">
                <h2 class="text-4xl font-extrabold text-center text-gray-800 mb-12">
                    <span class="text-green-600">Popular</span> Conference Destinations
                </h2>
                <div class="flex flex-wrap justify-center gap-6">
                    <!-- Destination 1 -->
                    <div class="flex flex-col items-center p-6 bg-gray-50 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 w-36 text-center">
                        <!-- Globe Icon SVG -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-12 w-12 text-green-600 mb-3"><circle cx="12" cy="12" r="10"></circle><path d="M12 2a14.5 14.5 0 0 0 0 20a14.5 14.5 0 0 0 0-20"></path><path d="M2 12h20"></path></svg>
                        <p class="text-lg font-semibold text-gray-800">London</p>
                    </div>
                    <!-- Destination 2 -->
                    <div class="flex flex-col items-center p-6 bg-gray-50 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 w-36 text-center">
                        <!-- Globe Icon SVG -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-12 w-12 text-green-600 mb-3"><circle cx="12" cy="12" r="10"></circle><path d="M12 2a14.5 14.5 0 0 0 0 20a14.5 14.5 0 0 0 0-20"></path><path d="M2 12h20"></path></svg>
                        <p class="text-lg font-semibold text-gray-800">New York</p>
                    </div>
                    <!-- Destination 3 -->
                    <div class="flex flex-col items-center p-6 bg-gray-50 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 w-36 text-center">
                        <!-- Globe Icon SVG -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-12 w-12 text-green-600 mb-3"><circle cx="12" cy="12" r="10"></circle><path d="M12 2a14.5 14.5 0 0 0 0 20a14.5 14.5 0 0 0 0-20"></path><path d="M2 12h20"></path></svg>
                        <p class="text-lg font-semibold text-gray-800">Tokyo</p>
                    </div>
                    <!-- Destination 4 -->
                    <div class="flex flex-col items-center p-6 bg-gray-50 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 w-36 text-center">
                        <!-- Globe Icon SVG -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-12 w-12 text-green-600 mb-3"><circle cx="12" cy="12" r="10"></circle><path d="M12 2a14.5 14.5 0 0 0 0 20a14.5 14.5 0 0 0 0-20"></path><path d="M2 12h20"></path></svg>
                        <p class="text-lg font-semibold text-gray-800">Berlin</p>
                    </div>
                    <!-- Destination 5 -->
                    <div class="flex flex-col items-center p-6 bg-gray-50 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 w-36 text-center">
                        <!-- Globe Icon SVG -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-12 w-12 text-green-600 mb-3"><circle cx="12" cy="12" r="10"></circle><path d="M12 2a14.5 14.5 0 0 0 0 20a14.5 14.5 0 0 0 0-20"></path><path d="M2 12h20"></path></svg>
                        <p class="text-lg font-semibold text-gray-800">Singapore</p>
                    </div>
                    <!-- Destination 6 -->
                    <div class="flex flex-col items-center p-6 bg-gray-50 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 w-36 text-center">
                        <!-- Globe Icon SVG -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-12 w-12 text-green-600 mb-3"><circle cx="12" cy="12" r="10"></circle><path d="M12 2a14.5 14.5 0 0 0 0 20a14.5 14.5 0 0 0 0-20"></path><path d="M2 12h20"></path></svg>
                        <p class="text-lg font-semibold text-gray-800">Sydney</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Call to Action Section -->
        <section class="py-20 bg-gradient-to-r from-indigo-700 to-purple-800 text-white text-center">
            <div class="container mx-auto px-4">
                <h2 class="text-4xl md:text-5xl font-extrabold leading-tight mb-6">
                    Organizing a Conference?
                </h2>
                <p class="text-xl md:text-2xl font-light mb-10 opacity-90">
                    Leverage our platform to easily reach the global academic community.
                </p>
                <button class="bg-white text-indigo-700 px-10 py-5 rounded-full text-xl font-semibold shadow-xl hover:bg-gray-100 transition-all duration-300 ease-in-out transform hover:scale-105">
                    Learn How to Get Started 
                    <!-- ChevronRight Icon SVG -->
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="inline-block h-6 w-6 ml-2"><path d="M9 18l6-6-6-6"></path></svg>
                </button>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-gray-300 py-12">
        <div class="container mx-auto px-4 grid grid-cols-1 md:grid-cols-4 gap-8">
            <!-- Brand Info -->
            <div class="col-span-1 md:col-span-1">
                <div class="flex items-center mb-4">
                    <!-- BookOpen Icon SVG -->
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-8 w-8 text-indigo-400 mr-2"><path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path><path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 0 3-3h7z"></path></svg>
                    <span class="text-2xl font-bold text-white">AcademiaLink</span>
                </div>
                <p class="text-sm leading-relaxed">
                    Connecting global academia, empowering cutting-edge research and innovation.
                </p>
            </div>

            <!-- Quick Links -->
            <div>
                <h3 class="text-lg font-semibold text-white mb-4">Quick Links</h3>
                <ul class="space-y-2">
                    <li><a href="#home" class="hover:text-white transition-colors duration-200">Home</a></li>
                    <li><a href="#conferences" class="hover:text-white transition-colors duration-200">Conferences</a></li>
                    <li><a href="#publications" class="hover:text-white transition-colors duration-200">Publications</a></li>
                    <li><a href="#news" class="hover:text-white transition-colors duration-200">News</a></li>
                </ul>
            </div>

            <!-- Resources -->
            <div>
                <h3 class="text-lg font-semibold text-white mb-4">Resources</h3>
                <ul class="space-y-2">
                    <li><a href="#" class="hover:text-white transition-colors duration-200">Submit Paper</a></li>
                    <li><a href="#" class="hover:text-white transition-colors duration-200">Become a Reviewer</a></li>
                    <li><a href="#" class="hover:text-white transition-colors duration-200">FAQ</a></li>
                    <li><a href="#" class="hover:text-white transition-colors duration-200">Career Opportunities</a></li>
                </ul>
            </div>

            <!-- Contact Info -->
            <div>
                <h3 class="text-lg font-semibold text-white mb-4">Contact Us</h3>
                <p class="text-sm mb-2">
                    <!-- MapPin Icon SVG -->
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="inline-block h-4 w-4 mr-2 text-indigo-400"><path d="M12 12a5 5 0 1 0 0-10a5 5 1 0 0 0 10z"></path><path d="M12 12v10"></path></svg>
                    Global Academic Hub, 123 Research Ave
                </p>
                <p class="text-sm mb-2">
                    <!-- Mail Icon SVG -->
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="inline-block h-4 w-4 mr-2 text-indigo-400"><rect x="2" y="4" width="20" height="16" rx="2"></rect><path d="M22 6l-10 7L2 6"></path></svg>
                    <EMAIL>
                </p>
                <div class="flex space-x-4 mt-4">
                    <!-- Social Media Icons - Placeholder -->
                    <a href="#" class="text-gray-400 hover:text-white transition-colors duration-200">
                        <!-- Users Icon SVG -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors duration-200">
                        <!-- Target Icon SVG -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6"><circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="12" r="6"></circle><circle cx="12" cy="12" r="2"></circle></svg>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors duration-200">
                        <!-- Briefcase Icon SVG -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6"><rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect><path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path></svg>
                    </a>
                </div>
            </div>
        </div>
        <div class="border-t border-gray-700 mt-8 pt-8 text-center text-sm text-gray-500">
            &copy; <span id="current-year"></span> AcademiaLink. All rights reserved.
        </div>
    </footer>

    <script>
        // Get current year for footer
        document.getElementById('current-year').textContent = new Date().getFullYear();

        // Mobile menu toggle functionality
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileNav = document.getElementById('mobile-nav');
        const menuIcon = document.getElementById('menu-icon');
        const xIcon = document.getElementById('x-icon');
        const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');

        mobileMenuButton.addEventListener('click', () => {
            mobileNav.classList.toggle('mobile-nav-hidden');
            menuIcon.classList.toggle('hidden');
            xIcon.classList.toggle('hidden');
        });

        // Close mobile menu when a link is clicked
        mobileNavLinks.forEach(link => {
            link.addEventListener('click', () => {
                mobileNav.classList.add('mobile-nav-hidden');
                menuIcon.classList.remove('hidden');
                xIcon.classList.add('hidden');
            });
        });
    </script>
</body>
</html>
