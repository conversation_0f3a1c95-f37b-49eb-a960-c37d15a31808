<?php

namespace App\Filament\Member\Widgets;

use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class QuickActionsWidget extends Widget
{
    protected static string $view = 'filament.member.widgets.quick-actions';
    
    protected int | string | array $columnSpan = 'full';
    
    protected static ?int $sort = 2;

    public function getActions(): array
    {
        $member = Auth::guard('member')->user();
        
        return [
            Action::make('create_event')
                ->label('Publish Event')
                ->icon('heroicon-o-plus-circle')
                ->color('primary')
                ->size('lg')
                ->url(route('filament.member.resources.events.create'))
                ->extraAttributes(['class' => 'w-full']),

            Action::make('my_events')
                ->label('My Events')
                ->icon('heroicon-o-calendar-days')
                ->color('success')
                ->size('lg')
                ->url(route('filament.member.resources.events.index'))
                ->extraAttributes(['class' => 'w-full']),
                
            Action::make('edit_profile')
                ->label('Edit Profile')
                ->icon('heroicon-o-user-circle')
                ->color('gray')
                ->size('lg')
                ->url('/member/edit-profile')
                ->extraAttributes(['class' => 'w-full']),
                
            Action::make('upgrade_vip')
                ->label($member && $member->vip ? 'VIP Member' : 'Upgrade VIP')
                ->icon($member && $member->vip ? 'heroicon-o-star' : 'heroicon-o-arrow-up-circle')
                ->color($member && $member->vip ? 'warning' : 'info')
                ->size('lg')
                ->disabled($member && $member->vip)
                ->action(function () {
                    // VIP upgrade logic can be added here
                    $this->notify('success', 'VIP upgrade feature coming soon!');
                })
                ->extraAttributes(['class' => 'w-full']),
        ];
    }
    
    protected function notify(string $type, string $message): void
    {
        session()->flash('filament.notifications', [
            [
                'type' => $type,
                'title' => $message,
            ]
        ]);
    }
}
