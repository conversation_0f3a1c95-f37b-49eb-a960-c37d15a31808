# 会员面板 – 关注领域会议推荐模块

> 更新时间：2025-06-25

## 功能简介
在会员中心首页展示当前登录会员所关注领域（`member.area` 指向 `category` 父分类）的最新会议列表。

* 仅推荐父分类及其全部子分类下 **未结束**、**已发布** 的会议。
* 最多展示 10 条，按开始日期升序。
* 模块文件：
  * 控制器小部件 `app/Filament/Member/Widgets/FocusAreaEventsWidget.php`
  * 视图 `resources/views/filament/member/widgets/focus-area-events.blade.php`

## 主要逻辑
1. 读取登录会员 `area` 字段得到父分类 ID。
2. 使用 `Category` 查询父分类及子分类 ID，缓存 5 分钟：
   ```php
   Cache::remember("member:{id}:focus_category_ids", 300, ...);
   ```
3. 使用一次 `JOIN list` 关联表筛选会议，避免 N+1：
   ```sql
   SELECT event.*
   FROM event
   JOIN list ON event.id = list.eid
   WHERE list.cid IN (?)
     AND event.status = Published
     AND event.end_date >= NOW()
   ORDER BY event.start_date
   LIMIT 10;
   ```
4. 结果缓存 5 分钟 `member:{id}:focus_events`，新增/更新会议或分类时主动 `Cache::forget`。

## 索引建议
```
CREATE INDEX idx_list_cid_eid ON list (cid, eid);
CREATE INDEX idx_event_status_enddate ON event (status, end_date);
```

## 单元测试
文件：`tests/Unit/FocusAreaEventsWidgetTest.php`

| 场景 | 断言 |
|------|------|
| 正常返回关注领域下未结束会议 | 仅返回 1 条未来会议，过滤已结束会议 |
| 命中缓存 | 当缓存存在时，不触发数据库查询 |

运行全部测试：
```bash
php artisan test --filter=FocusAreaEventsWidgetTest
```

## 变更记录
| 版本 | 日期 | 说明 |
|-------|------------|-------|
| v1.0 | 2025-06-25 | 初始实现、数据库优化、缓存、单元测试 |
