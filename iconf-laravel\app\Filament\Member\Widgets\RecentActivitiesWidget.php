<?php

namespace App\Filament\Member\Widgets;

use App\Models\Event;
use App\Enums\ConferenceStatus;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class RecentActivitiesWidget extends Widget
{
    protected static string $view = 'filament.member.widgets.recent-activities';
    
    protected int | string | array $columnSpan = [
        'md' => 2,
        'xl' => 1,
    ];
    
    protected static ?int $sort = 4;

    public function getActivities(): array
    {
        $member = Auth::guard('member')->user();

        if (!$member) {
            return [];
        }

        $activities = [];

        // Get recent conference activities
        $recentEvents = $member->events()
            ->latest('addtime')
            ->limit(8)
            ->get();

        foreach ($recentEvents as $event) {
            $statusInfo = $this->getStatusInfo($event->status);
            $activities[] = [
                'type' => 'event',
                'title' => $statusInfo['action'],
                'description' => $event->title,
                'time' => $this->formatTime($event->addtime),
                'status' => $event->status,
                'status_label' => $statusInfo['label'],
                'icon' => $statusInfo['icon'],
                'color' => $statusInfo['color'],
                'url' => route('filament.member.resources.events.edit', ['record' => $event->id]),
                'extra_info' => $event->city ? "📍 {$event->city}" : null,
            ];
        }

        // Add system activity (registration welcome)
        if (count($activities) < 3) {
            $activities[] = [
                'type' => 'system',
                'title' => '🎉 Welcome Aboard',
                'description' => 'Welcome to the iConf conference publishing system, start your academic journey!',
                'time' => $this->formatTime($member->regtime),
                'status' => 'welcome',
                'status_label' => 'System Message',
                'icon' => 'heroicon-o-sparkles',
                'color' => 'primary',
                'url' => null,
                'extra_info' => $member->vip ? '🌟 VIP Member' : '👤 Regular Member',
            ];
        }

        // Add more system activity examples
        if (count($activities) < 4) {
            $activities[] = [
                'type' => 'tip',
                'title' => '💡 Usage Tip',
                'description' => $member->vip ?
                    'Conferences published by VIP members will be automatically approved' :
                    'Conferences published require admin approval',
                'time' => $this->formatTime($member->regtime + 3600), // 1 hour after registration
                'status' => 'info',
                'status_label' => 'System Tip',
                'icon' => 'heroicon-o-light-bulb',
                'color' => 'info',
                'url' => null,
                'extra_info' => '📚 Help Information',
            ];
        }

        // Sort by time
        usort($activities, function ($a, $b) {
            $timeA = strtotime($a['time']);
            $timeB = strtotime($b['time']);
            return $timeB - $timeA;
        });

        return array_slice($activities, 0, 6);
    }
    
    private function formatTime($timestamp): string
    {
        if (is_numeric($timestamp)) {
            return Carbon::createFromTimestamp($timestamp)->diffForHumans();
        }
        
        return Carbon::parse($timestamp)->diffForHumans();
    }
    
    /**
     * Get status information (including action, label, icon, color)
     */
    private function getStatusInfo($status): array
    {
        if ($status instanceof ConferenceStatus) {
            return match ($status) {
                ConferenceStatus::Pending => [
                    'action' => '📝 Submitted a conference',
                    'label' => 'Pending',
                    'icon' => 'heroicon-o-clock',
                    'color' => 'warning',
                ],
                ConferenceStatus::Published => [
                    'action' => '✅ Published a conference',
                    'label' => 'Published',
                    'icon' => 'heroicon-o-check-circle',
                    'color' => 'success',
                ],
                ConferenceStatus::Rejected => [
                    'action' => '❌ Conference was rejected',
                    'label' => 'Rejected',
                    'icon' => 'heroicon-o-x-circle',
                    'color' => 'danger',
                ],
                default => [
                    'action' => '📋 Created a conference',
                    'label' => 'Draft',
                    'icon' => 'heroicon-o-document',
                    'color' => 'gray',
                ],
            };
        }

        // Compatible with numeric status
        return match ($status) {
            0 => [
                'action' => '📝 Submitted a conference',
                'label' => 'Pending',
                'icon' => 'heroicon-o-clock',
                'color' => 'warning',
            ],
            1 => [
                'action' => '✅ Published a conference',
                'label' => 'Published',
                'icon' => 'heroicon-o-check-circle',
                'color' => 'success',
            ],
            2 => [
                'action' => '❌ Conference was rejected',
                'label' => 'Rejected',
                'icon' => 'heroicon-o-x-circle',
                'color' => 'danger',
            ],
            default => [
                'action' => '📋 Created a conference',
                'label' => 'Draft',
                'icon' => 'heroicon-o-document',
                'color' => 'gray',
            ],
        };
    }
}
