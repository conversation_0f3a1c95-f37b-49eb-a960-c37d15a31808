{"__meta": {"id": "01K09GN9WDXAACC6RC0XRHHFD9", "datetime": "2025-07-16 11:42:02", "utime": **********.125819, "method": "GET", "uri": "/categories/social_sciences_&amp;_humanities", "ip": "127.0.0.1"}, "messages": {"count": 4, "messages": [{"message": "[11:42:02] LOG.info: Finding category by URL {\n    \"url\": \"social_sciences_&amp;_humanities\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.048032, "xdebug_link": null, "collector": "log"}, {"message": "[11:42:02] LOG.info: Category found {\n    \"id\": 167,\n    \"name\": \"Social Sciences &amp; Humanities\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.05083, "xdebug_link": null, "collector": "log"}, {"message": "[11:42:02] LOG.info: Top category with children {\n    \"category_id\": 167,\n    \"child_ids\": [\n        168,\n        170,\n        171,\n        172,\n        173,\n        174,\n        175,\n        176,\n        177,\n        178,\n        179,\n        180,\n        181,\n        182,\n        183,\n        184\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.051927, "xdebug_link": null, "collector": "log"}, {"message": "[11:42:02] LOG.info: Conferences loaded {\n    \"count\": 15,\n    \"total\": 647\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.072285, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752666121.866651, "end": **********.125832, "duration": 0.25918102264404297, "duration_str": "259ms", "measures": [{"label": "Booting", "start": 1752666121.866651, "relative_start": 0, "end": **********.030483, "relative_end": **********.030483, "duration": 0.****************, "duration_str": "164ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.030489, "relative_start": 0.*****************, "end": **********.125832, "relative_end": 0, "duration": 0.*****************, "duration_str": "95.34ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.037398, "relative_start": 0.****************, "end": **********.038686, "relative_end": **********.038686, "duration": 0.0012879371643066406, "duration_str": "1.29ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.079668, "relative_start": 0.*****************, "end": **********.124699, "relative_end": **********.124699, "duration": 0.045031070709228516, "duration_str": "45.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: frontend.home.lists", "start": **********.080748, "relative_start": 0.****************, "end": **********.080748, "relative_end": **********.080748, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.conference.creative-grid", "start": **********.08189, "relative_start": 0.****************, "end": **********.08189, "relative_end": **********.08189, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.conference.creative-card", "start": **********.082119, "relative_start": 0.2154679298400879, "end": **********.082119, "relative_end": **********.082119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.084358, "relative_start": 0.21770691871643066, "end": **********.084358, "relative_end": **********.084358, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.08657, "relative_start": 0.21991896629333496, "end": **********.08657, "relative_end": **********.08657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.08847, "relative_start": 0.2218189239501953, "end": **********.08847, "relative_end": **********.08847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.090043, "relative_start": 0.22339200973510742, "end": **********.090043, "relative_end": **********.090043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.092247, "relative_start": 0.22559595108032227, "end": **********.092247, "relative_end": **********.092247, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.093874, "relative_start": 0.22722291946411133, "end": **********.093874, "relative_end": **********.093874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.095317, "relative_start": 0.22866582870483398, "end": **********.095317, "relative_end": **********.095317, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.097631, "relative_start": 0.23097991943359375, "end": **********.097631, "relative_end": **********.097631, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.099265, "relative_start": 0.23261404037475586, "end": **********.099265, "relative_end": **********.099265, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.100697, "relative_start": 0.23404598236083984, "end": **********.100697, "relative_end": **********.100697, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.102526, "relative_start": 0.23587489128112793, "end": **********.102526, "relative_end": **********.102526, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.104288, "relative_start": 0.23763704299926758, "end": **********.104288, "relative_end": **********.104288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.105807, "relative_start": 0.23915600776672363, "end": **********.105807, "relative_end": **********.105807, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.107471, "relative_start": 0.24081993103027344, "end": **********.107471, "relative_end": **********.107471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.109893, "relative_start": 0.2432420253753662, "end": **********.109893, "relative_end": **********.109893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: pagination::tailwind", "start": **********.110259, "relative_start": 0.2436079978942871, "end": **********.110259, "relative_end": **********.110259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.accordion", "start": **********.11602, "relative_start": 0.24936890602111816, "end": **********.11602, "relative_end": **********.11602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.advertisement", "start": **********.117957, "relative_start": 0.25130605697631836, "end": **********.117957, "relative_end": **********.117957, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.layouts.app", "start": **********.119884, "relative_start": 0.2532329559326172, "end": **********.119884, "relative_end": **********.119884, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.navigation", "start": **********.120627, "relative_start": 0.25397586822509766, "end": **********.120627, "relative_end": **********.120627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.friendly-links", "start": **********.123285, "relative_start": 0.256633996963501, "end": **********.123285, "relative_end": **********.123285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 45658168, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.4.2", "Environment": "local", "Debug Mode": "Enabled", "URL": "iconf.lv", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 24, "nb_templates": 24, "templates": [{"name": "frontend.home.lists", "param_count": null, "params": [], "start": **********.080737, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/frontend/home/<USER>", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Ffrontend%2Fhome%2Flists.blade.php&line=1", "ajax": false, "filename": "lists.blade.php", "line": "?"}}, {"name": "components.conference.creative-grid", "param_count": null, "params": [], "start": **********.081883, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/conference/creative-grid.blade.phpcomponents.conference.creative-grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fconference%2Fcreative-grid.blade.php&line=1", "ajax": false, "filename": "creative-grid.blade.php", "line": "?"}}, {"name": "components.conference.creative-card", "param_count": null, "params": [], "start": **********.082109, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/conference/creative-card.blade.phpcomponents.conference.creative-card", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fconference%2Fcreative-card.blade.php&line=1", "ajax": false, "filename": "creative-card.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.084351, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.086562, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.08844, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.090036, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.092238, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.093867, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.09531, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.097623, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.099259, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.10069, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.102518, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.104281, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.1058, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.107461, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.109885, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "pagination::tailwind", "param_count": null, "params": [], "start": **********.110252, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination/resources/views/tailwind.blade.phppagination::tailwind", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2Fresources%2Fviews%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}}, {"name": "components.category.accordion", "param_count": null, "params": [], "start": **********.116012, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/accordion.blade.phpcomponents.category.accordion", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Faccordion.blade.php&line=1", "ajax": false, "filename": "accordion.blade.php", "line": "?"}}, {"name": "components.advertisement", "param_count": null, "params": [], "start": **********.117949, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/advertisement.blade.phpcomponents.advertisement", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fadvertisement.blade.php&line=1", "ajax": false, "filename": "advertisement.blade.php", "line": "?"}}, {"name": "frontend.layouts.app", "param_count": null, "params": [], "start": **********.119876, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/frontend/layouts/app.blade.phpfrontend.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "components.navigation", "param_count": null, "params": [], "start": **********.120619, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/navigation.blade.phpcomponents.navigation", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fnavigation.blade.php&line=1", "ajax": false, "filename": "navigation.blade.php", "line": "?"}}, {"name": "components.friendly-links", "param_count": null, "params": [], "start": **********.123278, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/friendly-links.blade.phpcomponents.friendly-links", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Ffriendly-links.blade.php&line=1", "ajax": false, "filename": "friendly-links.blade.php", "line": "?"}}]}, "queries": {"count": 57, "nb_statements": 56, "nb_visible_statements": 57, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02834, "accumulated_duration_str": "28.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.043134, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'n81JYPLEP0cENw7ZqI8BBaufrhZyV3CgHX5ObznX' limit 1", "type": "query", "params": [], "bindings": ["n81JYPLEP0cENw7ZqI8BBaufrhZyV3CgHX5ObznX"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.043643, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 0, "width_percent": 3.14}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.049401, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 3.14, "width_percent": 0.953}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.050923, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 4.093, "width_percent": 0.423}, {"sql": "select count(*) as aggregate from `event` inner join `list` on `event`.`id` = `list`.`eid` where `status` = 1 and `list`.`cid` in (168, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184)", "type": "query", "params": [], "bindings": [1, 168, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 366}, {"index": 17, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 234}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 82}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.053085, "duration": 0.00262, "duration_str": "2.62ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:366", "source": {"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 366}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=366", "ajax": false, "filename": "HomeService.php", "line": "366"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 4.517, "width_percent": 9.245}, {"sql": "select distinct `event`.* from `event` inner join `list` on `event`.`id` = `list`.`eid` where `status` = 1 and `list`.`cid` in (168, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184) order by `event`.`end_date` desc limit 15 offset 0", "type": "query", "params": [], "bindings": [1, 168, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 366}, {"index": 17, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 234}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 82}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.056058, "duration": 0.01249, "duration_str": "12.49ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:366", "source": {"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 366}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=366", "ajax": false, "filename": "HomeService.php", "line": "366"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 13.761, "width_percent": 44.072}, {"sql": "select `id`, `venue` from `country` where `country`.`id` in (12, 14, 15, 25, 190)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 366}, {"index": 22, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 234}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 82}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.0696378, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:366", "source": {"index": 21, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 366}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=366", "ajax": false, "filename": "HomeService.php", "line": "366"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 57.833, "width_percent": 0.917}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.0706239, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 58.751, "width_percent": 0.741}, {"sql": "select `eid`, `cid` from `list` where `eid` in (5456, 5499, 5492, 5448, 5455, 5508, 5394, 5342, 5390, 5333, 5332, 5312, 5381, 5262, 5063)", "type": "query", "params": [], "bindings": [5456, 5499, 5492, 5448, 5455, 5508, 5394, 5342, 5390, 5333, 5332, 5312, 5381, 5262, 5063], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 436}, {"index": 14, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 369}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 234}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 82}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.071664, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:436", "source": {"index": 13, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 436}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=436", "ajax": false, "filename": "HomeService.php", "line": "436"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 59.492, "width_percent": 0.529}, {"sql": "select distinct `event`.* from `event` inner join `list` on `event`.`id` = `list`.`eid` where `status` = 1 and `is_featured` = 1 and `event`.`end_date` >= ********** and `list`.`cid` in (168, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184) order by `event`.`end_date` asc limit 5", "type": "query", "params": [], "bindings": [1, 1, **********, 168, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 404}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 237}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 82}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.072464, "duration": 0.0024100000000000002, "duration_str": "2.41ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:404", "source": {"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=404", "ajax": false, "filename": "HomeService.php", "line": "404"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 60.021, "width_percent": 8.504}, {"sql": "select `id`, `venue` from `country` where `country`.`id` in (12)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 404}, {"index": 21, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 237}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 82}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.075329, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:404", "source": {"index": 20, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=404", "ajax": false, "filename": "HomeService.php", "line": "404"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 68.525, "width_percent": 0.529}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.07582, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 69.054, "width_percent": 0.494}, {"sql": "select `eid`, `cid` from `list` where `eid` in (5499)", "type": "query", "params": [], "bindings": [5499], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 436}, {"index": 14, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 407}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 237}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 82}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.0766299, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:436", "source": {"index": 13, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 436}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=436", "ajax": false, "filename": "HomeService.php", "line": "436"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 69.548, "width_percent": 0.282}, {"sql": "select * from `ad_txt` where `cid` = 167 and `endtime` >= ********** order by `id` desc limit 5", "type": "query", "params": [], "bindings": [167, **********], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 468}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 240}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 82}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.077281, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:468", "source": {"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 468}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=468", "ajax": false, "filename": "HomeService.php", "line": "468"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 69.831, "width_percent": 1.447}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_seo_setting_conferences_list')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_seo_setting_conferences_list"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.078128, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 71.277, "width_percent": 0.318}, {"sql": "select * from `ad_txt` where `endtime` > ********** and `cid` = 167 order by `listorder` asc, `id` desc limit 5", "type": "query", "params": [], "bindings": [**********, 167], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/AdvertisementService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\AdvertisementService.php", "line": 25}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 85}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.0786421, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "AdvertisementService.php:25", "source": {"index": 15, "namespace": null, "name": "app/Services/AdvertisementService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\AdvertisementService.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FAdvertisementService.php&line=25", "ajax": false, "filename": "AdvertisementService.php", "line": "25"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 71.595, "width_percent": 0.388}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.0826142, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 71.983, "width_percent": 0.953}, {"sql": "select `cid` from `list` where `eid` = 5456", "type": "query", "params": [], "bindings": [5456], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.083656, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 72.936, "width_percent": 0.353}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.084664, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 73.289, "width_percent": 0.459}, {"sql": "select `cid` from `list` where `eid` = 5499", "type": "query", "params": [], "bindings": [5499], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.085693, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 73.747, "width_percent": 1.341}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.08704, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 75.088, "width_percent": 0.776}, {"sql": "select `cid` from `list` where `eid` = 5492", "type": "query", "params": [], "bindings": [5492], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.087923, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 75.865, "width_percent": 0.318}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.0887492, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 76.182, "width_percent": 0.459}, {"sql": "select `cid` from `list` where `eid` = 5448", "type": "query", "params": [], "bindings": [5448], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.089507, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 76.641, "width_percent": 0.423}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.090322, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 77.064, "width_percent": 1.729}, {"sql": "select `cid` from `list` where `eid` = 5455", "type": "query", "params": [], "bindings": [5455], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.0915961, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 78.793, "width_percent": 0.565}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.09257, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 79.358, "width_percent": 0.6}, {"sql": "select `cid` from `list` where `eid` = 5508", "type": "query", "params": [], "bindings": [5508], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.093401, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 79.958, "width_percent": 0.282}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.0941281, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 80.24, "width_percent": 0.388}, {"sql": "select `cid` from `list` where `eid` = 5394", "type": "query", "params": [], "bindings": [5394], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.0948539, "duration": 7.000000000000001e-05, "duration_str": "70μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 80.628, "width_percent": 0.247}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.095579, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 80.875, "width_percent": 0.635}, {"sql": "select `cid` from `list` where `eid` = 5342", "type": "query", "params": [], "bindings": [5342], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.096859, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 81.51, "width_percent": 0.882}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.098007, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 82.392, "width_percent": 0.565}, {"sql": "select `cid` from `list` where `eid` = 5390", "type": "query", "params": [], "bindings": [5390], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.0987978, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 82.957, "width_percent": 0.282}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.099529, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 83.239, "width_percent": 0.353}, {"sql": "select `cid` from `list` where `eid` = 5333", "type": "query", "params": [], "bindings": [5333], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.100241, "duration": 7.000000000000001e-05, "duration_str": "70μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 83.592, "width_percent": 0.247}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.1009572, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 83.839, "width_percent": 0.318}, {"sql": "select `cid` from `list` where `eid` = 5332", "type": "query", "params": [], "bindings": [5332], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.101796, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 84.157, "width_percent": 0.776}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.1028879, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 84.933, "width_percent": 0.812}, {"sql": "select `cid` from `list` where `eid` = 5312", "type": "query", "params": [], "bindings": [5312], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.103789, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 85.745, "width_percent": 0.353}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.104563, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 86.097, "width_percent": 0.423}, {"sql": "select `cid` from `list` where `eid` = 5381", "type": "query", "params": [], "bindings": [5381], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.1053011, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 86.521, "width_percent": 0.318}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.106072, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 86.838, "width_percent": 0.494}, {"sql": "select `cid` from `list` where `eid` = 5262", "type": "query", "params": [], "bindings": [5262], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.106837, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 87.332, "width_percent": 0.353}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.108158, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 87.685, "width_percent": 1.129}, {"sql": "select `cid` from `list` where `eid` = 5063", "type": "query", "params": [], "bindings": [5063], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.109354, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 88.814, "width_percent": 0.423}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.top')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.top"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.111011, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 89.238, "width_percent": 0.388}, {"sql": "select * from `category` where `category`.`fid` = 115 and `category`.`fid` is not null order by `listorder` asc", "type": "query", "params": [], "bindings": [115], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/View/Components/Category/Accordion.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\Accordion.php", "line": 89}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.112003, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Accordion.php:89", "source": {"index": 21, "namespace": null, "name": "app/View/Components/Category/Accordion.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\Accordion.php", "line": 89}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FAccordion.php&line=89", "ajax": false, "filename": "Accordion.php", "line": "89"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 89.626, "width_percent": 0.6}, {"sql": "select * from `category` where `category`.`fid` = 128 and `category`.`fid` is not null order by `listorder` asc", "type": "query", "params": [], "bindings": [128], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/View/Components/Category/Accordion.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\Accordion.php", "line": 89}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.112579, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Accordion.php:89", "source": {"index": 21, "namespace": null, "name": "app/View/Components/Category/Accordion.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\Accordion.php", "line": 89}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FAccordion.php&line=89", "ajax": false, "filename": "Accordion.php", "line": "89"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 90.226, "width_percent": 1.729}, {"sql": "select * from `category` where `category`.`fid` = 142 and `category`.`fid` is not null order by `listorder` asc", "type": "query", "params": [], "bindings": [142], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/View/Components/Category/Accordion.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\Accordion.php", "line": 89}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.113524, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Accordion.php:89", "source": {"index": 21, "namespace": null, "name": "app/View/Components/Category/Accordion.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\Accordion.php", "line": 89}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FAccordion.php&line=89", "ajax": false, "filename": "Accordion.php", "line": "89"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 91.955, "width_percent": 0.635}, {"sql": "select * from `category` where `category`.`fid` = 158 and `category`.`fid` is not null order by `listorder` asc", "type": "query", "params": [], "bindings": [158], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/View/Components/Category/Accordion.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\Accordion.php", "line": 89}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.114101, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Accordion.php:89", "source": {"index": 21, "namespace": null, "name": "app/View/Components/Category/Accordion.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\Accordion.php", "line": 89}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FAccordion.php&line=89", "ajax": false, "filename": "Accordion.php", "line": "89"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 92.59, "width_percent": 0.6}, {"sql": "select * from `category` where `category`.`fid` = 167 and `category`.`fid` is not null order by `listorder` asc", "type": "query", "params": [], "bindings": [167], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/View/Components/Category/Accordion.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\Accordion.php", "line": 89}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.114626, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "Accordion.php:89", "source": {"index": 21, "namespace": null, "name": "app/View/Components/Category/Accordion.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\Accordion.php", "line": 89}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FAccordion.php&line=89", "ajax": false, "filename": "Accordion.php", "line": "89"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 93.19, "width_percent": 0.494}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.1151211, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 93.684, "width_percent": 0.494}, {"sql": "select * from `ad_txt` where `endtime` > ********** and `cid` = 167 order by `listorder` asc, `id` desc limit 5", "type": "query", "params": [], "bindings": [**********, 167], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/AdvertisementService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\AdvertisementService.php", "line": 25}, {"index": 16, "namespace": null, "name": "app/View/Components/Advertisement.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Advertisement.php", "line": 32}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}], "start": **********.117505, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "AdvertisementService.php:25", "source": {"index": 15, "namespace": null, "name": "app/Services/AdvertisementService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\AdvertisementService.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FAdvertisementService.php&line=25", "ajax": false, "filename": "AdvertisementService.php", "line": "25"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 94.178, "width_percent": 0.388}, {"sql": "select * from `html_fragments` where `is_active` = 1 and `type` = 'js'", "type": "query", "params": [], "bindings": [1, "js"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/View/Composers/HtmlFragmentComposer.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Composers\\HtmlFragmentComposer.php", "line": 19}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 189}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160}], "start": **********.1187549, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "HtmlFragmentComposer.php:19", "source": {"index": 15, "namespace": null, "name": "app/View/Composers/HtmlFragmentComposer.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Composers\\HtmlFragmentComposer.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComposers%2FHtmlFragmentComposer.php&line=19", "ajax": false, "filename": "HtmlFragmentComposer.php", "line": "19"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 94.566, "width_percent": 2.717}, {"sql": "select * from `links` where `status` = 1 order by `listorder` asc, `id` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/View/Components/FriendlyLinks.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\FriendlyLinks.php", "line": 23}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 110}, {"index": 17, "namespace": "view", "name": "frontend.layouts.app", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/frontend/layouts/app.blade.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.12243, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "FriendlyLinks.php:23", "source": {"index": 15, "namespace": null, "name": "app/View/Components/FriendlyLinks.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\FriendlyLinks.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FFriendlyLinks.php&line=23", "ajax": false, "filename": "FriendlyLinks.php", "line": "23"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 97.283, "width_percent": 1.729}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_html_fragment_footer')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_html_fragment_footer"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "app/Services/HtmlFragmentService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\HtmlFragmentService.php", "line": 36}], "start": **********.123688, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 99.012, "width_percent": 0.988}]}, "models": {"data": {"App\\Models\\Category": {"value": 63, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Event": {"value": 16, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "App\\Models\\Link": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FLink.php&line=1", "ajax": false, "filename": "Link.php", "line": "?"}}, "App\\Models\\Country": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FCountry.php&line=1", "ajax": false, "filename": "Country.php", "line": "?"}}, "App\\Models\\HtmlFragment": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FHtmlFragment.php&line=1", "ajax": false, "filename": "HtmlFragment.php", "line": "?"}}}, "count": 94, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://iconf.lv/categories/social_sciences_&amp;_humanities", "action_name": "categories.lists", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@lists", "uri": "GET categories/{url}", "controller": "App\\Http\\Controllers\\Frontend\\HomeController@lists<a href=\"phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=78\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=78\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/HomeController.php:78-91</a>", "middleware": "web", "duration": "260ms", "peak_memory": "48MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1915825617 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1915825617\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1851071991 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1851071991\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1264562361 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"719 characters\">XSRF-TOKEN=eyJpdiI6InR6U0xiUVIrMnJYN1NmRXNidENKdXc9PSIsInZhbHVlIjoiQkRIVm9tNTg0Ky9zWjg1bzZDRndyU29JS1ZZVVprT201cXRsTlo2ZmtnempGYnRWSDV4NWt2dHMvZHcydkM0ajhQNi92VEx5dmw0YjJRKzNxR0xOWGgrWXdDcXpxci8yZ2Iwb3NiK2V2VVQvdVNUSmRBVko4MjNpZm1OeWo4S1MiLCJtYWMiOiIyZmRlNmRlYTQ5NDRjMzFjYjMzYmVkMjQ5ZGViYzAxMDY2ZjUzODZhY2YyNjc4OTk3M2MxZDI1OTM4YjRjODYwIiwidGFnIjoiIn0%3D; iconf_meeting_session=eyJpdiI6ImlOdjFFanRaNGswZmxNTFc1RzQ5dmc9PSIsInZhbHVlIjoiZ1NxQVNpSEMwZlk0RjZaRFhKZ2R1UUFsN1dJWFc5ZkN4ejdJL1JBMVlQV3pMaVBZeDZtTTQrbld2OHVjbGF3bjJkY1UySTlXRG52KzlNVGJJZGRrbGd5b2RBUlVpcXlCV3VMRUF0TXNYSmdqYktZTW1FUTl4MWhORVd4Ylh4Q0wiLCJtYWMiOiI3YjkxYTMzOWUzOGYzNDFjN2QzZGE5OGI2MGMxNTU5MzhjMDk2NTE5MWM4YmQ0Y2U4MWM1ZDIwZjA5ODk5YWUzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://iconf.lv/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">iconf.lv</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1264562361\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1573716601 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KzVnHwsb1j7aFLoepnAgBRMhnc1xQgVO8Wwk7tZ4</span>\"\n  \"<span class=sf-dump-key>iconf_meeting_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">n81JYPLEP0cENw7ZqI8BBaufrhZyV3CgHX5ObznX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1573716601\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1957257504 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 16 Jul 2025 11:42:02 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1957257504\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-483166738 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KzVnHwsb1j7aFLoepnAgBRMhnc1xQgVO8Wwk7tZ4</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"15 characters\">http://iconf.lv</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$i.a5gP7/Z07pk3CeR6vNfe9lGm.BtthHlLi1.olj0JJDfdNlCRY8K</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-483166738\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://iconf.lv/categories/social_sciences_&amp;_humanities", "action_name": "categories.lists", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@lists"}, "badge": null}}