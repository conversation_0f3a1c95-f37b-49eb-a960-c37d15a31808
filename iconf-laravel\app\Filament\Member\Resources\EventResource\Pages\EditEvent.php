<?php

namespace App\Filament\Member\Resources\EventResource\Pages;

use App\Filament\Member\Resources\EventResource;
use App\Filament\Concerns\HandlesEventData;
use App\Services\EventUrlService;
use App\Enums\ConferenceStatus;

use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Auth;

class EditEvent extends EditRecord
{
    use HandlesEventData;

    protected static string $resource = EventResource::class;

    protected static ?string $title = 'Edit Conference';

    /**
     * Permission check on page mount
     */
    public function mount(int | string $record): void
    {
        parent::mount($record);

        $member = Auth::guard('member')->user();

        // Check if the user owns this conference
        if ($this->record->uid !== $member->id) {
            abort(403, 'You do not have permission to edit this conference');
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            // View conference details
            Actions\ViewAction::make()
                ->label('View Details'),
            
            // View on site (published conferences only)
            Actions\Action::make('viewOnSite')
                ->label('View on Site')
                ->icon('heroicon-o-eye')
                ->color('info')
                ->url(fn () => url("/conference/{$this->record->url}"))
                ->openUrlInNewTab()
                ->visible(fn () => $this->record->status === ConferenceStatus::Published),
            
            // Resubmit for review (rejected conferences only)
            Actions\Action::make('resubmit')
                ->label('Resubmit for Review')
                ->icon('heroicon-o-arrow-path')
                ->color('warning')
                ->action(function () {
                    $this->record->update(['status' => ConferenceStatus::Pending]);
                    $this->notify('success', 'Resubmitted for review, please wait for admin processing.');
                })
                ->requiresConfirmation()
                ->modalHeading('Resubmit for Review')
                ->modalDescription('Are you sure you want to resubmit this conference for review?')
                ->visible(fn () => $this->record->status === ConferenceStatus::Rejected),
            
            // Delete conference
            Actions\DeleteAction::make()
                ->label('Delete Conference')
                ->requiresConfirmation()
                ->modalHeading('Delete Conference')
                ->modalDescription('Are you sure you want to delete this conference? This action cannot be undone.'),
        ];
    }

    /**
     * Process data before saving the record
     */
    protected function mutateFormDataBeforeSave(array $data): array
    {
        $member = Auth::guard('member')->user();

        // Security check: ensure the current user owns this conference
        if ($this->record->uid !== $member->id) {
            abort(403, 'You do not have permission to edit this conference');
        }

        // Ensure user ID is not modified
        $data['uid'] = $this->record->uid;

        // Process time fields: convert Carbon objects to Unix timestamps
        $data = $this->convertDateTimesToTimestamps($data);

        // Process URL field
        if (empty($data['url']) && !empty($data['event'])) {
            $urlService = app(EventUrlService::class);
            $data['url'] = $urlService->generateUniqueUrl($data['event'], $this->record->id);
        } elseif (!empty($data['url']) && $data['url'] !== $this->record->url) {
            // If the user modifies the URL, ensure it is unique
            $urlService = app(EventUrlService::class);
            $data['url'] = $urlService->generateUniqueUrl($data['url'], $this->record->id);
        }

        // If the conference is re-edited after being rejected, reset to pending status (unless a VIP user)
        if ($this->record->status === ConferenceStatus::Rejected) {
            $data['status'] = $member->vip ? ConferenceStatus::Published : ConferenceStatus::Pending;
        }

        return $data;
    }

    /**
     * Processing after saving the record
     */
    protected function afterSave(): void
    {
        // Synchronize the cid field with the categories association table
        $this->syncCategoriesWithCidField();

        // Note: The list table records have been automatically synchronized through the CheckboxList relationship
        // We just need to ensure that the cid field is consistent with the list table
    }

    /**
     * Notification message after successful save
     */
    protected function getSavedNotificationTitle(): ?string
    {
        $member = Auth::guard('member')->user();
        $originalStatus = $this->record->getOriginal('status');
        $newStatus = $this->record->status;

        // If the status has changed
        if ($originalStatus !== $newStatus->value) {
            if ($newStatus === ConferenceStatus::Pending) {
                return 'Conference has been resubmitted for review';
            } elseif ($newStatus === ConferenceStatus::Published && $member->vip) {
                return 'Conference updated and automatically published successfully';
            }
        }

        return 'Conference updated successfully';
    }

    /**
     * Notification description after successful save
     */
    protected function getSavedNotificationBody(): ?string
    {
        $member = Auth::guard('member')->user();
        $originalStatus = $this->record->getOriginal('status');
        $newStatus = $this->record->status;

        // If the status has changed
        if ($originalStatus !== $newStatus->value) {
            if ($newStatus === ConferenceStatus::Pending) {
                return 'Your conference has been resubmitted for review after modification, please wait for admin processing.';
            } elseif ($newStatus === ConferenceStatus::Published && $member->vip) {
                return 'As a VIP user, your conference modifications have taken effect automatically.';
            }
        }

        return null;
    }

    /**
     * Get form action buttons
     */
    protected function getFormActions(): array
    {
        return [
            $this->getSaveFormAction()
                ->label('Save Changes'),
                
            $this->getCancelFormAction(),
        ];
    }

    /**
     * Custom page subtitle
     */
    public function getSubheading(): ?string
    {
        $status = $this->record->status;
        
        return match($status) {
            ConferenceStatus::Published => 'This conference is published, changes will take effect immediately.',
            ConferenceStatus::Pending => 'This conference is pending review, changes will require re-review.',
            ConferenceStatus::Rejected => 'This conference has been rejected, changes will be resubmitted for review.',
            ConferenceStatus::Draft => 'This conference is in draft status and can be modified at any time.',
            default => 'Edit conference information',
        };
    }

    /**
     * Fill form data
     */
    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Synchronize categories data from the cid field
        $data = $this->syncCategoriesFromCidField($data);

        // Convert Unix timestamps to Carbon objects for use by the DateTimePicker
        if (isset($data['start_date']) && $data['start_date']) {
            $data['start_date'] = \Carbon\Carbon::createFromTimestamp($data['start_date']);
        }

        if (isset($data['end_date']) && $data['end_date']) {
            $data['end_date'] = \Carbon\Carbon::createFromTimestamp($data['end_date']);
        }

        if (isset($data['sub_date']) && $data['sub_date']) {
            $data['sub_date'] = \Carbon\Carbon::createFromTimestamp($data['sub_date']);
        }

        return $data;
    }

}
