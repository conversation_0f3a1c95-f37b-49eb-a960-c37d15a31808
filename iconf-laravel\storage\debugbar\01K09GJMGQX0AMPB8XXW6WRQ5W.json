{"__meta": {"id": "01K09GJMGQX0AMPB8XXW6WRQ5W", "datetime": "2025-07-16 11:40:34", "utime": **********.711651, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.029744, "end": **********.711659, "duration": 0.6819150447845459, "duration_str": "682ms", "measures": [{"label": "Booting", "start": **********.029744, "relative_start": 0, "end": **********.181948, "relative_end": **********.181948, "duration": 0.*****************, "duration_str": "152ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.181954, "relative_start": 0.*****************, "end": **********.71166, "relative_end": 9.5367431640625e-07, "duration": 0.****************, "duration_str": "530ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.188932, "relative_start": 0.*****************, "end": **********.190461, "relative_end": **********.190461, "duration": 0.0015289783477783203, "duration_str": "1.53ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.699171, "relative_start": 0.****************, "end": **********.710827, "relative_end": **********.710827, "duration": 0.011656045913696289, "duration_str": "11.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: frontend.home.index", "start": **********.700412, "relative_start": 0.****************, "end": **********.700412, "relative_end": **********.700412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.top-list", "start": **********.703126, "relative_start": 0.***************, "end": **********.703126, "relative_end": **********.703126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.global-conference-distribution", "start": **********.704768, "relative_start": 0.6750240325927734, "end": **********.704768, "relative_end": **********.704768, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.layouts.app", "start": **********.706585, "relative_start": 0.6768410205841064, "end": **********.706585, "relative_end": **********.706585, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.navigation", "start": **********.707124, "relative_start": 0.6773800849914551, "end": **********.707124, "relative_end": **********.707124, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.friendly-links", "start": **********.709927, "relative_start": 0.6801831722259521, "end": **********.709927, "relative_end": **********.709927, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 45820352, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.4.2", "Environment": "local", "Debug Mode": "Enabled", "URL": "iconf.lv", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 6, "nb_templates": 6, "templates": [{"name": "frontend.home.index", "param_count": null, "params": [], "start": **********.700401, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/frontend/home/<USER>", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Ffrontend%2Fhome%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "components.category.top-list", "param_count": null, "params": [], "start": **********.703118, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/top-list.blade.phpcomponents.category.top-list", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Ftop-list.blade.php&line=1", "ajax": false, "filename": "top-list.blade.php", "line": "?"}}, {"name": "components.global-conference-distribution", "param_count": null, "params": [], "start": **********.704761, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/global-conference-distribution.blade.phpcomponents.global-conference-distribution", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fglobal-conference-distribution.blade.php&line=1", "ajax": false, "filename": "global-conference-distribution.blade.php", "line": "?"}}, {"name": "frontend.layouts.app", "param_count": null, "params": [], "start": **********.706578, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/frontend/layouts/app.blade.phpfrontend.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "components.navigation", "param_count": null, "params": [], "start": **********.707117, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/navigation.blade.phpcomponents.navigation", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fnavigation.blade.php&line=1", "ajax": false, "filename": "navigation.blade.php", "line": "?"}}, {"name": "components.friendly-links", "param_count": null, "params": [], "start": **********.709921, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/friendly-links.blade.phpcomponents.friendly-links", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Ffriendly-links.blade.php&line=1", "ajax": false, "filename": "friendly-links.blade.php", "line": "?"}}]}, "queries": {"count": 55, "nb_statements": 54, "nb_visible_statements": 55, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.47396, "accumulated_duration_str": "474ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.194512, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'n81JYPLEP0cENw7ZqI8BBaufrhZyV3CgHX5ObznX' limit 1", "type": "query", "params": [], "bindings": ["n81JYPLEP0cENw7ZqI8BBaufrhZyV3CgHX5ObznX"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.1950328, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 0, "width_percent": 0.23}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.top')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.top"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.199417, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 0.23, "width_percent": 0.025}, {"sql": "select * from `event` where `status` = 1 and `end_date` >= 1755344434 order by `ding` desc, `end_date` asc limit 7", "type": "query", "params": [], "bindings": [1, 1755344434], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 122}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 59}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 49}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.2009761, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:122", "source": {"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=122", "ajax": false, "filename": "HomeService.php", "line": "122"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 0.255, "width_percent": 0.428}, {"sql": "select `id`, `venue` from `country` where `country`.`id` in (12, 57, 146)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 122}, {"index": 21, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 59}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 49}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.203844, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:122", "source": {"index": 20, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=122", "ajax": false, "filename": "HomeService.php", "line": "122"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 0.684, "width_percent": 0.074}, {"sql": "select `category`.`id`, `category`.`name`, `list`.`eid` as `pivot_eid`, `list`.`cid` as `pivot_cid` from `category` inner join `list` on `category`.`id` = `list`.`cid` where `list`.`eid` in (4838, 4896, 4908, 4932, 4933, 5003, 5178)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 122}, {"index": 20, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 59}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 49}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.205731, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:122", "source": {"index": 19, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=122", "ajax": false, "filename": "HomeService.php", "line": "122"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 0.757, "width_percent": 0.08}, {"sql": "select * from `event` where `status` = 1 and `push` = 1 order by `id` desc limit 8", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 138}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 62}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 49}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.207009, "duration": 0.0023, "duration_str": "2.3ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:138", "source": {"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=138", "ajax": false, "filename": "HomeService.php", "line": "138"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 0.838, "width_percent": 0.485}, {"sql": "select `id`, `venue` from `country` where `country`.`id` in (12, 57, 72, 85, 95, 129)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 138}, {"index": 21, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 62}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 49}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.2096741, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:138", "source": {"index": 20, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=138", "ajax": false, "filename": "HomeService.php", "line": "138"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 1.323, "width_percent": 0.027}, {"sql": "select `category`.`id`, `category`.`name`, `list`.`eid` as `pivot_eid`, `list`.`cid` as `pivot_cid` from `category` inner join `list` on `category`.`id` = `list`.`cid` where `list`.`eid` in (4681, 4920, 5021, 5032, 5147, 5148, 5223, 5256)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 138}, {"index": 20, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 62}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 49}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.210178, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:138", "source": {"index": 19, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=138", "ajax": false, "filename": "HomeService.php", "line": "138"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 1.35, "width_percent": 0.036}, {"sql": "select * from `ad_txt` where `cid` = 0 and `endtime` >= **********", "type": "query", "params": [], "bindings": [0, **********], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 165}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 65}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 49}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.211112, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:165", "source": {"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 165}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=165", "ajax": false, "filename": "HomeService.php", "line": "165"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 1.386, "width_percent": 0.07}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_countries_with_event_count')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_countries_with_event_count"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.2117732, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 1.456, "width_percent": 0.021}, {"sql": "delete from `cache` where `key` in ('iconf_meeting_cache_countries_with_event_count', 'iconf_meeting_cache_illuminate:cache:flexible:created:countries_with_event_count') and `expiration` <= **********", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_countries_with_event_count", "iconf_meeting_cache_illuminate:cache:flexible:created:countries_with_event_count", **********], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 411}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 145}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}], "start": **********.21225, "duration": 0.01893, "duration_str": "18.93ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:411", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=411", "ajax": false, "filename": "DatabaseStore.php", "line": "411"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 1.477, "width_percent": 3.994}, {"sql": "select `id`, `venue`, `url`, (select count(*) from `event` where `country`.`id` = `event`.`venue`) as `events_count` from `country` where `fid` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/CountryService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\CountryService.php", "line": 402}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 19, "namespace": null, "name": "app/Services/CountryService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\CountryService.php", "line": 397}, {"index": 20, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 176}], "start": **********.232513, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "CountryService.php:402", "source": {"index": 15, "namespace": null, "name": "app/Services/CountryService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\CountryService.php", "line": 402}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FCountryService.php&line=402", "ajax": false, "filename": "CountryService.php", "line": "402"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 5.471, "width_percent": 0.051}, {"sql": "select `id`, `fid` from `country`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/CountryService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\CountryService.php", "line": 405}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 19, "namespace": null, "name": "app/Services/CountryService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\CountryService.php", "line": 397}, {"index": 20, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 176}], "start": **********.2330852, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "CountryService.php:405", "source": {"index": 15, "namespace": null, "name": "app/Services/CountryService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\CountryService.php", "line": 405}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FCountryService.php&line=405", "ajax": false, "filename": "CountryService.php", "line": "405"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 5.522, "width_percent": 0.021}, {"sql": "select venue, count(*) as total from `event` where `venue` in (15, 11, 12, 13, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 191, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190) group by `venue`", "type": "query", "params": [], "bindings": [15, 11, 12, 13, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 191, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Services/CountryService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\CountryService.php", "line": 414}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 18, "namespace": null, "name": "app/Services/CountryService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\CountryService.php", "line": 397}, {"index": 19, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 176}], "start": **********.235542, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "CountryService.php:414", "source": {"index": 14, "namespace": null, "name": "app/Services/CountryService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\CountryService.php", "line": 414}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FCountryService.php&line=414", "ajax": false, "filename": "CountryService.php", "line": "414"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 5.543, "width_percent": 0.371}, {"sql": "select count(*) as aggregate from `event` where `event`.`venue` = 11 and `event`.`venue` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Country.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Models\\Country.php", "line": 177}, {"index": 26, "namespace": null, "name": "app/Services/CountryService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\CountryService.php", "line": 418}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 30, "namespace": null, "name": "app/Services/CountryService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\CountryService.php", "line": 397}], "start": **********.237906, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "Country.php:177", "source": {"index": 19, "namespace": null, "name": "app/Models/Country.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Models\\Country.php", "line": 177}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FCountry.php&line=177", "ajax": false, "filename": "Country.php", "line": "177"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 5.914, "width_percent": 0.032}, {"sql": "select count(*) as aggregate from `event` where `event`.`venue` = 59 and `event`.`venue` is not null", "type": "query", "params": [], "bindings": [59], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Country.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Models\\Country.php", "line": 177}, {"index": 26, "namespace": null, "name": "app/Services/CountryService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\CountryService.php", "line": 418}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 30, "namespace": null, "name": "app/Services/CountryService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\CountryService.php", "line": 397}], "start": **********.238548, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "Country.php:177", "source": {"index": 19, "namespace": null, "name": "app/Models/Country.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Models\\Country.php", "line": 177}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FCountry.php&line=177", "ajax": false, "filename": "Country.php", "line": "177"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 5.946, "width_percent": 0.025}, {"sql": "select count(*) as aggregate from `event` where `event`.`venue` = 102 and `event`.`venue` is not null", "type": "query", "params": [], "bindings": [102], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Country.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Models\\Country.php", "line": 177}, {"index": 26, "namespace": null, "name": "app/Services/CountryService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\CountryService.php", "line": 418}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 30, "namespace": null, "name": "app/Services/CountryService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\CountryService.php", "line": 397}], "start": **********.239081, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "Country.php:177", "source": {"index": 19, "namespace": null, "name": "app/Models/Country.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Models\\Country.php", "line": 177}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FCountry.php&line=177", "ajax": false, "filename": "Country.php", "line": "177"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 5.971, "width_percent": 0.017}, {"sql": "select count(*) as aggregate from `event` where `event`.`venue` = 128 and `event`.`venue` is not null", "type": "query", "params": [], "bindings": [128], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Country.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Models\\Country.php", "line": 177}, {"index": 26, "namespace": null, "name": "app/Services/CountryService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\CountryService.php", "line": 418}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 30, "namespace": null, "name": "app/Services/CountryService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\CountryService.php", "line": 397}], "start": **********.239516, "duration": 5.9999999999999995e-05, "duration_str": "60μs", "memory": 0, "memory_str": null, "filename": "Country.php:177", "source": {"index": 19, "namespace": null, "name": "app/Models/Country.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Models\\Country.php", "line": 177}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FCountry.php&line=177", "ajax": false, "filename": "Country.php", "line": "177"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 5.988, "width_percent": 0.013}, {"sql": "select count(*) as aggregate from `event` where `event`.`venue` = 145 and `event`.`venue` is not null", "type": "query", "params": [], "bindings": [145], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Country.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Models\\Country.php", "line": 177}, {"index": 26, "namespace": null, "name": "app/Services/CountryService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\CountryService.php", "line": 418}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 30, "namespace": null, "name": "app/Services/CountryService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\CountryService.php", "line": 397}], "start": **********.239837, "duration": 5.9999999999999995e-05, "duration_str": "60μs", "memory": 0, "memory_str": null, "filename": "Country.php:177", "source": {"index": 19, "namespace": null, "name": "app/Models/Country.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Models\\Country.php", "line": 177}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FCountry.php&line=177", "ajax": false, "filename": "Country.php", "line": "177"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 6.001, "width_percent": 0.013}, {"sql": "select count(*) as aggregate from `event` where `event`.`venue` = 176 and `event`.`venue` is not null", "type": "query", "params": [], "bindings": [176], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Country.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Models\\Country.php", "line": 177}, {"index": 26, "namespace": null, "name": "app/Services/CountryService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\CountryService.php", "line": 418}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 30, "namespace": null, "name": "app/Services/CountryService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\CountryService.php", "line": 397}], "start": **********.24027, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Country.php:177", "source": {"index": 19, "namespace": null, "name": "app/Models/Country.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Models\\Country.php", "line": 177}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FCountry.php&line=177", "ajax": false, "filename": "Country.php", "line": "177"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 6.013, "width_percent": 0.044}, {"sql": "insert into `cache` (`expiration`, `key`, `value`) values (1752669634, 'iconf_meeting_cache_countries_with_event_count', 'O:39:\\\"Illuminate\\Database\\Eloquent\\Collection\\\":2:{s:8:\\\"?*?items\\\";a:6:{i:0;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:5:{s:2:\\\"id\\\";i:11;s:5:\\\"venue\\\";s:4:\\\"Asia\\\";s:3:\\\"url\\\";s:4:\\\"asia\\\";s:12:\\\"events_count\\\";i:0;s:18:\\\"total_events_count\\\";i:4118;}s:11:\\\"?*?original\\\";a:4:{s:2:\\\"id\\\";i:11;s:5:\\\"venue\\\";s:4:\\\"Asia\\\";s:3:\\\"url\\\";s:4:\\\"asia\\\";s:12:\\\"events_count\\\";i:0;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}i:1;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:5:{s:2:\\\"id\\\";i:59;s:5:\\\"venue\\\";s:8:\\\"European\\\";s:3:\\\"url\\\";s:8:\\\"european\\\";s:12:\\\"events_count\\\";i:0;s:18:\\\"total_events_count\\\";i:939;}s:11:\\\"?*?original\\\";a:4:{s:2:\\\"id\\\";i:59;s:5:\\\"venue\\\";s:8:\\\"European\\\";s:3:\\\"url\\\";s:8:\\\"european\\\";s:12:\\\"events_count\\\";i:0;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}i:2;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:5:{s:2:\\\"id\\\";i:102;s:5:\\\"venue\\\";s:6:\\\"Africa\\\";s:3:\\\"url\\\";s:6:\\\"africa\\\";s:12:\\\"events_count\\\";i:1;s:18:\\\"total_events_count\\\";i:61;}s:11:\\\"?*?original\\\";a:4:{s:2:\\\"id\\\";i:102;s:5:\\\"venue\\\";s:6:\\\"Africa\\\";s:3:\\\"url\\\";s:6:\\\"africa\\\";s:12:\\\"events_count\\\";i:1;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}i:3;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:5:{s:2:\\\"id\\\";i:128;s:5:\\\"venue\\\";s:7:\\\"Oceania\\\";s:3:\\\"url\\\";s:7:\\\"oceania\\\";s:12:\\\"events_count\\\";i:0;s:18:\\\"total_events_count\\\";i:100;}s:11:\\\"?*?original\\\";a:4:{s:2:\\\"id\\\";i:128;s:5:\\\"venue\\\";s:7:\\\"Oceania\\\";s:3:\\\"url\\\";s:7:\\\"oceania\\\";s:12:\\\"events_count\\\";i:0;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}i:4;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:5:{s:2:\\\"id\\\";i:145;s:5:\\\"venue\\\";s:13:\\\"North America\\\";s:3:\\\"url\\\";s:13:\\\"north_america\\\";s:12:\\\"events_count\\\";i:0;s:18:\\\"total_events_count\\\";i:177;}s:11:\\\"?*?original\\\";a:4:{s:2:\\\"id\\\";i:145;s:5:\\\"venue\\\";s:13:\\\"North America\\\";s:3:\\\"url\\\";s:13:\\\"north_america\\\";s:12:\\\"events_count\\\";i:0;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}i:5;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:5:{s:2:\\\"id\\\";i:176;s:5:\\\"venue\\\";s:13:\\\"South America\\\";s:3:\\\"url\\\";s:13:\\\"south_america\\\";s:12:\\\"events_count\\\";i:0;s:18:\\\"total_events_count\\\";i:9;}s:11:\\\"?*?original\\\";a:4:{s:2:\\\"id\\\";i:176;s:5:\\\"venue\\\";s:13:\\\"South America\\\";s:3:\\\"url\\\";s:13:\\\"south_america\\\";s:12:\\\"events_count\\\";i:0;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}}s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;}') on duplicate key update `expiration` = values(`expiration`), `key` = values(`key`), `value` = values(`value`)", "type": "query", "params": [], "bindings": [1752669634, "iconf_meeting_cache_countries_with_event_count", "O:39:\"Illuminate\\Database\\Eloquent\\Collection\":2:{s:8:\"\u0000*\u0000items\";a:6:{i:0;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:5:{s:2:\"id\";i:11;s:5:\"venue\";s:4:\"Asia\";s:3:\"url\";s:4:\"asia\";s:12:\"events_count\";i:0;s:18:\"total_events_count\";i:4118;}s:11:\"\u0000*\u0000original\";a:4:{s:2:\"id\";i:11;s:5:\"venue\";s:4:\"Asia\";s:3:\"url\";s:4:\"asia\";s:12:\"events_count\";i:0;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}i:1;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:5:{s:2:\"id\";i:59;s:5:\"venue\";s:8:\"European\";s:3:\"url\";s:8:\"european\";s:12:\"events_count\";i:0;s:18:\"total_events_count\";i:939;}s:11:\"\u0000*\u0000original\";a:4:{s:2:\"id\";i:59;s:5:\"venue\";s:8:\"European\";s:3:\"url\";s:8:\"european\";s:12:\"events_count\";i:0;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}i:2;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:5:{s:2:\"id\";i:102;s:5:\"venue\";s:6:\"Africa\";s:3:\"url\";s:6:\"africa\";s:12:\"events_count\";i:1;s:18:\"total_events_count\";i:61;}s:11:\"\u0000*\u0000original\";a:4:{s:2:\"id\";i:102;s:5:\"venue\";s:6:\"Africa\";s:3:\"url\";s:6:\"africa\";s:12:\"events_count\";i:1;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}i:3;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:5:{s:2:\"id\";i:128;s:5:\"venue\";s:7:\"Oceania\";s:3:\"url\";s:7:\"oceania\";s:12:\"events_count\";i:0;s:18:\"total_events_count\";i:100;}s:11:\"\u0000*\u0000original\";a:4:{s:2:\"id\";i:128;s:5:\"venue\";s:7:\"Oceania\";s:3:\"url\";s:7:\"oceania\";s:12:\"events_count\";i:0;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}i:4;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:5:{s:2:\"id\";i:145;s:5:\"venue\";s:13:\"North America\";s:3:\"url\";s:13:\"north_america\";s:12:\"events_count\";i:0;s:18:\"total_events_count\";i:177;}s:11:\"\u0000*\u0000original\";a:4:{s:2:\"id\";i:145;s:5:\"venue\";s:13:\"North America\";s:3:\"url\";s:13:\"north_america\";s:12:\"events_count\";i:0;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}i:5;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:5:{s:2:\"id\";i:176;s:5:\"venue\";s:13:\"South America\";s:3:\"url\";s:13:\"south_america\";s:12:\"events_count\";i:0;s:18:\"total_events_count\";i:9;}s:11:\"\u0000*\u0000original\";a:4:{s:2:\"id\";i:176;s:5:\"venue\";s:13:\"South America\";s:3:\"url\";s:13:\"south_america\";s:12:\"events_count\";i:0;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}}s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;}"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 191}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 167}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 241}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 433}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.240995, "duration": 0.02368, "duration_str": "23.68ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:191", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 191}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=191", "ajax": false, "filename": "DatabaseStore.php", "line": "191"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 6.057, "width_percent": 4.996}, {"sql": "select count(*) as aggregate from `news`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 188}, {"index": 17, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 71}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 49}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.265464, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:188", "source": {"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=188", "ajax": false, "filename": "HomeService.php", "line": "188"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 11.054, "width_percent": 0.076}, {"sql": "select * from `news` order by `id` desc limit 8 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 188}, {"index": 17, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 71}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 49}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.266176, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:188", "source": {"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=188", "ajax": false, "filename": "HomeService.php", "line": "188"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 11.13, "width_percent": 0.152}, {"sql": "select * from `news` where `is_featured` = 1 and `cover` is not null and `cover` != '' order by `id` desc limit 3", "type": "query", "params": [], "bindings": [1, ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 204}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 49}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.2674968, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:204", "source": {"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 204}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=204", "ajax": false, "filename": "HomeService.php", "line": "204"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 11.282, "width_percent": 0.498}, {"sql": "select * from `event` where `status` = 1 order by `addtime` desc limit 8", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 153}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 77}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 49}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.270226, "duration": 0.01038, "duration_str": "10.38ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:153", "source": {"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 153}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=153", "ajax": false, "filename": "HomeService.php", "line": "153"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 11.779, "width_percent": 2.19}, {"sql": "select `id`, `venue` from `country` where `country`.`id` in (12, 15, 17, 57, 102)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 153}, {"index": 21, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 77}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 49}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.281004, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:153", "source": {"index": 20, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 153}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=153", "ajax": false, "filename": "HomeService.php", "line": "153"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 13.97, "width_percent": 0.027}, {"sql": "select `category`.`id`, `category`.`name`, `list`.`eid` as `pivot_eid`, `list`.`cid` as `pivot_cid` from `category` inner join `list` on `category`.`id` = `list`.`cid` where `list`.`eid` in (4920, 4921, 5085, 5147, 5389, 5508, 5509, 5515)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 153}, {"index": 20, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 77}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 49}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.281528, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:153", "source": {"index": 19, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 153}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=153", "ajax": false, "filename": "HomeService.php", "line": "153"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 13.997, "width_percent": 0.03}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_seo_setting_home')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_seo_setting_home"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.282403, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 14.027, "width_percent": 0.03}, {"sql": "delete from `cache` where `key` in ('iconf_meeting_cache_seo_setting_home', 'iconf_meeting_cache_illuminate:cache:flexible:created:seo_setting_home') and `expiration` <= **********", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_seo_setting_home", "iconf_meeting_cache_illuminate:cache:flexible:created:seo_setting_home", **********], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 411}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 145}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}], "start": **********.283026, "duration": 0.03403, "duration_str": "34.03ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:411", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=411", "ajax": false, "filename": "DatabaseStore.php", "line": "411"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 14.056, "width_percent": 7.18}, {"sql": "select * from `seo_settings` where `page_type` = 'home' limit 1", "type": "query", "params": [], "bindings": ["home"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/SeoSetting.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Models\\SeoSetting.php", "line": 51}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 20, "namespace": null, "name": "app/Models/SeoSetting.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Models\\SeoSetting.php", "line": 48}, {"index": 21, "namespace": null, "name": "app/Services/SeoService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\SeoService.php", "line": 14}], "start": **********.31757, "duration": 0.0253, "duration_str": "25.3ms", "memory": 0, "memory_str": null, "filename": "SeoSetting.php:51", "source": {"index": 16, "namespace": null, "name": "app/Models/SeoSetting.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Models\\SeoSetting.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FSeoSetting.php&line=51", "ajax": false, "filename": "SeoSetting.php", "line": "51"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 21.236, "width_percent": 5.338}, {"sql": "insert into `cache` (`expiration`, `key`, `value`) values (1752669634, 'iconf_meeting_cache_seo_setting_home', 'O:21:\\\"App\\Models\\SeoSetting\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:12:\\\"seo_settings\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:8:{s:2:\\\"id\\\";i:2;s:9:\\\"page_type\\\";s:4:\\\"home\\\";s:5:\\\"label\\\";s:6:\\\"首页\\\";s:5:\\\"title\\\";s:72:\\\"ICONF | Academic Conferences Find – Upcoming International Conferences\\\";s:8:\\\"keywords\\\";s:254:\\\"academic conferences,international conferences,call for papers,cfp,conference list,conference calendar,conference alerts,conference papers,conference papers submission,scopus conferences,ieee conferences,ai conferences,it conferences,flagship conferences\\\";s:11:\\\"description\\\";s:174:\\\"Iconf offers everyone the latest academic conference inquiries,international academic conferences,and provides submission methods to comprehensively solve your problems-iconf\\\";s:10:\\\"created_at\\\";s:19:\\\"2025-07-16 18:03:54\\\";s:10:\\\"updated_at\\\";s:19:\\\"2025-07-16 18:03:54\\\";}s:11:\\\"?*?original\\\";a:8:{s:2:\\\"id\\\";i:2;s:9:\\\"page_type\\\";s:4:\\\"home\\\";s:5:\\\"label\\\";s:6:\\\"首页\\\";s:5:\\\"title\\\";s:72:\\\"ICONF | Academic Conferences Find – Upcoming International Conferences\\\";s:8:\\\"keywords\\\";s:254:\\\"academic conferences,international conferences,call for papers,cfp,conference list,conference calendar,conference alerts,conference papers,conference papers submission,scopus conferences,ieee conferences,ai conferences,it conferences,flagship conferences\\\";s:11:\\\"description\\\";s:174:\\\"Iconf offers everyone the latest academic conference inquiries,international academic conferences,and provides submission methods to comprehensively solve your problems-iconf\\\";s:10:\\\"created_at\\\";s:19:\\\"2025-07-16 18:03:54\\\";s:10:\\\"updated_at\\\";s:19:\\\"2025-07-16 18:03:54\\\";}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:0:{}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:1;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:5:{i:0;s:9:\\\"page_type\\\";i:1;s:5:\\\"label\\\";i:2;s:5:\\\"title\\\";i:3;s:8:\\\"keywords\\\";i:4;s:11:\\\"description\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}') on duplicate key update `expiration` = values(`expiration`), `key` = values(`key`), `value` = values(`value`)", "type": "query", "params": [], "bindings": [1752669634, "iconf_meeting_cache_seo_setting_home", "O:21:\"App\\Models\\SeoSetting\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:12:\"seo_settings\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:8:{s:2:\"id\";i:2;s:9:\"page_type\";s:4:\"home\";s:5:\"label\";s:6:\"首页\";s:5:\"title\";s:72:\"ICONF | Academic Conferences Find – Upcoming International Conferences\";s:8:\"keywords\";s:254:\"academic conferences,international conferences,call for papers,cfp,conference list,conference calendar,conference alerts,conference papers,conference papers submission,scopus conferences,ieee conferences,ai conferences,it conferences,flagship conferences\";s:11:\"description\";s:174:\"Iconf offers everyone the latest academic conference inquiries,international academic conferences,and provides submission methods to comprehensively solve your problems-iconf\";s:10:\"created_at\";s:19:\"2025-07-16 18:03:54\";s:10:\"updated_at\";s:19:\"2025-07-16 18:03:54\";}s:11:\"\u0000*\u0000original\";a:8:{s:2:\"id\";i:2;s:9:\"page_type\";s:4:\"home\";s:5:\"label\";s:6:\"首页\";s:5:\"title\";s:72:\"ICONF | Academic Conferences Find – Upcoming International Conferences\";s:8:\"keywords\";s:254:\"academic conferences,international conferences,call for papers,cfp,conference list,conference calendar,conference alerts,conference papers,conference papers submission,scopus conferences,ieee conferences,ai conferences,it conferences,flagship conferences\";s:11:\"description\";s:174:\"Iconf offers everyone the latest academic conference inquiries,international academic conferences,and provides submission methods to comprehensively solve your problems-iconf\";s:10:\"created_at\";s:19:\"2025-07-16 18:03:54\";s:10:\"updated_at\";s:19:\"2025-07-16 18:03:54\";}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:0:{}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:5:{i:0;s:9:\"page_type\";i:1;s:5:\"label\";i:2;s:5:\"title\";i:3;s:8:\"keywords\";i:4;s:11:\"description\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 191}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 167}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 241}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 433}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.34342, "duration": 0.02926, "duration_str": "29.26ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:191", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 191}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=191", "ajax": false, "filename": "DatabaseStore.php", "line": "191"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 26.574, "width_percent": 6.174}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_top_countries_by_conferences_5')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_top_countries_by_conferences_5"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.373092, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 32.747, "width_percent": 0.034}, {"sql": "delete from `cache` where `key` in ('iconf_meeting_cache_top_countries_by_conferences_5', 'iconf_meeting_cache_illuminate:cache:flexible:created:top_countries_by_conferences_5') and `expiration` <= **********", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_top_countries_by_conferences_5", "iconf_meeting_cache_illuminate:cache:flexible:created:top_countries_by_conferences_5", **********], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 411}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 145}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}], "start": **********.373716, "duration": 0.024149999999999998, "duration_str": "24.15ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:411", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=411", "ajax": false, "filename": "DatabaseStore.php", "line": "411"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 32.781, "width_percent": 5.095}, {"sql": "select `country`.*, COUNT(event.id) as conference_count from `country` left join `event` on `country`.`id` = `event`.`venue` and `event`.`status` = 1 where `country`.`fid` != 0 group by `country`.`id` having `conference_count` > 0 order by `conference_count` desc limit 5", "type": "query", "params": [], "bindings": [1, 0, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/Frontend/ConferenceStatisticsService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\ConferenceStatisticsService.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 19, "namespace": null, "name": "app/Services/Frontend/ConferenceStatisticsService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\ConferenceStatisticsService.php", "line": 32}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 52}], "start": **********.3983212, "duration": 0.014150000000000001, "duration_str": "14.15ms", "memory": 0, "memory_str": null, "filename": "ConferenceStatisticsService.php:44", "source": {"index": 15, "namespace": null, "name": "app/Services/Frontend/ConferenceStatisticsService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\ConferenceStatisticsService.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FConferenceStatisticsService.php&line=44", "ajax": false, "filename": "ConferenceStatisticsService.php", "line": "44"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 37.877, "width_percent": 2.985}, {"sql": "insert into `cache` (`expiration`, `key`, `value`) values (1752666094, 'iconf_meeting_cache_top_countries_by_conferences_5', 'O:39:\\\"Illuminate\\Database\\Eloquent\\Collection\\\":2:{s:8:\\\"?*?items\\\";a:5:{i:0;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:6:{s:2:\\\"id\\\";i:12;s:9:\\\"listorder\\\";i:12;s:5:\\\"venue\\\";s:5:\\\"China\\\";s:3:\\\"url\\\";s:5:\\\"china\\\";s:3:\\\"fid\\\";i:11;s:16:\\\"conference_count\\\";i:1693;}s:11:\\\"?*?original\\\";a:6:{s:2:\\\"id\\\";i:12;s:9:\\\"listorder\\\";i:12;s:5:\\\"venue\\\";s:5:\\\"China\\\";s:3:\\\"url\\\";s:5:\\\"china\\\";s:3:\\\"fid\\\";i:11;s:16:\\\"conference_count\\\";i:1693;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}i:1;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:6:{s:2:\\\"id\\\";i:15;s:9:\\\"listorder\\\";i:15;s:5:\\\"venue\\\";s:5:\\\"Japan\\\";s:3:\\\"url\\\";s:5:\\\"japan\\\";s:3:\\\"fid\\\";i:11;s:16:\\\"conference_count\\\";i:720;}s:11:\\\"?*?original\\\";a:6:{s:2:\\\"id\\\";i:15;s:9:\\\"listorder\\\";i:15;s:5:\\\"venue\\\";s:5:\\\"Japan\\\";s:3:\\\"url\\\";s:5:\\\"japan\\\";s:3:\\\"fid\\\";i:11;s:16:\\\"conference_count\\\";i:720;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}i:2;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:6:{s:2:\\\"id\\\";i:24;s:9:\\\"listorder\\\";i:24;s:5:\\\"venue\\\";s:9:\\\"Singapore\\\";s:3:\\\"url\\\";s:9:\\\"singapore\\\";s:3:\\\"fid\\\";i:11;s:16:\\\"conference_count\\\";i:273;}s:11:\\\"?*?original\\\";a:6:{s:2:\\\"id\\\";i:24;s:9:\\\"listorder\\\";i:24;s:5:\\\"venue\\\";s:9:\\\"Singapore\\\";s:3:\\\"url\\\";s:9:\\\"singapore\\\";s:3:\\\"fid\\\";i:11;s:16:\\\"conference_count\\\";i:273;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}i:3;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:6:{s:2:\\\"id\\\";i:21;s:9:\\\"listorder\\\";i:21;s:5:\\\"venue\\\";s:8:\\\"Thailand\\\";s:3:\\\"url\\\";s:8:\\\"thailand\\\";s:3:\\\"fid\\\";i:11;s:16:\\\"conference_count\\\";i:184;}s:11:\\\"?*?original\\\";a:6:{s:2:\\\"id\\\";i:21;s:9:\\\"listorder\\\";i:21;s:5:\\\"venue\\\";s:8:\\\"Thailand\\\";s:3:\\\"url\\\";s:8:\\\"thailand\\\";s:3:\\\"fid\\\";i:11;s:16:\\\"conference_count\\\";i:184;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}i:4;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:6:{s:2:\\\"id\\\";i:190;s:9:\\\"listorder\\\";i:190;s:5:\\\"venue\\\";s:18:\\\"The United Kingdom\\\";s:3:\\\"url\\\";s:18:\\\"the_united_kingdom\\\";s:3:\\\"fid\\\";i:59;s:16:\\\"conference_count\\\";i:160;}s:11:\\\"?*?original\\\";a:6:{s:2:\\\"id\\\";i:190;s:9:\\\"listorder\\\";i:190;s:5:\\\"venue\\\";s:18:\\\"The United Kingdom\\\";s:3:\\\"url\\\";s:18:\\\"the_united_kingdom\\\";s:3:\\\"fid\\\";i:59;s:16:\\\"conference_count\\\";i:160;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}}s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;}') on duplicate key update `expiration` = values(`expiration`), `key` = values(`key`), `value` = values(`value`)", "type": "query", "params": [], "bindings": [1752666094, "iconf_meeting_cache_top_countries_by_conferences_5", "O:39:\"Illuminate\\Database\\Eloquent\\Collection\":2:{s:8:\"\u0000*\u0000items\";a:5:{i:0;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:6:{s:2:\"id\";i:12;s:9:\"listorder\";i:12;s:5:\"venue\";s:5:\"China\";s:3:\"url\";s:5:\"china\";s:3:\"fid\";i:11;s:16:\"conference_count\";i:1693;}s:11:\"\u0000*\u0000original\";a:6:{s:2:\"id\";i:12;s:9:\"listorder\";i:12;s:5:\"venue\";s:5:\"China\";s:3:\"url\";s:5:\"china\";s:3:\"fid\";i:11;s:16:\"conference_count\";i:1693;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}i:1;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:6:{s:2:\"id\";i:15;s:9:\"listorder\";i:15;s:5:\"venue\";s:5:\"Japan\";s:3:\"url\";s:5:\"japan\";s:3:\"fid\";i:11;s:16:\"conference_count\";i:720;}s:11:\"\u0000*\u0000original\";a:6:{s:2:\"id\";i:15;s:9:\"listorder\";i:15;s:5:\"venue\";s:5:\"Japan\";s:3:\"url\";s:5:\"japan\";s:3:\"fid\";i:11;s:16:\"conference_count\";i:720;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}i:2;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:6:{s:2:\"id\";i:24;s:9:\"listorder\";i:24;s:5:\"venue\";s:9:\"Singapore\";s:3:\"url\";s:9:\"singapore\";s:3:\"fid\";i:11;s:16:\"conference_count\";i:273;}s:11:\"\u0000*\u0000original\";a:6:{s:2:\"id\";i:24;s:9:\"listorder\";i:24;s:5:\"venue\";s:9:\"Singapore\";s:3:\"url\";s:9:\"singapore\";s:3:\"fid\";i:11;s:16:\"conference_count\";i:273;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}i:3;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:6:{s:2:\"id\";i:21;s:9:\"listorder\";i:21;s:5:\"venue\";s:8:\"Thailand\";s:3:\"url\";s:8:\"thailand\";s:3:\"fid\";i:11;s:16:\"conference_count\";i:184;}s:11:\"\u0000*\u0000original\";a:6:{s:2:\"id\";i:21;s:9:\"listorder\";i:21;s:5:\"venue\";s:8:\"Thailand\";s:3:\"url\";s:8:\"thailand\";s:3:\"fid\";i:11;s:16:\"conference_count\";i:184;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}i:4;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:6:{s:2:\"id\";i:190;s:9:\"listorder\";i:190;s:5:\"venue\";s:18:\"The United Kingdom\";s:3:\"url\";s:18:\"the_united_kingdom\";s:3:\"fid\";i:59;s:16:\"conference_count\";i:160;}s:11:\"\u0000*\u0000original\";a:6:{s:2:\"id\";i:190;s:9:\"listorder\";i:190;s:5:\"venue\";s:18:\"The United Kingdom\";s:3:\"url\";s:18:\"the_united_kingdom\";s:3:\"fid\";i:59;s:16:\"conference_count\";i:160;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}}s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;}"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 191}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 167}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 241}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 433}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.4131079, "duration": 0.026359999999999998, "duration_str": "26.36ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:191", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 191}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=191", "ajax": false, "filename": "DatabaseStore.php", "line": "191"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 40.862, "width_percent": 5.562}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_total_conferences_count')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_total_conferences_count"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.439864, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 46.424, "width_percent": 0.087}, {"sql": "delete from `cache` where `key` in ('iconf_meeting_cache_total_conferences_count', 'iconf_meeting_cache_illuminate:cache:flexible:created:total_conferences_count') and `expiration` <= **********", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_total_conferences_count", "iconf_meeting_cache_illuminate:cache:flexible:created:total_conferences_count", **********], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 411}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 145}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}], "start": **********.4407392, "duration": 0.02379, "duration_str": "23.79ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:411", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=411", "ajax": false, "filename": "DatabaseStore.php", "line": "411"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 46.51, "width_percent": 5.019}, {"sql": "select count(*) as aggregate from `event` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/Frontend/ConferenceStatisticsService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\ConferenceStatisticsService.php", "line": 56}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 20, "namespace": null, "name": "app/Services/Frontend/ConferenceStatisticsService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\ConferenceStatisticsService.php", "line": 55}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 55}], "start": **********.46489, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ConferenceStatisticsService.php:56", "source": {"index": 16, "namespace": null, "name": "app/Services/Frontend/ConferenceStatisticsService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\ConferenceStatisticsService.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FConferenceStatisticsService.php&line=56", "ajax": false, "filename": "ConferenceStatisticsService.php", "line": "56"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 51.53, "width_percent": 0.091}, {"sql": "insert into `cache` (`expiration`, `key`, `value`) values (1752666094, 'iconf_meeting_cache_total_conferences_count', 'i:4424;') on duplicate key update `expiration` = values(`expiration`), `key` = values(`key`), `value` = values(`value`)", "type": "query", "params": [], "bindings": [1752666094, "iconf_meeting_cache_total_conferences_count", "i:4424;"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 191}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 167}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 241}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 433}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.4656608, "duration": 0.054329999999999996, "duration_str": "54.33ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:191", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 191}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=191", "ajax": false, "filename": "DatabaseStore.php", "line": "191"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 51.62, "width_percent": 11.463}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_active_countries_count')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_active_countries_count"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.520347, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 63.083, "width_percent": 0.032}, {"sql": "delete from `cache` where `key` in ('iconf_meeting_cache_active_countries_count', 'iconf_meeting_cache_illuminate:cache:flexible:created:active_countries_count') and `expiration` <= **********", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_active_countries_count", "iconf_meeting_cache_illuminate:cache:flexible:created:active_countries_count", **********], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 411}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 145}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}], "start": **********.520818, "duration": 0.04076, "duration_str": "40.76ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:411", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=411", "ajax": false, "filename": "DatabaseStore.php", "line": "411"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 63.115, "width_percent": 8.6}, {"sql": "select count(*) as aggregate from `country` where exists (select * from `event` where `country`.`id` = `event`.`venue` and `status` = 1) and `fid` != 0", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/Frontend/ConferenceStatisticsService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\ConferenceStatisticsService.php", "line": 70}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 20, "namespace": null, "name": "app/Services/Frontend/ConferenceStatisticsService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\ConferenceStatisticsService.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 56}], "start": **********.562087, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "ConferenceStatisticsService.php:70", "source": {"index": 16, "namespace": null, "name": "app/Services/Frontend/ConferenceStatisticsService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\ConferenceStatisticsService.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FConferenceStatisticsService.php&line=70", "ajax": false, "filename": "ConferenceStatisticsService.php", "line": "70"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 71.715, "width_percent": 0.156}, {"sql": "insert into `cache` (`expiration`, `key`, `value`) values (1752666094, 'iconf_meeting_cache_active_countries_count', 'i:60;') on duplicate key update `expiration` = values(`expiration`), `key` = values(`key`), `value` = values(`value`)", "type": "query", "params": [], "bindings": [1752666094, "iconf_meeting_cache_active_countries_count", "i:60;"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 191}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 167}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 241}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 433}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.563195, "duration": 0.023850000000000003, "duration_str": "23.85ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:191", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 191}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=191", "ajax": false, "filename": "DatabaseStore.php", "line": "191"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 71.871, "width_percent": 5.032}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_all_continent_conference_data_for_js')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_all_continent_conference_data_for_js"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.587384, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 76.903, "width_percent": 0.03}, {"sql": "delete from `cache` where `key` in ('iconf_meeting_cache_all_continent_conference_data_for_js', 'iconf_meeting_cache_illuminate:cache:flexible:created:all_continent_conference_data_for_js') and `expiration` <= **********", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_all_continent_conference_data_for_js", "iconf_meeting_cache_illuminate:cache:flexible:created:all_continent_conference_data_for_js", **********], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 411}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 145}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}], "start": **********.587834, "duration": 0.02933, "duration_str": "29.33ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:411", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=411", "ajax": false, "filename": "DatabaseStore.php", "line": "411"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 76.933, "width_percent": 6.188}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_conference_distribution_continents')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_conference_distribution_continents"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.617542, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 83.121, "width_percent": 0.042}, {"sql": "delete from `cache` where `key` in ('iconf_meeting_cache_conference_distribution_continents', 'iconf_meeting_cache_illuminate:cache:flexible:created:conference_distribution_continents') and `expiration` <= **********", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_conference_distribution_continents", "iconf_meeting_cache_illuminate:cache:flexible:created:conference_distribution_continents", **********], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 411}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 145}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}], "start": **********.6181512, "duration": 0.0245, "duration_str": "24.5ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:411", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=411", "ajax": false, "filename": "DatabaseStore.php", "line": "411"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 83.163, "width_percent": 5.169}, {"sql": "select `country`.*, COUNT(event.id) as total_events_count from `country` left join `event` on `country`.`id` = `event`.`venue` and `event`.`status` = 1 where `country`.`fid` = 0 group by `country`.`id` order by `total_events_count` desc", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/Frontend/ConferenceStatisticsService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\ConferenceStatisticsService.php", "line": 125}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 19, "namespace": null, "name": "app/Services/Frontend/ConferenceStatisticsService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\ConferenceStatisticsService.php", "line": 115}, {"index": 20, "namespace": null, "name": "app/Services/Frontend/ConferenceStatisticsService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\ConferenceStatisticsService.php", "line": 227}], "start": **********.6431, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ConferenceStatisticsService.php:125", "source": {"index": 15, "namespace": null, "name": "app/Services/Frontend/ConferenceStatisticsService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\ConferenceStatisticsService.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FConferenceStatisticsService.php&line=125", "ajax": false, "filename": "ConferenceStatisticsService.php", "line": "125"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 88.332, "width_percent": 0.059}, {"sql": "insert into `cache` (`expiration`, `key`, `value`) values (1752666094, 'iconf_meeting_cache_conference_distribution_continents', 'O:39:\\\"Illuminate\\Database\\Eloquent\\Collection\\\":2:{s:8:\\\"?*?items\\\";a:6:{i:0;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:6:{s:2:\\\"id\\\";i:102;s:9:\\\"listorder\\\";i:102;s:5:\\\"venue\\\";s:6:\\\"Africa\\\";s:3:\\\"url\\\";s:6:\\\"africa\\\";s:3:\\\"fid\\\";i:0;s:18:\\\"total_events_count\\\";i:1;}s:11:\\\"?*?original\\\";a:6:{s:2:\\\"id\\\";i:102;s:9:\\\"listorder\\\";i:102;s:5:\\\"venue\\\";s:6:\\\"Africa\\\";s:3:\\\"url\\\";s:6:\\\"africa\\\";s:3:\\\"fid\\\";i:0;s:18:\\\"total_events_count\\\";i:1;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}i:1;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:6:{s:2:\\\"id\\\";i:128;s:9:\\\"listorder\\\";i:128;s:5:\\\"venue\\\";s:7:\\\"Oceania\\\";s:3:\\\"url\\\";s:7:\\\"oceania\\\";s:3:\\\"fid\\\";i:0;s:18:\\\"total_events_count\\\";i:0;}s:11:\\\"?*?original\\\";a:6:{s:2:\\\"id\\\";i:128;s:9:\\\"listorder\\\";i:128;s:5:\\\"venue\\\";s:7:\\\"Oceania\\\";s:3:\\\"url\\\";s:7:\\\"oceania\\\";s:3:\\\"fid\\\";i:0;s:18:\\\"total_events_count\\\";i:0;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}i:2;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:6:{s:2:\\\"id\\\";i:11;s:9:\\\"listorder\\\";i:11;s:5:\\\"venue\\\";s:4:\\\"Asia\\\";s:3:\\\"url\\\";s:4:\\\"asia\\\";s:3:\\\"fid\\\";i:0;s:18:\\\"total_events_count\\\";i:0;}s:11:\\\"?*?original\\\";a:6:{s:2:\\\"id\\\";i:11;s:9:\\\"listorder\\\";i:11;s:5:\\\"venue\\\";s:4:\\\"Asia\\\";s:3:\\\"url\\\";s:4:\\\"asia\\\";s:3:\\\"fid\\\";i:0;s:18:\\\"total_events_count\\\";i:0;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}i:3;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:6:{s:2:\\\"id\\\";i:176;s:9:\\\"listorder\\\";i:176;s:5:\\\"venue\\\";s:13:\\\"South America\\\";s:3:\\\"url\\\";s:13:\\\"south_america\\\";s:3:\\\"fid\\\";i:0;s:18:\\\"total_events_count\\\";i:0;}s:11:\\\"?*?original\\\";a:6:{s:2:\\\"id\\\";i:176;s:9:\\\"listorder\\\";i:176;s:5:\\\"venue\\\";s:13:\\\"South America\\\";s:3:\\\"url\\\";s:13:\\\"south_america\\\";s:3:\\\"fid\\\";i:0;s:18:\\\"total_events_count\\\";i:0;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}i:4;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:6:{s:2:\\\"id\\\";i:145;s:9:\\\"listorder\\\";i:145;s:5:\\\"venue\\\";s:13:\\\"North America\\\";s:3:\\\"url\\\";s:13:\\\"north_america\\\";s:3:\\\"fid\\\";i:0;s:18:\\\"total_events_count\\\";i:0;}s:11:\\\"?*?original\\\";a:6:{s:2:\\\"id\\\";i:145;s:9:\\\"listorder\\\";i:145;s:5:\\\"venue\\\";s:13:\\\"North America\\\";s:3:\\\"url\\\";s:13:\\\"north_america\\\";s:3:\\\"fid\\\";i:0;s:18:\\\"total_events_count\\\";i:0;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}i:5;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:6:{s:2:\\\"id\\\";i:59;s:9:\\\"listorder\\\";i:59;s:5:\\\"venue\\\";s:8:\\\"European\\\";s:3:\\\"url\\\";s:8:\\\"european\\\";s:3:\\\"fid\\\";i:0;s:18:\\\"total_events_count\\\";i:0;}s:11:\\\"?*?original\\\";a:6:{s:2:\\\"id\\\";i:59;s:9:\\\"listorder\\\";i:59;s:5:\\\"venue\\\";s:8:\\\"European\\\";s:3:\\\"url\\\";s:8:\\\"european\\\";s:3:\\\"fid\\\";i:0;s:18:\\\"total_events_count\\\";i:0;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}}s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;}') on duplicate key update `expiration` = values(`expiration`), `key` = values(`key`), `value` = values(`value`)", "type": "query", "params": [], "bindings": [1752666094, "iconf_meeting_cache_conference_distribution_continents", "O:39:\"Illuminate\\Database\\Eloquent\\Collection\":2:{s:8:\"\u0000*\u0000items\";a:6:{i:0;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:6:{s:2:\"id\";i:102;s:9:\"listorder\";i:102;s:5:\"venue\";s:6:\"Africa\";s:3:\"url\";s:6:\"africa\";s:3:\"fid\";i:0;s:18:\"total_events_count\";i:1;}s:11:\"\u0000*\u0000original\";a:6:{s:2:\"id\";i:102;s:9:\"listorder\";i:102;s:5:\"venue\";s:6:\"Africa\";s:3:\"url\";s:6:\"africa\";s:3:\"fid\";i:0;s:18:\"total_events_count\";i:1;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}i:1;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:6:{s:2:\"id\";i:128;s:9:\"listorder\";i:128;s:5:\"venue\";s:7:\"Oceania\";s:3:\"url\";s:7:\"oceania\";s:3:\"fid\";i:0;s:18:\"total_events_count\";i:0;}s:11:\"\u0000*\u0000original\";a:6:{s:2:\"id\";i:128;s:9:\"listorder\";i:128;s:5:\"venue\";s:7:\"Oceania\";s:3:\"url\";s:7:\"oceania\";s:3:\"fid\";i:0;s:18:\"total_events_count\";i:0;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}i:2;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:6:{s:2:\"id\";i:11;s:9:\"listorder\";i:11;s:5:\"venue\";s:4:\"Asia\";s:3:\"url\";s:4:\"asia\";s:3:\"fid\";i:0;s:18:\"total_events_count\";i:0;}s:11:\"\u0000*\u0000original\";a:6:{s:2:\"id\";i:11;s:9:\"listorder\";i:11;s:5:\"venue\";s:4:\"Asia\";s:3:\"url\";s:4:\"asia\";s:3:\"fid\";i:0;s:18:\"total_events_count\";i:0;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}i:3;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:6:{s:2:\"id\";i:176;s:9:\"listorder\";i:176;s:5:\"venue\";s:13:\"South America\";s:3:\"url\";s:13:\"south_america\";s:3:\"fid\";i:0;s:18:\"total_events_count\";i:0;}s:11:\"\u0000*\u0000original\";a:6:{s:2:\"id\";i:176;s:9:\"listorder\";i:176;s:5:\"venue\";s:13:\"South America\";s:3:\"url\";s:13:\"south_america\";s:3:\"fid\";i:0;s:18:\"total_events_count\";i:0;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}i:4;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:6:{s:2:\"id\";i:145;s:9:\"listorder\";i:145;s:5:\"venue\";s:13:\"North America\";s:3:\"url\";s:13:\"north_america\";s:3:\"fid\";i:0;s:18:\"total_events_count\";i:0;}s:11:\"\u0000*\u0000original\";a:6:{s:2:\"id\";i:145;s:9:\"listorder\";i:145;s:5:\"venue\";s:13:\"North America\";s:3:\"url\";s:13:\"north_america\";s:3:\"fid\";i:0;s:18:\"total_events_count\";i:0;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}i:5;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:6:{s:2:\"id\";i:59;s:9:\"listorder\";i:59;s:5:\"venue\";s:8:\"European\";s:3:\"url\";s:8:\"european\";s:3:\"fid\";i:0;s:18:\"total_events_count\";i:0;}s:11:\"\u0000*\u0000original\";a:6:{s:2:\"id\";i:59;s:9:\"listorder\";i:59;s:5:\"venue\";s:8:\"European\";s:3:\"url\";s:8:\"european\";s:3:\"fid\";i:0;s:18:\"total_events_count\";i:0;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}}s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;}"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 191}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 167}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 241}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 433}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.64378, "duration": 0.02903, "duration_str": "29.03ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:191", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 191}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=191", "ajax": false, "filename": "DatabaseStore.php", "line": "191"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 88.391, "width_percent": 6.125}, {"sql": "insert into `cache` (`expiration`, `key`, `value`) values (1752666094, 'iconf_meeting_cache_all_continent_conference_data_for_js', 'a:6:{s:6:\\\"africa\\\";a:4:{s:4:\\\"name\\\";s:6:\\\"Africa\\\";s:11:\\\"conferences\\\";i:1;s:9:\\\"countries\\\";a:0:{}s:13:\\\"topCategories\\\";a:0:{}}s:7:\\\"oceania\\\";a:4:{s:4:\\\"name\\\";s:7:\\\"Oceania\\\";s:11:\\\"conferences\\\";i:0;s:9:\\\"countries\\\";a:0:{}s:13:\\\"topCategories\\\";a:0:{}}s:4:\\\"asia\\\";a:4:{s:4:\\\"name\\\";s:4:\\\"Asia\\\";s:11:\\\"conferences\\\";i:0;s:9:\\\"countries\\\";a:0:{}s:13:\\\"topCategories\\\";a:0:{}}s:13:\\\"south-america\\\";a:4:{s:4:\\\"name\\\";s:13:\\\"South America\\\";s:11:\\\"conferences\\\";i:0;s:9:\\\"countries\\\";a:0:{}s:13:\\\"topCategories\\\";a:0:{}}s:13:\\\"north-america\\\";a:4:{s:4:\\\"name\\\";s:13:\\\"North America\\\";s:11:\\\"conferences\\\";i:0;s:9:\\\"countries\\\";a:0:{}s:13:\\\"topCategories\\\";a:0:{}}s:8:\\\"european\\\";a:4:{s:4:\\\"name\\\";s:8:\\\"European\\\";s:11:\\\"conferences\\\";i:0;s:9:\\\"countries\\\";a:0:{}s:13:\\\"topCategories\\\";a:0:{}}}') on duplicate key update `expiration` = values(`expiration`), `key` = values(`key`), `value` = values(`value`)", "type": "query", "params": [], "bindings": [1752666094, "iconf_meeting_cache_all_continent_conference_data_for_js", "a:6:{s:6:\"africa\";a:4:{s:4:\"name\";s:6:\"Africa\";s:11:\"conferences\";i:1;s:9:\"countries\";a:0:{}s:13:\"topCategories\";a:0:{}}s:7:\"oceania\";a:4:{s:4:\"name\";s:7:\"Oceania\";s:11:\"conferences\";i:0;s:9:\"countries\";a:0:{}s:13:\"topCategories\";a:0:{}}s:4:\"asia\";a:4:{s:4:\"name\";s:4:\"Asia\";s:11:\"conferences\";i:0;s:9:\"countries\";a:0:{}s:13:\"topCategories\";a:0:{}}s:13:\"south-america\";a:4:{s:4:\"name\";s:13:\"South America\";s:11:\"conferences\";i:0;s:9:\"countries\";a:0:{}s:13:\"topCategories\";a:0:{}}s:13:\"north-america\";a:4:{s:4:\"name\";s:13:\"North America\";s:11:\"conferences\";i:0;s:9:\"countries\";a:0:{}s:13:\"topCategories\";a:0:{}}s:8:\"european\";a:4:{s:4:\"name\";s:8:\"European\";s:11:\"conferences\";i:0;s:9:\"countries\";a:0:{}s:13:\"topCategories\";a:0:{}}}"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 191}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 167}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 241}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 433}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.673571, "duration": 0.02469, "duration_str": "24.69ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:191", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 191}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=191", "ajax": false, "filename": "DatabaseStore.php", "line": "191"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 94.516, "width_percent": 5.209}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.top')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.top"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.7021089, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 99.726, "width_percent": 0.046}, {"sql": "select * from `html_fragments` where `is_active` = 1 and `type` = 'js'", "type": "query", "params": [], "bindings": [1, "js"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/View/Composers/HtmlFragmentComposer.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Composers\\HtmlFragmentComposer.php", "line": 19}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 189}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160}], "start": **********.705681, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HtmlFragmentComposer.php:19", "source": {"index": 15, "namespace": null, "name": "app/View/Composers/HtmlFragmentComposer.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Composers\\HtmlFragmentComposer.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComposers%2FHtmlFragmentComposer.php&line=19", "ajax": false, "filename": "HtmlFragmentComposer.php", "line": "19"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 99.772, "width_percent": 0.099}, {"sql": "select * from `links` where `status` = 1 order by `listorder` asc, `id` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/View/Components/FriendlyLinks.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\FriendlyLinks.php", "line": 23}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 110}, {"index": 17, "namespace": "view", "name": "frontend.layouts.app", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/frontend/layouts/app.blade.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.709007, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "FriendlyLinks.php:23", "source": {"index": 15, "namespace": null, "name": "app/View/Components/FriendlyLinks.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\FriendlyLinks.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FFriendlyLinks.php&line=23", "ajax": false, "filename": "FriendlyLinks.php", "line": "23"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 99.871, "width_percent": 0.108}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_html_fragment_footer')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_html_fragment_footer"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "app/Services/HtmlFragmentService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\HtmlFragmentService.php", "line": 36}], "start": **********.710345, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 99.979, "width_percent": 0.021}]}, "models": {"data": {"App\\Models\\Country": {"value": 211, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FCountry.php&line=1", "ajax": false, "filename": "Country.php", "line": "?"}}, "App\\Models\\Category": {"value": 49, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Event": {"value": 23, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "App\\Models\\News": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FNews.php&line=1", "ajax": false, "filename": "News.php", "line": "?"}}, "App\\Models\\Link": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FLink.php&line=1", "ajax": false, "filename": "Link.php", "line": "?"}}, "App\\Models\\Advertisement": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FAdvertisement.php&line=1", "ajax": false, "filename": "Advertisement.php", "line": "?"}}, "App\\Models\\SeoSetting": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FSeoSetting.php&line=1", "ajax": false, "filename": "SeoSetting.php", "line": "?"}}, "App\\Models\\HtmlFragment": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FHtmlFragment.php&line=1", "ajax": false, "filename": "HtmlFragment.php", "line": "?"}}}, "count": 304, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://iconf.lv", "action_name": "home", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@index", "uri": "GET /", "controller": "App\\Http\\Controllers\\Frontend\\HomeController@index<a href=\"phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=46\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=46\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/HomeController.php:46-69</a>", "middleware": "web", "duration": "684ms", "peak_memory": "48MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1312128630 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1312128630\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-772215677 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-772215677\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1991854432 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"719 characters\">XSRF-TOKEN=eyJpdiI6IlJHZE81bUpEWHMvdU5CWlVwVERDbmc9PSIsInZhbHVlIjoialhwZ3BKb1UxVUVVcGNLOVNaTnZ1cHM5bW0zYTB5V3NhcFBMTjRpbVV4WVN2UlNocnFpMmVhekxTUUZLcGM4aXVPdlZsTFM2OGJFR1FmR3JuR2hoeGRkc2tCTURDWWtBemZtK1ZNb2ZKSWljalZRS2xScW4rbW5DU25ia3FWRG8iLCJtYWMiOiJjZjhjN2Y3Y2JlOThiMDA3MGFhMDc1N2VjNGY3N2FlMmRiNzg2NDBmYjZmMWQ2NWMyZTNiY2E0NjUzZjg4YjczIiwidGFnIjoiIn0%3D; iconf_meeting_session=eyJpdiI6IjFWcTNhL3RJVytyQXZwd2ZyN0ZnQnc9PSIsInZhbHVlIjoiWGtjYjdTZFNheStOQ05FSFZFQnFzanI5UUxrWDJTZUtKV1NaaWplTjM2WHZqc1AxSEtTZVl2Ykp6MmV0c1oyZHYrUGdqSzBXUTUyQmdYdjlWbzZSVFZEajVSNWk4QmlFWkJMZlZpVHZEZmhqM0JIdlZMc0dCVGp0R3Y5eE9Tak4iLCJtYWMiOiI3ZWNjYjU3ODFjMDdlMWYyYzhlNjc2ZDUyNDU3OTQwMTAwYzAxZTZhOGE2OTk2YTVmOTFhOWJjZmEwMDEzYmEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">iconf.lv</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1991854432\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-391198625 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KzVnHwsb1j7aFLoepnAgBRMhnc1xQgVO8Wwk7tZ4</span>\"\n  \"<span class=sf-dump-key>iconf_meeting_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">n81JYPLEP0cENw7ZqI8BBaufrhZyV3CgHX5ObznX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-391198625\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1919398184 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 16 Jul 2025 11:40:34 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1919398184\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1795575126 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KzVnHwsb1j7aFLoepnAgBRMhnc1xQgVO8Wwk7tZ4</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://iconf.lv/categories/environments</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$i.a5gP7/Z07pk3CeR6vNfe9lGm.BtthHlLi1.olj0JJDfdNlCRY8K</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1795575126\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://iconf.lv", "action_name": "home", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@index"}, "badge": null}}