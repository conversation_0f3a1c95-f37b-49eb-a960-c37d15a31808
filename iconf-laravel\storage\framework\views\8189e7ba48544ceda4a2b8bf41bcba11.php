<?php if($advertisements->count() > 0): ?>
<div class="bg-white rounded-xl shadow-lg border border-slate-200/50 overflow-hidden relative">
    <div class="bg-gradient-to-r from-amber-500 to-orange-600 p-4">
        <h3 class="text-lg font-bold text-white flex items-center gap-2">
            <i class="fas fa-bullhorn"></i>
            Sponsored Content
        </h3>
    </div>
    <div class="p-4 space-y-4">
        <?php $__currentLoopData = $advertisements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ad): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="group relative bg-gradient-to-br from-slate-50 to-white rounded-lg border border-slate-200 p-4 hover:shadow-md transition-all duration-300">
            <!-- Ad Label -->
            <div class="absolute top-0 right-0 bg-gray-100 text-gray-500 text-xs px-1.5 py-0.5 rounded-bl-md font-medium opacity-70">AD</div>
            <?php if($ad->pic): ?>
                <?php if($ad->link): ?>
                <a href="<?php echo e($ad->link); ?>" target="_blank" class="block mb-3">
                    <img src="<?php echo e($ad->picUrl); ?>" alt="<?php echo e($ad->title); ?>" class="w-full rounded-lg group-hover:scale-105 transition-transform duration-300">
                </a>
                <?php else: ?>
                <img src="<?php echo e($ad->picUrl); ?>" alt="<?php echo e($ad->title); ?>" class="w-full rounded-lg mb-3">
                <?php endif; ?>
            <?php endif; ?>
            
            <div class="ad-content">
                <?php if($ad->link): ?>
                <a href="<?php echo e($ad->link); ?>" target="_blank" class="block">
                    <h4 class="font-semibold text-slate-800 group-hover:text-amber-600 transition-colors"><?php echo e($ad->title); ?></h4>
                </a>
                <?php else: ?>
                <h4 class="font-semibold text-slate-800"><?php echo e($ad->title); ?></h4>
                <?php endif; ?>
                
                <?php if($ad->content): ?>
                <div class="text-slate-600 text-sm mt-2 leading-relaxed ad-html-content">
                    <?php echo $ad->content; ?>

                </div>
                <?php endif; ?>
                
                <?php if($ad->link): ?>
                <div class="mt-3">
                    <a href="<?php echo e($ad->link); ?>" target="_blank" class="text-sm text-green-600 hover:text-green-800 transition-colors flex items-center">
                        <?php echo e(parse_url($ad->link, PHP_URL_HOST) ?? $ad->link); ?>

                        <i class="fas fa-external-link-alt ml-1 text-xs"></i>
                    </a>
                </div>
                <?php endif; ?>
            </div>
            
            <!-- 装饰元素 -->
            <div class="absolute top-0 right-0 w-12 h-12 bg-gradient-to-br from-amber-50 to-orange-50 rounded-full transform translate-x-6 -translate-y-6 opacity-20 group-hover:opacity-30 transition-all duration-300"></div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>

<style>
    .ad-html-content img {
        max-width: 100%;
        height: auto;
        border-radius: 0.375rem;
        margin-bottom: 0.5rem;
    }
    
    .ad-html-content a {
        color: #f59e0b;
        text-decoration: underline;
        transition: color 0.2s;
    }
    
    .ad-html-content a:hover {
        color: #d97706;
    }
    
    .ad-html-content p {
        margin-bottom: 0.5rem;
    }
    
    .ad-html-content ul, .ad-html-content ol {
        padding-left: 1.5rem;
        margin-bottom: 0.5rem;
    }
</style>
<?php endif; ?>
<?php /**PATH D:\phpEnv\www\iconf_org_by_laravel\iconf-laravel\resources\views/components/advertisement.blade.php ENDPATH**/ ?>