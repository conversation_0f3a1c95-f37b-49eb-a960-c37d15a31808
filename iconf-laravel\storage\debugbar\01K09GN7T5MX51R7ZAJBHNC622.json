{"__meta": {"id": "01K09GN7T5MX51R7ZAJBHNC622", "datetime": "2025-07-16 11:42:00", "utime": **********.006278, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.347729, "end": **********.006288, "duration": 0.6585590839385986, "duration_str": "659ms", "measures": [{"label": "Booting", "start": **********.347729, "relative_start": 0, "end": **********.546, "relative_end": **********.546, "duration": 0.****************, "duration_str": "198ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.546006, "relative_start": 0.*****************, "end": **********.006289, "relative_end": 9.5367431640625e-07, "duration": 0.****************, "duration_str": "460ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.554457, "relative_start": 0.****************, "end": **********.555827, "relative_end": **********.555827, "duration": 0.0013699531555175781, "duration_str": "1.37ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.993255, "relative_start": 0.****************, "end": **********.005192, "relative_end": **********.005192, "duration": 0.011937141418457031, "duration_str": "11.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: frontend.home.index", "start": **********.994266, "relative_start": 0.****************, "end": **********.994266, "relative_end": **********.994266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.top-list", "start": **********.997068, "relative_start": 0.***************, "end": **********.997068, "relative_end": **********.997068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.global-conference-distribution", "start": **********.998784, "relative_start": 0.651055097579956, "end": **********.998784, "relative_end": **********.998784, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.layouts.app", "start": **********.00043, "relative_start": 0.6527011394500732, "end": **********.00043, "relative_end": **********.00043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.navigation", "start": **********.000766, "relative_start": 0.6530370712280273, "end": **********.000766, "relative_end": **********.000766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.friendly-links", "start": **********.003989, "relative_start": 0.6562600135803223, "end": **********.003989, "relative_end": **********.003989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 45779128, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.4.2", "Environment": "local", "Debug Mode": "Enabled", "URL": "iconf.lv", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 6, "nb_templates": 6, "templates": [{"name": "frontend.home.index", "param_count": null, "params": [], "start": **********.994255, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/frontend/home/<USER>", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Ffrontend%2Fhome%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "components.category.top-list", "param_count": null, "params": [], "start": **********.997059, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/top-list.blade.phpcomponents.category.top-list", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Ftop-list.blade.php&line=1", "ajax": false, "filename": "top-list.blade.php", "line": "?"}}, {"name": "components.global-conference-distribution", "param_count": null, "params": [], "start": **********.998776, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/global-conference-distribution.blade.phpcomponents.global-conference-distribution", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fglobal-conference-distribution.blade.php&line=1", "ajax": false, "filename": "global-conference-distribution.blade.php", "line": "?"}}, {"name": "frontend.layouts.app", "param_count": null, "params": [], "start": **********.000423, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/frontend/layouts/app.blade.phpfrontend.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "components.navigation", "param_count": null, "params": [], "start": **********.00076, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/navigation.blade.phpcomponents.navigation", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fnavigation.blade.php&line=1", "ajax": false, "filename": "navigation.blade.php", "line": "?"}}, {"name": "components.friendly-links", "param_count": null, "params": [], "start": **********.003982, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/friendly-links.blade.phpcomponents.friendly-links", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Ffriendly-links.blade.php&line=1", "ajax": false, "filename": "friendly-links.blade.php", "line": "?"}}]}, "queries": {"count": 41, "nb_statements": 40, "nb_visible_statements": 41, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.4078299999999999, "accumulated_duration_str": "408ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.561337, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'n81JYPLEP0cENw7ZqI8BBaufrhZyV3CgHX5ObznX' limit 1", "type": "query", "params": [], "bindings": ["n81JYPLEP0cENw7ZqI8BBaufrhZyV3CgHX5ObznX"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.562011, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 0, "width_percent": 0.233}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.top')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.top"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.566954, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 0.233, "width_percent": 0.047}, {"sql": "select * from `event` where `status` = 1 and `end_date` >= 1755344519 order by `ding` desc, `end_date` asc limit 7", "type": "query", "params": [], "bindings": [1, 1755344519], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 122}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 59}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 49}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.5692549, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:122", "source": {"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=122", "ajax": false, "filename": "HomeService.php", "line": "122"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 0.28, "width_percent": 0.52}, {"sql": "select `id`, `venue` from `country` where `country`.`id` in (12, 57, 146)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 122}, {"index": 21, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 59}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 49}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.572137, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:122", "source": {"index": 20, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=122", "ajax": false, "filename": "HomeService.php", "line": "122"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 0.799, "width_percent": 0.074}, {"sql": "select `category`.`id`, `category`.`name`, `list`.`eid` as `pivot_eid`, `list`.`cid` as `pivot_cid` from `category` inner join `list` on `category`.`id` = `list`.`cid` where `list`.`eid` in (4838, 4896, 4908, 4932, 4933, 5003, 5178)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 122}, {"index": 20, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 59}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 49}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.573868, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:122", "source": {"index": 19, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=122", "ajax": false, "filename": "HomeService.php", "line": "122"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 0.873, "width_percent": 0.181}, {"sql": "select * from `event` where `status` = 1 and `push` = 1 order by `id` desc limit 8", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 138}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 62}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 49}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.575531, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:138", "source": {"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=138", "ajax": false, "filename": "HomeService.php", "line": "138"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 1.054, "width_percent": 0.579}, {"sql": "select `id`, `venue` from `country` where `country`.`id` in (12, 57, 72, 85, 95, 129)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 138}, {"index": 21, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 62}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 49}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.578285, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:138", "source": {"index": 20, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=138", "ajax": false, "filename": "HomeService.php", "line": "138"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 1.633, "width_percent": 0.027}, {"sql": "select `category`.`id`, `category`.`name`, `list`.`eid` as `pivot_eid`, `list`.`cid` as `pivot_cid` from `category` inner join `list` on `category`.`id` = `list`.`cid` where `list`.`eid` in (4681, 4920, 5021, 5032, 5147, 5148, 5223, 5256)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 138}, {"index": 20, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 62}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 49}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.578781, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:138", "source": {"index": 19, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=138", "ajax": false, "filename": "HomeService.php", "line": "138"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 1.66, "width_percent": 0.042}, {"sql": "select * from `ad_txt` where `cid` = 0 and `endtime` >= **********", "type": "query", "params": [], "bindings": [0, **********], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 165}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 65}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 49}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.57993, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:165", "source": {"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 165}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=165", "ajax": false, "filename": "HomeService.php", "line": "165"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 1.702, "width_percent": 0.12}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_countries_with_event_count')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_countries_with_event_count"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.580774, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 1.822, "width_percent": 0.032}, {"sql": "select count(*) as aggregate from `news`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 188}, {"index": 17, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 71}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 49}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.581585, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:188", "source": {"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=188", "ajax": false, "filename": "HomeService.php", "line": "188"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 1.854, "width_percent": 0.056}, {"sql": "select * from `news` order by `id` desc limit 8 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 188}, {"index": 17, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 71}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 49}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.582069, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:188", "source": {"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=188", "ajax": false, "filename": "HomeService.php", "line": "188"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 1.91, "width_percent": 0.14}, {"sql": "select * from `news` where `is_featured` = 1 and `cover` is not null and `cover` != '' order by `id` desc limit 3", "type": "query", "params": [], "bindings": [1, ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 204}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 49}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.583257, "duration": 0.0025099999999999996, "duration_str": "2.51ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:204", "source": {"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 204}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=204", "ajax": false, "filename": "HomeService.php", "line": "204"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 2.05, "width_percent": 0.615}, {"sql": "select * from `event` where `status` = 1 order by `addtime` desc limit 8", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 153}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 77}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 49}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.58618, "duration": 0.01052, "duration_str": "10.52ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:153", "source": {"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 153}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=153", "ajax": false, "filename": "HomeService.php", "line": "153"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 2.665, "width_percent": 2.58}, {"sql": "select `id`, `venue` from `country` where `country`.`id` in (12, 15, 17, 57, 102)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 153}, {"index": 21, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 77}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 49}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.597181, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:153", "source": {"index": 20, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 153}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=153", "ajax": false, "filename": "HomeService.php", "line": "153"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 5.245, "width_percent": 0.042}, {"sql": "select `category`.`id`, `category`.`name`, `list`.`eid` as `pivot_eid`, `list`.`cid` as `pivot_cid` from `category` inner join `list` on `category`.`id` = `list`.`cid` where `list`.`eid` in (4920, 4921, 5085, 5147, 5389, 5508, 5509, 5515)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 153}, {"index": 20, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 77}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 49}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.597806, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:153", "source": {"index": 19, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 153}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=153", "ajax": false, "filename": "HomeService.php", "line": "153"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 5.287, "width_percent": 0.037}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_seo_setting_home')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_seo_setting_home"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.5985801, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 5.323, "width_percent": 0.025}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_top_countries_by_conferences_5')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_top_countries_by_conferences_5"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.599066, "duration": 7.000000000000001e-05, "duration_str": "70μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 5.348, "width_percent": 0.017}, {"sql": "delete from `cache` where `key` in ('iconf_meeting_cache_top_countries_by_conferences_5', 'iconf_meeting_cache_illuminate:cache:flexible:created:top_countries_by_conferences_5') and `expiration` <= **********", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_top_countries_by_conferences_5", "iconf_meeting_cache_illuminate:cache:flexible:created:top_countries_by_conferences_5", **********], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 411}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 145}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}], "start": **********.5995011, "duration": 0.027710000000000002, "duration_str": "27.71ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:411", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=411", "ajax": false, "filename": "DatabaseStore.php", "line": "411"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 5.365, "width_percent": 6.794}, {"sql": "select `country`.*, COUNT(event.id) as conference_count from `country` left join `event` on `country`.`id` = `event`.`venue` and `event`.`status` = 1 where `country`.`fid` != 0 group by `country`.`id` having `conference_count` > 0 order by `conference_count` desc limit 5", "type": "query", "params": [], "bindings": [1, 0, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/Frontend/ConferenceStatisticsService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\ConferenceStatisticsService.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 19, "namespace": null, "name": "app/Services/Frontend/ConferenceStatisticsService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\ConferenceStatisticsService.php", "line": 32}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 52}], "start": **********.627985, "duration": 0.014, "duration_str": "14ms", "memory": 0, "memory_str": null, "filename": "ConferenceStatisticsService.php:44", "source": {"index": 15, "namespace": null, "name": "app/Services/Frontend/ConferenceStatisticsService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\ConferenceStatisticsService.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FConferenceStatisticsService.php&line=44", "ajax": false, "filename": "ConferenceStatisticsService.php", "line": "44"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 12.159, "width_percent": 3.433}, {"sql": "insert into `cache` (`expiration`, `key`, `value`) values (1752666179, 'iconf_meeting_cache_top_countries_by_conferences_5', 'O:39:\\\"Illuminate\\Database\\Eloquent\\Collection\\\":2:{s:8:\\\"?*?items\\\";a:5:{i:0;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:6:{s:2:\\\"id\\\";i:12;s:9:\\\"listorder\\\";i:12;s:5:\\\"venue\\\";s:5:\\\"China\\\";s:3:\\\"url\\\";s:5:\\\"china\\\";s:3:\\\"fid\\\";i:11;s:16:\\\"conference_count\\\";i:1693;}s:11:\\\"?*?original\\\";a:6:{s:2:\\\"id\\\";i:12;s:9:\\\"listorder\\\";i:12;s:5:\\\"venue\\\";s:5:\\\"China\\\";s:3:\\\"url\\\";s:5:\\\"china\\\";s:3:\\\"fid\\\";i:11;s:16:\\\"conference_count\\\";i:1693;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}i:1;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:6:{s:2:\\\"id\\\";i:15;s:9:\\\"listorder\\\";i:15;s:5:\\\"venue\\\";s:5:\\\"Japan\\\";s:3:\\\"url\\\";s:5:\\\"japan\\\";s:3:\\\"fid\\\";i:11;s:16:\\\"conference_count\\\";i:720;}s:11:\\\"?*?original\\\";a:6:{s:2:\\\"id\\\";i:15;s:9:\\\"listorder\\\";i:15;s:5:\\\"venue\\\";s:5:\\\"Japan\\\";s:3:\\\"url\\\";s:5:\\\"japan\\\";s:3:\\\"fid\\\";i:11;s:16:\\\"conference_count\\\";i:720;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}i:2;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:6:{s:2:\\\"id\\\";i:24;s:9:\\\"listorder\\\";i:24;s:5:\\\"venue\\\";s:9:\\\"Singapore\\\";s:3:\\\"url\\\";s:9:\\\"singapore\\\";s:3:\\\"fid\\\";i:11;s:16:\\\"conference_count\\\";i:273;}s:11:\\\"?*?original\\\";a:6:{s:2:\\\"id\\\";i:24;s:9:\\\"listorder\\\";i:24;s:5:\\\"venue\\\";s:9:\\\"Singapore\\\";s:3:\\\"url\\\";s:9:\\\"singapore\\\";s:3:\\\"fid\\\";i:11;s:16:\\\"conference_count\\\";i:273;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}i:3;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:6:{s:2:\\\"id\\\";i:21;s:9:\\\"listorder\\\";i:21;s:5:\\\"venue\\\";s:8:\\\"Thailand\\\";s:3:\\\"url\\\";s:8:\\\"thailand\\\";s:3:\\\"fid\\\";i:11;s:16:\\\"conference_count\\\";i:184;}s:11:\\\"?*?original\\\";a:6:{s:2:\\\"id\\\";i:21;s:9:\\\"listorder\\\";i:21;s:5:\\\"venue\\\";s:8:\\\"Thailand\\\";s:3:\\\"url\\\";s:8:\\\"thailand\\\";s:3:\\\"fid\\\";i:11;s:16:\\\"conference_count\\\";i:184;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}i:4;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:6:{s:2:\\\"id\\\";i:190;s:9:\\\"listorder\\\";i:190;s:5:\\\"venue\\\";s:18:\\\"The United Kingdom\\\";s:3:\\\"url\\\";s:18:\\\"the_united_kingdom\\\";s:3:\\\"fid\\\";i:59;s:16:\\\"conference_count\\\";i:160;}s:11:\\\"?*?original\\\";a:6:{s:2:\\\"id\\\";i:190;s:9:\\\"listorder\\\";i:190;s:5:\\\"venue\\\";s:18:\\\"The United Kingdom\\\";s:3:\\\"url\\\";s:18:\\\"the_united_kingdom\\\";s:3:\\\"fid\\\";i:59;s:16:\\\"conference_count\\\";i:160;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}}s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;}') on duplicate key update `expiration` = values(`expiration`), `key` = values(`key`), `value` = values(`value`)", "type": "query", "params": [], "bindings": [1752666179, "iconf_meeting_cache_top_countries_by_conferences_5", "O:39:\"Illuminate\\Database\\Eloquent\\Collection\":2:{s:8:\"\u0000*\u0000items\";a:5:{i:0;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:6:{s:2:\"id\";i:12;s:9:\"listorder\";i:12;s:5:\"venue\";s:5:\"China\";s:3:\"url\";s:5:\"china\";s:3:\"fid\";i:11;s:16:\"conference_count\";i:1693;}s:11:\"\u0000*\u0000original\";a:6:{s:2:\"id\";i:12;s:9:\"listorder\";i:12;s:5:\"venue\";s:5:\"China\";s:3:\"url\";s:5:\"china\";s:3:\"fid\";i:11;s:16:\"conference_count\";i:1693;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}i:1;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:6:{s:2:\"id\";i:15;s:9:\"listorder\";i:15;s:5:\"venue\";s:5:\"Japan\";s:3:\"url\";s:5:\"japan\";s:3:\"fid\";i:11;s:16:\"conference_count\";i:720;}s:11:\"\u0000*\u0000original\";a:6:{s:2:\"id\";i:15;s:9:\"listorder\";i:15;s:5:\"venue\";s:5:\"Japan\";s:3:\"url\";s:5:\"japan\";s:3:\"fid\";i:11;s:16:\"conference_count\";i:720;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}i:2;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:6:{s:2:\"id\";i:24;s:9:\"listorder\";i:24;s:5:\"venue\";s:9:\"Singapore\";s:3:\"url\";s:9:\"singapore\";s:3:\"fid\";i:11;s:16:\"conference_count\";i:273;}s:11:\"\u0000*\u0000original\";a:6:{s:2:\"id\";i:24;s:9:\"listorder\";i:24;s:5:\"venue\";s:9:\"Singapore\";s:3:\"url\";s:9:\"singapore\";s:3:\"fid\";i:11;s:16:\"conference_count\";i:273;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}i:3;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:6:{s:2:\"id\";i:21;s:9:\"listorder\";i:21;s:5:\"venue\";s:8:\"Thailand\";s:3:\"url\";s:8:\"thailand\";s:3:\"fid\";i:11;s:16:\"conference_count\";i:184;}s:11:\"\u0000*\u0000original\";a:6:{s:2:\"id\";i:21;s:9:\"listorder\";i:21;s:5:\"venue\";s:8:\"Thailand\";s:3:\"url\";s:8:\"thailand\";s:3:\"fid\";i:11;s:16:\"conference_count\";i:184;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}i:4;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:6:{s:2:\"id\";i:190;s:9:\"listorder\";i:190;s:5:\"venue\";s:18:\"The United Kingdom\";s:3:\"url\";s:18:\"the_united_kingdom\";s:3:\"fid\";i:59;s:16:\"conference_count\";i:160;}s:11:\"\u0000*\u0000original\";a:6:{s:2:\"id\";i:190;s:9:\"listorder\";i:190;s:5:\"venue\";s:18:\"The United Kingdom\";s:3:\"url\";s:18:\"the_united_kingdom\";s:3:\"fid\";i:59;s:16:\"conference_count\";i:160;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}}s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;}"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 191}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 167}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 241}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 433}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.642683, "duration": 0.027350000000000003, "duration_str": "27.35ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:191", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 191}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=191", "ajax": false, "filename": "DatabaseStore.php", "line": "191"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 15.592, "width_percent": 6.706}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_total_conferences_count')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_total_conferences_count"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.670599, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 22.299, "width_percent": 0.054}, {"sql": "delete from `cache` where `key` in ('iconf_meeting_cache_total_conferences_count', 'iconf_meeting_cache_illuminate:cache:flexible:created:total_conferences_count') and `expiration` <= **********", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_total_conferences_count", "iconf_meeting_cache_illuminate:cache:flexible:created:total_conferences_count", **********], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 411}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 145}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}], "start": **********.6712258, "duration": 0.033780000000000004, "duration_str": "33.78ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:411", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=411", "ajax": false, "filename": "DatabaseStore.php", "line": "411"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 22.352, "width_percent": 8.283}, {"sql": "select count(*) as aggregate from `event` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/Frontend/ConferenceStatisticsService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\ConferenceStatisticsService.php", "line": 56}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 20, "namespace": null, "name": "app/Services/Frontend/ConferenceStatisticsService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\ConferenceStatisticsService.php", "line": 55}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 55}], "start": **********.7054281, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ConferenceStatisticsService.php:56", "source": {"index": 16, "namespace": null, "name": "app/Services/Frontend/ConferenceStatisticsService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\ConferenceStatisticsService.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FConferenceStatisticsService.php&line=56", "ajax": false, "filename": "ConferenceStatisticsService.php", "line": "56"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 30.635, "width_percent": 0.115}, {"sql": "insert into `cache` (`expiration`, `key`, `value`) values (1752666179, 'iconf_meeting_cache_total_conferences_count', 'i:4424;') on duplicate key update `expiration` = values(`expiration`), `key` = values(`key`), `value` = values(`value`)", "type": "query", "params": [], "bindings": [1752666179, "iconf_meeting_cache_total_conferences_count", "i:4424;"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 191}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 167}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 241}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 433}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.7062252, "duration": 0.06543, "duration_str": "65.43ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:191", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 191}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=191", "ajax": false, "filename": "DatabaseStore.php", "line": "191"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 30.751, "width_percent": 16.043}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_active_countries_count')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_active_countries_count"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.7720752, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 46.794, "width_percent": 0.066}, {"sql": "delete from `cache` where `key` in ('iconf_meeting_cache_active_countries_count', 'iconf_meeting_cache_illuminate:cache:flexible:created:active_countries_count') and `expiration` <= **********", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_active_countries_count", "iconf_meeting_cache_illuminate:cache:flexible:created:active_countries_count", **********], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 411}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 145}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}], "start": **********.7727861, "duration": 0.06558, "duration_str": "65.58ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:411", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=411", "ajax": false, "filename": "DatabaseStore.php", "line": "411"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 46.86, "width_percent": 16.08}, {"sql": "select count(*) as aggregate from `country` where exists (select * from `event` where `country`.`id` = `event`.`venue` and `status` = 1) and `fid` != 0", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/Frontend/ConferenceStatisticsService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\ConferenceStatisticsService.php", "line": 70}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 20, "namespace": null, "name": "app/Services/Frontend/ConferenceStatisticsService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\ConferenceStatisticsService.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 56}], "start": **********.8395221, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ConferenceStatisticsService.php:70", "source": {"index": 16, "namespace": null, "name": "app/Services/Frontend/ConferenceStatisticsService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\ConferenceStatisticsService.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FConferenceStatisticsService.php&line=70", "ajax": false, "filename": "ConferenceStatisticsService.php", "line": "70"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 62.94, "width_percent": 0.125}, {"sql": "insert into `cache` (`expiration`, `key`, `value`) values (1752666179, 'iconf_meeting_cache_active_countries_count', 'i:60;') on duplicate key update `expiration` = values(`expiration`), `key` = values(`key`), `value` = values(`value`)", "type": "query", "params": [], "bindings": [1752666179, "iconf_meeting_cache_active_countries_count", "i:60;"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 191}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 167}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 241}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 433}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.8404071, "duration": 0.02964, "duration_str": "29.64ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:191", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 191}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=191", "ajax": false, "filename": "DatabaseStore.php", "line": "191"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 63.065, "width_percent": 7.268}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_all_continent_conference_data_for_js')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_all_continent_conference_data_for_js"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.87042, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 70.333, "width_percent": 0.042}, {"sql": "delete from `cache` where `key` in ('iconf_meeting_cache_all_continent_conference_data_for_js', 'iconf_meeting_cache_illuminate:cache:flexible:created:all_continent_conference_data_for_js') and `expiration` <= **********", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_all_continent_conference_data_for_js", "iconf_meeting_cache_illuminate:cache:flexible:created:all_continent_conference_data_for_js", **********], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 411}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 145}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}], "start": **********.870918, "duration": 0.0342, "duration_str": "34.2ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:411", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=411", "ajax": false, "filename": "DatabaseStore.php", "line": "411"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 70.375, "width_percent": 8.386}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_conference_distribution_continents')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_conference_distribution_continents"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.905522, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 78.761, "width_percent": 0.042}, {"sql": "delete from `cache` where `key` in ('iconf_meeting_cache_conference_distribution_continents', 'iconf_meeting_cache_illuminate:cache:flexible:created:conference_distribution_continents') and `expiration` <= **********", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_conference_distribution_continents", "iconf_meeting_cache_illuminate:cache:flexible:created:conference_distribution_continents", **********], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 411}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 145}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}], "start": **********.906032, "duration": 0.030699999999999998, "duration_str": "30.7ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:411", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=411", "ajax": false, "filename": "DatabaseStore.php", "line": "411"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 78.802, "width_percent": 7.528}, {"sql": "select `country`.*, COUNT(event.id) as total_events_count from `country` left join `event` on `country`.`id` = `event`.`venue` and `event`.`status` = 1 where `country`.`fid` = 0 group by `country`.`id` order by `total_events_count` desc", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/Frontend/ConferenceStatisticsService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\ConferenceStatisticsService.php", "line": 125}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 19, "namespace": null, "name": "app/Services/Frontend/ConferenceStatisticsService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\ConferenceStatisticsService.php", "line": 115}, {"index": 20, "namespace": null, "name": "app/Services/Frontend/ConferenceStatisticsService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\ConferenceStatisticsService.php", "line": 227}], "start": **********.937187, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ConferenceStatisticsService.php:125", "source": {"index": 15, "namespace": null, "name": "app/Services/Frontend/ConferenceStatisticsService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\ConferenceStatisticsService.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FConferenceStatisticsService.php&line=125", "ajax": false, "filename": "ConferenceStatisticsService.php", "line": "125"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 86.33, "width_percent": 0.078}, {"sql": "insert into `cache` (`expiration`, `key`, `value`) values (1752666179, 'iconf_meeting_cache_conference_distribution_continents', 'O:39:\\\"Illuminate\\Database\\Eloquent\\Collection\\\":2:{s:8:\\\"?*?items\\\";a:6:{i:0;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:6:{s:2:\\\"id\\\";i:102;s:9:\\\"listorder\\\";i:102;s:5:\\\"venue\\\";s:6:\\\"Africa\\\";s:3:\\\"url\\\";s:6:\\\"africa\\\";s:3:\\\"fid\\\";i:0;s:18:\\\"total_events_count\\\";i:1;}s:11:\\\"?*?original\\\";a:6:{s:2:\\\"id\\\";i:102;s:9:\\\"listorder\\\";i:102;s:5:\\\"venue\\\";s:6:\\\"Africa\\\";s:3:\\\"url\\\";s:6:\\\"africa\\\";s:3:\\\"fid\\\";i:0;s:18:\\\"total_events_count\\\";i:1;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}i:1;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:6:{s:2:\\\"id\\\";i:128;s:9:\\\"listorder\\\";i:128;s:5:\\\"venue\\\";s:7:\\\"Oceania\\\";s:3:\\\"url\\\";s:7:\\\"oceania\\\";s:3:\\\"fid\\\";i:0;s:18:\\\"total_events_count\\\";i:0;}s:11:\\\"?*?original\\\";a:6:{s:2:\\\"id\\\";i:128;s:9:\\\"listorder\\\";i:128;s:5:\\\"venue\\\";s:7:\\\"Oceania\\\";s:3:\\\"url\\\";s:7:\\\"oceania\\\";s:3:\\\"fid\\\";i:0;s:18:\\\"total_events_count\\\";i:0;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}i:2;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:6:{s:2:\\\"id\\\";i:11;s:9:\\\"listorder\\\";i:11;s:5:\\\"venue\\\";s:4:\\\"Asia\\\";s:3:\\\"url\\\";s:4:\\\"asia\\\";s:3:\\\"fid\\\";i:0;s:18:\\\"total_events_count\\\";i:0;}s:11:\\\"?*?original\\\";a:6:{s:2:\\\"id\\\";i:11;s:9:\\\"listorder\\\";i:11;s:5:\\\"venue\\\";s:4:\\\"Asia\\\";s:3:\\\"url\\\";s:4:\\\"asia\\\";s:3:\\\"fid\\\";i:0;s:18:\\\"total_events_count\\\";i:0;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}i:3;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:6:{s:2:\\\"id\\\";i:176;s:9:\\\"listorder\\\";i:176;s:5:\\\"venue\\\";s:13:\\\"South America\\\";s:3:\\\"url\\\";s:13:\\\"south_america\\\";s:3:\\\"fid\\\";i:0;s:18:\\\"total_events_count\\\";i:0;}s:11:\\\"?*?original\\\";a:6:{s:2:\\\"id\\\";i:176;s:9:\\\"listorder\\\";i:176;s:5:\\\"venue\\\";s:13:\\\"South America\\\";s:3:\\\"url\\\";s:13:\\\"south_america\\\";s:3:\\\"fid\\\";i:0;s:18:\\\"total_events_count\\\";i:0;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}i:4;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:6:{s:2:\\\"id\\\";i:145;s:9:\\\"listorder\\\";i:145;s:5:\\\"venue\\\";s:13:\\\"North America\\\";s:3:\\\"url\\\";s:13:\\\"north_america\\\";s:3:\\\"fid\\\";i:0;s:18:\\\"total_events_count\\\";i:0;}s:11:\\\"?*?original\\\";a:6:{s:2:\\\"id\\\";i:145;s:9:\\\"listorder\\\";i:145;s:5:\\\"venue\\\";s:13:\\\"North America\\\";s:3:\\\"url\\\";s:13:\\\"north_america\\\";s:3:\\\"fid\\\";i:0;s:18:\\\"total_events_count\\\";i:0;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}i:5;O:18:\\\"App\\Models\\Country\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:7:\\\"country\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:6:{s:2:\\\"id\\\";i:59;s:9:\\\"listorder\\\";i:59;s:5:\\\"venue\\\";s:8:\\\"European\\\";s:3:\\\"url\\\";s:8:\\\"european\\\";s:3:\\\"fid\\\";i:0;s:18:\\\"total_events_count\\\";i:0;}s:11:\\\"?*?original\\\";a:6:{s:2:\\\"id\\\";i:59;s:9:\\\"listorder\\\";i:59;s:5:\\\"venue\\\";s:8:\\\"European\\\";s:3:\\\"url\\\";s:8:\\\"european\\\";s:3:\\\"fid\\\";i:0;s:18:\\\"total_events_count\\\";i:0;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:3:\\\"fid\\\";s:7:\\\"integer\\\";s:9:\\\"listorder\\\";s:7:\\\"integer\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:0;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:4:{i:0;s:3:\\\"fid\\\";i:1;s:5:\\\"venue\\\";i:2;s:3:\\\"url\\\";i:3;s:9:\\\"listorder\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}}s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;}') on duplicate key update `expiration` = values(`expiration`), `key` = values(`key`), `value` = values(`value`)", "type": "query", "params": [], "bindings": [1752666179, "iconf_meeting_cache_conference_distribution_continents", "O:39:\"Illuminate\\Database\\Eloquent\\Collection\":2:{s:8:\"\u0000*\u0000items\";a:6:{i:0;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:6:{s:2:\"id\";i:102;s:9:\"listorder\";i:102;s:5:\"venue\";s:6:\"Africa\";s:3:\"url\";s:6:\"africa\";s:3:\"fid\";i:0;s:18:\"total_events_count\";i:1;}s:11:\"\u0000*\u0000original\";a:6:{s:2:\"id\";i:102;s:9:\"listorder\";i:102;s:5:\"venue\";s:6:\"Africa\";s:3:\"url\";s:6:\"africa\";s:3:\"fid\";i:0;s:18:\"total_events_count\";i:1;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}i:1;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:6:{s:2:\"id\";i:128;s:9:\"listorder\";i:128;s:5:\"venue\";s:7:\"Oceania\";s:3:\"url\";s:7:\"oceania\";s:3:\"fid\";i:0;s:18:\"total_events_count\";i:0;}s:11:\"\u0000*\u0000original\";a:6:{s:2:\"id\";i:128;s:9:\"listorder\";i:128;s:5:\"venue\";s:7:\"Oceania\";s:3:\"url\";s:7:\"oceania\";s:3:\"fid\";i:0;s:18:\"total_events_count\";i:0;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}i:2;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:6:{s:2:\"id\";i:11;s:9:\"listorder\";i:11;s:5:\"venue\";s:4:\"Asia\";s:3:\"url\";s:4:\"asia\";s:3:\"fid\";i:0;s:18:\"total_events_count\";i:0;}s:11:\"\u0000*\u0000original\";a:6:{s:2:\"id\";i:11;s:9:\"listorder\";i:11;s:5:\"venue\";s:4:\"Asia\";s:3:\"url\";s:4:\"asia\";s:3:\"fid\";i:0;s:18:\"total_events_count\";i:0;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}i:3;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:6:{s:2:\"id\";i:176;s:9:\"listorder\";i:176;s:5:\"venue\";s:13:\"South America\";s:3:\"url\";s:13:\"south_america\";s:3:\"fid\";i:0;s:18:\"total_events_count\";i:0;}s:11:\"\u0000*\u0000original\";a:6:{s:2:\"id\";i:176;s:9:\"listorder\";i:176;s:5:\"venue\";s:13:\"South America\";s:3:\"url\";s:13:\"south_america\";s:3:\"fid\";i:0;s:18:\"total_events_count\";i:0;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}i:4;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:6:{s:2:\"id\";i:145;s:9:\"listorder\";i:145;s:5:\"venue\";s:13:\"North America\";s:3:\"url\";s:13:\"north_america\";s:3:\"fid\";i:0;s:18:\"total_events_count\";i:0;}s:11:\"\u0000*\u0000original\";a:6:{s:2:\"id\";i:145;s:9:\"listorder\";i:145;s:5:\"venue\";s:13:\"North America\";s:3:\"url\";s:13:\"north_america\";s:3:\"fid\";i:0;s:18:\"total_events_count\";i:0;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}i:5;O:18:\"App\\Models\\Country\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:7:\"country\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:6:{s:2:\"id\";i:59;s:9:\"listorder\";i:59;s:5:\"venue\";s:8:\"European\";s:3:\"url\";s:8:\"european\";s:3:\"fid\";i:0;s:18:\"total_events_count\";i:0;}s:11:\"\u0000*\u0000original\";a:6:{s:2:\"id\";i:59;s:9:\"listorder\";i:59;s:5:\"venue\";s:8:\"European\";s:3:\"url\";s:8:\"european\";s:3:\"fid\";i:0;s:18:\"total_events_count\";i:0;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:3:\"fid\";s:7:\"integer\";s:9:\"listorder\";s:7:\"integer\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:0;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:4:{i:0;s:3:\"fid\";i:1;s:5:\"venue\";i:2;s:3:\"url\";i:3;s:9:\"listorder\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}}s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;}"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 191}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 167}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 241}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 433}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.9379392, "duration": 0.02274, "duration_str": "22.74ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:191", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 191}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=191", "ajax": false, "filename": "DatabaseStore.php", "line": "191"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 86.409, "width_percent": 5.576}, {"sql": "insert into `cache` (`expiration`, `key`, `value`) values (1752666179, 'iconf_meeting_cache_all_continent_conference_data_for_js', 'a:6:{s:6:\\\"africa\\\";a:4:{s:4:\\\"name\\\";s:6:\\\"Africa\\\";s:11:\\\"conferences\\\";i:1;s:9:\\\"countries\\\";a:0:{}s:13:\\\"topCategories\\\";a:0:{}}s:7:\\\"oceania\\\";a:4:{s:4:\\\"name\\\";s:7:\\\"Oceania\\\";s:11:\\\"conferences\\\";i:0;s:9:\\\"countries\\\";a:0:{}s:13:\\\"topCategories\\\";a:0:{}}s:4:\\\"asia\\\";a:4:{s:4:\\\"name\\\";s:4:\\\"Asia\\\";s:11:\\\"conferences\\\";i:0;s:9:\\\"countries\\\";a:0:{}s:13:\\\"topCategories\\\";a:0:{}}s:13:\\\"south-america\\\";a:4:{s:4:\\\"name\\\";s:13:\\\"South America\\\";s:11:\\\"conferences\\\";i:0;s:9:\\\"countries\\\";a:0:{}s:13:\\\"topCategories\\\";a:0:{}}s:13:\\\"north-america\\\";a:4:{s:4:\\\"name\\\";s:13:\\\"North America\\\";s:11:\\\"conferences\\\";i:0;s:9:\\\"countries\\\";a:0:{}s:13:\\\"topCategories\\\";a:0:{}}s:8:\\\"european\\\";a:4:{s:4:\\\"name\\\";s:8:\\\"European\\\";s:11:\\\"conferences\\\";i:0;s:9:\\\"countries\\\";a:0:{}s:13:\\\"topCategories\\\";a:0:{}}}') on duplicate key update `expiration` = values(`expiration`), `key` = values(`key`), `value` = values(`value`)", "type": "query", "params": [], "bindings": [1752666179, "iconf_meeting_cache_all_continent_conference_data_for_js", "a:6:{s:6:\"africa\";a:4:{s:4:\"name\";s:6:\"Africa\";s:11:\"conferences\";i:1;s:9:\"countries\";a:0:{}s:13:\"topCategories\";a:0:{}}s:7:\"oceania\";a:4:{s:4:\"name\";s:7:\"Oceania\";s:11:\"conferences\";i:0;s:9:\"countries\";a:0:{}s:13:\"topCategories\";a:0:{}}s:4:\"asia\";a:4:{s:4:\"name\";s:4:\"Asia\";s:11:\"conferences\";i:0;s:9:\"countries\";a:0:{}s:13:\"topCategories\";a:0:{}}s:13:\"south-america\";a:4:{s:4:\"name\";s:13:\"South America\";s:11:\"conferences\";i:0;s:9:\"countries\";a:0:{}s:13:\"topCategories\";a:0:{}}s:13:\"north-america\";a:4:{s:4:\"name\";s:13:\"North America\";s:11:\"conferences\";i:0;s:9:\"countries\";a:0:{}s:13:\"topCategories\";a:0:{}}s:8:\"european\";a:4:{s:4:\"name\";s:8:\"European\";s:11:\"conferences\";i:0;s:9:\"countries\";a:0:{}s:13:\"topCategories\";a:0:{}}}"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 191}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 167}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 241}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 433}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.9611828, "duration": 0.031129999999999998, "duration_str": "31.13ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:191", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 191}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=191", "ajax": false, "filename": "DatabaseStore.php", "line": "191"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 91.984, "width_percent": 7.633}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.top')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.top"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.995837, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 99.617, "width_percent": 0.049}, {"sql": "select * from `html_fragments` where `is_active` = 1 and `type` = 'js'", "type": "query", "params": [], "bindings": [1, "js"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/View/Composers/HtmlFragmentComposer.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Composers\\HtmlFragmentComposer.php", "line": 19}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 189}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160}], "start": **********.999614, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HtmlFragmentComposer.php:19", "source": {"index": 15, "namespace": null, "name": "app/View/Composers/HtmlFragmentComposer.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Composers\\HtmlFragmentComposer.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComposers%2FHtmlFragmentComposer.php&line=19", "ajax": false, "filename": "HtmlFragmentComposer.php", "line": "19"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 99.667, "width_percent": 0.123}, {"sql": "select * from `links` where `status` = 1 order by `listorder` asc, `id` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/View/Components/FriendlyLinks.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\FriendlyLinks.php", "line": 23}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 110}, {"index": 17, "namespace": "view", "name": "frontend.layouts.app", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/frontend/layouts/app.blade.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.0029008, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "FriendlyLinks.php:23", "source": {"index": 15, "namespace": null, "name": "app/View/Components/FriendlyLinks.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\FriendlyLinks.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FFriendlyLinks.php&line=23", "ajax": false, "filename": "FriendlyLinks.php", "line": "23"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 99.789, "width_percent": 0.15}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_html_fragment_footer')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_html_fragment_footer"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "app/Services/HtmlFragmentService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\HtmlFragmentService.php", "line": 36}], "start": **********.004447, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 99.939, "width_percent": 0.061}]}, "models": {"data": {"App\\Models\\Category": {"value": 49, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Country": {"value": 25, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FCountry.php&line=1", "ajax": false, "filename": "Country.php", "line": "?"}}, "App\\Models\\Event": {"value": 23, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "App\\Models\\News": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FNews.php&line=1", "ajax": false, "filename": "News.php", "line": "?"}}, "App\\Models\\Link": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FLink.php&line=1", "ajax": false, "filename": "Link.php", "line": "?"}}, "App\\Models\\Advertisement": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FAdvertisement.php&line=1", "ajax": false, "filename": "Advertisement.php", "line": "?"}}, "App\\Models\\HtmlFragment": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FHtmlFragment.php&line=1", "ajax": false, "filename": "HtmlFragment.php", "line": "?"}}}, "count": 117, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://iconf.lv", "action_name": "home", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@index", "uri": "GET /", "controller": "App\\Http\\Controllers\\Frontend\\HomeController@index<a href=\"phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=46\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=46\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/HomeController.php:46-69</a>", "middleware": "web", "duration": "660ms", "peak_memory": "48MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-96114964 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-96114964\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-513578074 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-513578074\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2004185115 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"719 characters\">XSRF-TOKEN=eyJpdiI6Ijkyd1IvbXJ2ZWJMamJjemNacVpPTnc9PSIsInZhbHVlIjoiNzF3WUhxTmZsajZzQjc2blY1bmFmbTJyQkpYNjVLYTJncnYycVZ3cTlpZHVDU2U4SVBhRmQvVGd3T1hhVnhRaG1JSWN3cFhzU1I5SWZKRExoRG9Fem01by9rVEgvSDl1VHl2WUNWemNBNHhVZHZ1VXA2QWpjS1hkNlA0Q0pDM0wiLCJtYWMiOiI1Nzg5NDAxZmExOWM3OGY5NDJkZTBiNjdjYzRiODY1MjZiMjhkMDViMjJjOWZmOTBiMTVmZDcyNjAzNGM5NzFhIiwidGFnIjoiIn0%3D; iconf_meeting_session=eyJpdiI6Ik5lR09pMzZyM3h5N2l2YW1RWmZaR1E9PSIsInZhbHVlIjoiaGI2STh2OVNiQTVRR1FLdExtRk52L2JqUjgzQW5CN3R4VWNPK2ZmaFdJczdHUFQ1dDhWTk16aWRlSm9kdEdzN1V3MHlvRm5STmFocG5OdER5ejVZWXhmSmcvQTBmVW5BY2R0OTU2QTk3NEN5ZE5kSjdiVWVPQVViRTRXWndrS2oiLCJtYWMiOiI2MWFlOGZhM2Q4ZTg3ZDg4YTkxYTdmNjhmNTNiZTIzOGRlYjZhYTJmMzI4Y2Q4YzA3NzZlYmMzMjMwYzFlNTVkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">http://iconf.lv/conferences?category=&amp;country=&amp;year=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">iconf.lv</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2004185115\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-323988072 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KzVnHwsb1j7aFLoepnAgBRMhnc1xQgVO8Wwk7tZ4</span>\"\n  \"<span class=sf-dump-key>iconf_meeting_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">n81JYPLEP0cENw7ZqI8BBaufrhZyV3CgHX5ObznX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-323988072\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1517315995 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 16 Jul 2025 11:41:59 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1517315995\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2023948719 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KzVnHwsb1j7aFLoepnAgBRMhnc1xQgVO8Wwk7tZ4</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://iconf.lv/conferences</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$i.a5gP7/Z07pk3CeR6vNfe9lGm.BtthHlLi1.olj0JJDfdNlCRY8K</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2023948719\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://iconf.lv", "action_name": "home", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@index"}, "badge": null}}