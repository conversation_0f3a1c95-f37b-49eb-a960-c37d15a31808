# 现在是2025年7月，你是一位世界顶级的web全栈开发人员，特别擅长laravel12  ,tailwindcss、Livewire的应用开发，汇聚了2025年最前沿的创意和技术。

现在需要你认真分析整个code（ 一个国际化的学术会议集合的网站），完成新功能


# 美化会议筛选器的界面，完善用户体验，使其符合国际化的用户体验

涉及到的文件： 
- resources\views\frontend\home\conferences.blade.php
- 具体的组件：<livewire:conference-filter />


# # 现在是2025年7月，你是一位世界顶级的web全栈开发人员，特别擅长laravel12 的应用开发，对seo 也有着深刻的见解。


现在需要你认真分析整个code（ 一个国际化的学术会议集合的网站），完成网站sitemap 的功能：

- 按照谷歌等搜索引擎的最佳指南，完成全站sitemap的生成，地址是：http://127.0.0.1:8000/sitemap （功能没有实现，路由待配置）
- 主要体现会议列表，会议分类导航、国家地区导航、新闻列表等动态URL功能，能有效提升网站的收录。


# 现在是2025年7月，你是一位世界顶级的web全栈开发人员，特别擅长laravel12  ,tailwindcss、Livewire的应用开发，汇聚了2025年最前沿的创意和技术。

现在需要你认真分析整个code（ 一个国际化的学术会议集合的网站），完成新功能

目前网页模板前端都是用cdn 引入的，还有一些自定义的样式和脚本混杂在页面里，我准备将其打包发布到vite 环境下，一边测试一遍完善。
我还没有接触过前端打包，结合项目，给我一个保姆级的迁移、打包的说明教程。生成一个md 文档，放在doc目录下