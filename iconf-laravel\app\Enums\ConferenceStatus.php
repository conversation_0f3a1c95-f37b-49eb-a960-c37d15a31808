<?php

namespace App\Enums;

/**
 * 会议状态枚举
 * 基于老项目的status字段值
 */
enum ConferenceStatus: int
{
    case Pending = 0;      // 待审核
    case Published = 1;    // 已发布
    case Rejected = 2;     // 已拒绝
    case Draft = 3;        // 草稿

    /**
     * 获取状态标签
     */
    public function label(): string
    {
        return match($this) {
            self::Pending => '待审核',
            self::Published => '已发布',
            self::Rejected => '已拒绝',
            self::Draft => '草稿',
        };
    }

    /**
     * 获取状态颜色（用于UI显示）
     */
    public function color(): string
    {
        return match($this) {
            self::Pending => 'warning',
            self::Published => 'success',
            self::Rejected => 'danger',
            self::Draft => 'secondary',
        };
    }

    /**
     * 获取所有状态选项
     */
    public static function options(): array
    {
        return collect(self::cases())
            ->mapWithKeys(fn($status) => [$status->value => $status->label()])
            ->toArray();
    }

    /**
     * 检查是否为已发布状态
     */
    public function isPublished(): bool
    {
        return $this === self::Published;
    }

    /**
     * 检查是否为待审核状态
     */
    public function isPending(): bool
    {
        return $this === self::Pending;
    }
}
