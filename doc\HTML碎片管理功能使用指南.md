# HTML碎片管理功能使用指南

*文档创建日期：2025年6月26日*

## 1. 功能概述

HTML碎片管理功能是一个灵活的内容管理系统，允许管理员在后台添加、编辑和删除HTML碎片，并通过简单的标签在前端模板中引用这些碎片。这对于频繁更新的内容区域（如导航菜单、页脚版权信息、公告等）特别有用。

### 主要特点

- 基于Laravel 12和Filament Admin构建
- 支持HTML内容的安全过滤，防止XSS攻击
- 使用缓存机制提高性能
- 提供多种调用方式（Blade指令和辅助函数）
- 简洁易用的管理界面

## 2. 安装与配置

该功能已经集成到系统中，无需额外安装。如果需要调整配置，可以修改`config/fragments.php`文件：

```php
// 缓存时间（分钟）
'cache_ttl' => env('FRAGMENT_CACHE_TTL', 1440), // 默认24小时

// 是否启用缓存
'cache_enabled' => env('FRAGMENT_CACHE_ENABLED', true),

// 缓存驱动
'cache_driver' => env('FRAGMENT_CACHE_DRIVER', null), // null表示使用默认缓存驱动
```

也可以在`.env`文件中设置这些值：

```
FRAGMENT_CACHE_TTL=1440
FRAGMENT_CACHE_ENABLED=true
FRAGMENT_CACHE_DRIVER=file
```

## 3. 在后台管理碎片

### 3.1 访问管理界面

1. 登录Filament Admin后台
2. 在侧边栏找到"HTML碎片管理"菜单项
3. 点击进入管理列表

### 3.2 创建新碎片

1. 在HTML碎片管理页面，点击"新建"按钮
2. 填写以下信息：
   - **碎片标识**：唯一标识符，用于在代码中引用碎片（如`main_navigation`）
   - **碎片别称**：方便在后台识别的名称（如"主导航栏"）
   - **描述**：（可选）简要说明碎片的用途和位置
   - **碎片内容**：HTML代码
3. 点击"保存"按钮

### 3.3 编辑或删除碎片

- 在列表页面，点击对应碎片行的"编辑"或"删除"按钮
- 编辑页面与创建页面相同，修改后点击"保存"即可

## 4. 在前端使用碎片

### 4.1 使用Blade指令

在Blade模板中，可以使用`@htmlFragment`指令引用碎片：

```blade
{{-- 在布局或视图文件中 --}}
<header>
    <nav>
        @htmlFragment('main_navigation', '<p>默认导航内容</p>')
    </nav>
</header>

<footer>
    @htmlFragment('footer_copyright', '© 2025 学术会议网站')
</footer>
```

第一个参数是碎片标识，第二个参数是默认内容（当碎片不存在时显示）。

### 4.2 使用辅助函数

在PHP代码中，可以使用`html_fragment`辅助函数：

```php
// 在控制器或其他PHP文件中
$navigationHtml = html_fragment('main_navigation', '<p>默认导航内容</p>');

// 在视图中输出
echo $navigationHtml;
```

## 5. 缓存管理

### 5.1 自动缓存

- 碎片内容会自动缓存，默认缓存时间为24小时
- 当碎片被更新或删除时，相关缓存会自动清除

### 5.2 手动清除缓存

如需手动清除所有缓存：

```bash
php artisan cache:clear
```

## 6. 安全性说明

所有HTML内容在保存前都会通过HTML Purifier进行净化，以防止XSS攻击。这意味着某些不安全的HTML标签和属性可能会被过滤掉。

如果需要调整HTML Purifier的配置，可以修改`config/purifier.php`文件（如果不存在，可以通过`php artisan vendor:publish --provider="Mews\Purifier\PurifierServiceProvider"`命令发布）。

## 7. 最佳实践

- 为碎片使用有意义的标识符，如`main_nav`、`footer_links`等
- 在添加复杂HTML时，先在外部编辑器中编写和测试，然后再粘贴到后台
- 对于频繁更新的内容，考虑减少缓存时间
- 对于很少更新的内容，可以增加缓存时间以提高性能

## 8. 故障排除

### 8.1 碎片不显示

- 检查标识符是否正确
- 确认碎片已在后台创建
- 尝试清除缓存
- 检查HTML内容是否被过滤

### 8.2 HTML内容被过滤

如果发现某些HTML标签或属性被移除，可能是因为HTML Purifier的安全设置。请检查`config/purifier.php`中的配置，并根据需要调整允许的标签和属性。

---

如有任何问题或建议，请联系技术支持团队。
