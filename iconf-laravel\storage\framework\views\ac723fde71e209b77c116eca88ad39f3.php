

<div class="category-accordion <?php echo e($class); ?>">
    <?php if($showTitle): ?>
    <h3 class="text-xl font-bold text-gray-800 mb-4"><?php echo e($title); ?></h3>
    <?php endif; ?>

    <?php if(count($categories) > 0): ?>
    <div x-data="{ activeAccordion: '<?php echo e($activeId); ?>' }" class="space-y-3">
        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if($category->url): ?>
        <div class="bg-white rounded-lg shadow-sm transition-all duration-300 ease-in-out"
            :class="{ 'bg-blue-50': activeAccordion == <?php echo e($category->id); ?> }">

            <!-- 一级分类标题 -->
            <div @click="activeAccordion = activeAccordion == <?php echo e($category->id); ?> ? null : <?php echo e($category->id); ?>"
                class="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 rounded-lg">

                <a href="<?php echo e(route('categories.lists', $category->url)); ?>"
                    @click.stop
                    class="flex-grow font-semibold transition-colors <?php echo e($currentUrl === $category->url ? 'text-blue-600' : 'text-gray-700 hover:text-blue-600'); ?>">
                    <?php echo $category->name; ?>

                </a>

                <?php if(isset($category->children) && $category->children->count() > 0): ?>
                <svg class="w-5 h-5 text-gray-500 transition-transform duration-300 ease-in-out"
                    :class="{ 'transform rotate-180 text-blue-600': activeAccordion == <?php echo e($category->id); ?> }"
                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
                <?php endif; ?>
            </div>

            <!-- 二级分类内容 -->
            <?php if(isset($category->children) && $category->children->count() > 0): ?>
            <div x-show="activeAccordion == <?php echo e($category->id); ?>"
                x-transition:enter="transition ease-out duration-300"
                x-transition:enter-start="opacity-0 transform -translate-y-2"
                x-transition:enter-end="opacity-100 transform translate-y-0"
                x-transition:leave="transition ease-in duration-200"
                x-transition:leave-start="opacity-100 transform translate-y-0"
                x-transition:leave-end="opacity-0 transform -translate-y-2"
                class="bg-white rounded-b-lg overflow-hidden"
                style="display: none;">

                <ul class="py-2 px-4">
                    <?php $__currentLoopData = $category->children; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($subCategory->url): ?>
                    <li class="group <?php echo e(!$loop->last ? 'border-b border-gray-100 pb-2 mb-2' : 'pb-2'); ?>">
                        <a href="<?php echo e(route('categories.lists', $subCategory->url)); ?>"
                            class="flex items-center p-2 rounded-md transition-colors
                                       <?php echo e($currentUrl === $subCategory->url ? 'bg-blue-50 text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-50 hover:text-blue-600'); ?>">
                            <span class="w-1.5 h-1.5 rounded-full mr-3 transition-colors
                                             <?php echo e($currentUrl === $subCategory->url ? 'bg-blue-500' : 'bg-gray-400 group-hover:bg-gray-500'); ?>"></span>
                            <?php echo $subCategory->name; ?>

                        </a>
                    </li>
                    <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
    <?php else: ?>
    <p class="text-gray-500">暂无分类数据</p>
    <?php endif; ?>
</div>

<!-- 确保Alpine.js可用 -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof window.Alpine !== 'undefined') return;
        var script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.8.2/dist/alpine.min.js';
        script.defer = true;
        document.head.appendChild(script);
    });
</script><?php /**PATH D:\phpEnv\www\iconf_org_by_laravel\iconf-laravel\resources\views/components/category/accordion.blade.php ENDPATH**/ ?>