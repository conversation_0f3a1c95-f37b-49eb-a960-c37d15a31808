<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Category;

class TestCategoryUrl extends Command
{
    protected $signature = 'test:category-url {url}';
    protected $description = 'Test category URL matching';

    public function handle()
    {
        $inputUrl = $this->argument('url');
        
        $this->info("Testing URL: {$inputUrl}");
        
        // 获取所有分类
        $categories = Category::all();
        
        $this->info("Total categories: " . $categories->count());
        
        // 显示所有分类URL
        $this->info("All category URLs:");
        foreach ($categories as $category) {
            $this->line("ID: {$category->id}, URL: '{$category->url}', Name: {$category->name}");
        }
        
        $this->info("\n--- Testing URL matching ---");
        
        // 1. 直接匹配
        $category = $categories->firstWhere('url', $inputUrl);
        $this->info("1. Direct match: " . ($category ? "Found ID {$category->id}" : "Not found"));
        
        // 2. URL解码匹配
        $decodedUrl = urldecode($inputUrl);
        if ($decodedUrl !== $inputUrl) {
            $category = $categories->firstWhere('url', $decodedUrl);
            $this->info("2. URL decoded '{$decodedUrl}': " . ($category ? "Found ID {$category->id}" : "Not found"));
        }
        
        // 3. HTML实体编码匹配（将输入URL编码后匹配）
        $htmlEncodedUrl = htmlentities($inputUrl);
        if ($htmlEncodedUrl !== $inputUrl) {
            $category = $categories->firstWhere('url', $htmlEncodedUrl);
            $this->info("3. HTML encoded '{$htmlEncodedUrl}': " . ($category ? "Found ID {$category->id}" : "Not found"));
        }

        // 4. HTML实体解码匹配
        $htmlDecodedUrl = html_entity_decode($inputUrl);
        if ($htmlDecodedUrl !== $inputUrl) {
            $category = $categories->firstWhere('url', $htmlDecodedUrl);
            $this->info("4. HTML decoded '{$htmlDecodedUrl}': " . ($category ? "Found ID {$category->id}" : "Not found"));
        }
        
        // 4. 反向测试：将数据库中的URL进行HTML编码
        $this->info("\n--- Reverse testing (encode DB URLs) ---");
        foreach ($categories as $cat) {
            $encodedUrl = htmlentities($cat->url);
            if ($encodedUrl === $inputUrl) {
                $this->info("Found match by encoding DB URL '{$cat->url}' to '{$encodedUrl}' - ID: {$cat->id}");
                break;
            }
        }
        
        return 0;
    }
}
