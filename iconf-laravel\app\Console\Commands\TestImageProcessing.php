<?php

namespace App\Console\Commands;

use App\Models\News;
use Illuminate\Console\Command;

class TestImageProcessing extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:image-processing {id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试图片处理功能';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $id = $this->argument('id');
        $news = News::find($id);

        if (!$news) {
            $this->error("新闻ID {$id} 不存在");
            return;
        }

        $this->info("📰 测试新闻图片处理 (ID: {$news->id})");
        $this->info("📝 标题: {$news->title}");
        $this->newLine();

        // 检查是否包含图片附件
        $hasImages = str_contains($news->content, 'data-trix-attachment');
        $this->line("🖼️  包含图片附件: " . ($hasImages ? '是' : '否'));
        $this->newLine();

        if ($hasImages) {
            $this->line("🔤 原始内容 (前800字符):");
            $this->line(mb_substr($news->content, 0, 800) . '...');
            $this->newLine();

            $this->line("✨ 解码后内容 (前800字符):");
            $this->line(mb_substr($news->getDecodedContent(), 0, 800) . '...');
            $this->newLine();

            $this->line("🧹 清理后显示内容:");
            $cleanContent = $news->getCleanDisplayContent();
            $this->line($cleanContent);
            $this->newLine();

            // 检查处理结果
            $hasSimplifiedImages = str_contains($cleanContent, '<img src=');
            $this->line("✅ 图片简化处理: " . ($hasSimplifiedImages ? '成功' : '失败'));
        } else {
            $this->warn("该新闻不包含图片附件");
        }
    }
}
