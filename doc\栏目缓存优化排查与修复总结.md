# 会议栏目缓存优化排查与修复总结

## 问题背景

在实施会议栏目缓存优化方案后，发现前台页面的栏目数据仍然直接从数据库读取，缓存未能生效。用户测试方法是：手动更新栏目缓存后，直接修改数据库中的栏目名称，刷新前台页面发现栏目名称同步变化，说明缓存未生效。同时，缓存目录（`storage/framework/cache/data`）为空，进一步证实了缓存未写入的问题。

## 问题排查过程

### 1. 缓存配置检查

首先检查了 Laravel 的缓存配置文件 `config/cache.php`，发现默认缓存驱动为 `database`，而非设计中优先使用的 `file` 驱动。这解释了为什么文件缓存目录为空。

### 2. 缓存标签兼容性问题

经过进一步研究，发现了根本原因：**Laravel 的数据库和文件缓存驱动均不支持缓存标签（tags）功能**。而我们的 `CategoryService` 使用了标签功能来管理缓存，导致缓存写入失败，系统实际一直是直接从数据库读取数据。

```php
// 原有代码中使用了标签功能
Cache::tags($this->cacheTags)->rememberForever('categories.tree', function () {
    return $this->buildTree();
});
```

Laravel 文档明确指出：只有 Redis 和 Memcached 缓存驱动支持标签功能，而 database 和 file 驱动不支持。

### 3. 前台调用链分析

检查了前台控制器和服务层代码，确认栏目数据调用均通过 `CategoryService` 获取缓存数据，符合设计预期。问题不在于调用方式，而在于缓存本身未能成功写入。

## 解决方案

### 1. 重构 CategoryService 缓存逻辑

决定保留当前缓存驱动配置（database），但重构 `CategoryService`，移除缓存标签用法，改为直接使用 `Cache::rememberForever()`，并实现手动清理所有缓存键的逻辑。

主要改动包括：

1. 移除 `$cacheTags`，改用缓存键前缀 `$cachePrefix`
2. 所有缓存读写操作均使用 `Cache::rememberForever()`，不再使用标签
3. 新增 `clearAllCaches()` 方法，清理所有栏目相关缓存键，替代标签缓存的批量清理
4. `refreshCache()` 方法调用 `clearAllCaches()` 并重新生成缓存

```php
// 重构后的代码示例
protected string $cachePrefix = 'categories';

public function getTree(): Collection
{
    try {
        return Cache::rememberForever("{$this->cachePrefix}.tree", function () {
            return $this->buildTree();
        });
    } catch (\Exception $e) {
        Log::error('获取栏目树失败: ' . $e->getMessage());
        return $this->buildTree();
    }
}

public function clearAllCaches(): void
{
    $cacheKeys = [
        "{$this->cachePrefix}.tree",
        "{$this->cachePrefix}.top",
        "{$this->cachePrefix}.all",
        // 其他缓存键...
    ];
    
    foreach ($cacheKeys as $key) {
        Cache::forget($key);
    }
    
    Log::info('所有栏目缓存已清理');
}
```

### 2. 优化 N+1 查询问题

在解决缓存问题后，发现 `/conferences` 页面仍然存在大量栏目表查询，这是由于 N+1 查询问题导致的。优化方案如下：

1. **从缓存一次性获取所有分类数据**
   ```php
   $allCategories = $this->categoryService->getTree()->flatten(1);
   $categoryMap = $allCategories->keyBy('id');
   ```

2. **移除模型关联的自动预加载**
   ```php
   // 修改前
   $query = Event::published()->with(['country:id,venue', 'categories:id,name']);
   
   // 修改后
   $query = Event::published()->with(['country:id,venue']); // 不再预加载分类
   ```

3. **手动加载会议与分类的关系**
   ```php
   $eventIds = $allConferences->pluck('id')->toArray();
   $eventCategoryRelations = \DB::table('list')
       ->whereIn('eid', $eventIds)
       ->get(['eid', 'cid']);
   ```

4. **使用缓存的分类数据构建关系**
   ```php
   foreach ($eventCategoryRelations as $relation) {
       if (!isset($eventCategoriesMap[$relation->eid])) {
           $eventCategoriesMap[$relation->eid] = [];
       }
       // 只有当分类存在于缓存中时才添加
       if ($categoryMap->has($relation->cid)) {
           $eventCategoriesMap[$relation->eid][] = $categoryMap[$relation->cid];
       }
   }
   ```

5. **手动将分类附加到会议对象**
   ```php
   foreach ($allConferences as $conference) {
       $conference->categories = collect($eventCategoriesMap[$conference->id] ?? []);
   }
   ```

## 经验教训与最佳实践

1. **了解缓存驱动的限制**：在使用 Laravel 缓存功能时，必须了解不同缓存驱动的特性和限制。特别是缓存标签功能只在 Redis 和 Memcached 驱动中可用。

2. **选择合适的缓存驱动**：
   - 如果需要使用标签功能，应选择 Redis 或 Memcached
   - 如果使用 database 或 file 驱动，需避免使用标签功能

3. **避免 N+1 查询**：
   - 使用预加载（Eager Loading）减少查询次数
   - 对于复杂场景，考虑手动加载关联数据
   - 利用缓存数据减少数据库查询

4. **缓存键管理**：
   - 使用前缀区分不同类型的缓存
   - 维护缓存键列表，便于清理
   - 实现有效的缓存刷新机制

5. **性能监控**：
   - 定期检查数据库查询性能
   - 监控缓存命中率
   - 使用调试工具（如 Laravel Debugbar）识别性能瓶颈

## 后续建议

1. **考虑迁移到 Redis 缓存**：如果项目对缓存依赖较高，可考虑迁移到 Redis 缓存驱动，以便利用标签功能进行更灵活的缓存管理。

2. **扩展缓存优化范围**：将类似的缓存优化和 N+1 查询优化应用到其他高频访问页面。

3. **添加缓存监控**：实现缓存命中率监控，以便及时发现缓存失效问题。

4. **完善文档**：更新缓存设计文档，明确记录缓存驱动限制及当前解决方案。
