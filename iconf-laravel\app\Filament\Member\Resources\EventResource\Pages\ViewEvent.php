<?php

namespace App\Filament\Member\Resources\EventResource\Pages;

use App\Filament\Member\Resources\EventResource;
use App\Enums\ConferenceStatus;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Illuminate\Support\Facades\Auth;

class ViewEvent extends ViewRecord
{
    protected static string $resource = EventResource::class;

    protected static ?string $title = 'View Conference';

    /**
     * Permission check on page mount
     */
    public function mount(int | string $record): void
    {
        parent::mount($record);

        $member = Auth::guard('member')->user();

        // Check if the user owns this conference
        if ($this->record->uid !== $member->id) {
            abort(403, 'You do not have permission to view this conference');
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->label('Edit Conference'),
                
            Actions\Action::make('viewOnSite')
                ->label('View on Site')
                ->icon('heroicon-o-eye')
                ->color('info')
                ->url(fn () => url("/conference/{$this->record->url}"))
                ->openUrlInNewTab()
                ->visible(fn () => $this->record->status === ConferenceStatus::Published),
                
            Actions\DeleteAction::make()
                ->label('Delete Conference')
                ->requiresConfirmation()
                ->modalHeading('Delete Conference')
                ->modalDescription('Are you sure you want to delete this conference? This action cannot be undone.'),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Basic Information')
                    ->schema([
                        Infolists\Components\TextEntry::make('title')
                            ->label('Conference Title')
                            ->size(Infolists\Components\TextEntry\TextEntrySize::Large)
                            ->weight('bold'),
                            
                        Infolists\Components\TextEntry::make('event')
                            ->label('Short Name'),
                            
                        Infolists\Components\TextEntry::make('url')
                            ->label('URL Alias')
                            ->url(fn ($record) => url("/conference/{$record->url}"))
                            ->openUrlInNewTab()
                            ->visible(fn ($record) => $record->status === ConferenceStatus::Published),
                            
                        Infolists\Components\TextEntry::make('status')
                            ->label('Status')
                            ->badge()
                            ->formatStateUsing(fn (ConferenceStatus $state): string => $state->label())
                            ->color(fn (ConferenceStatus $state): string => $state->color()),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Category Information')
                    ->schema([
                        Infolists\Components\TextEntry::make('categories.name')
                            ->label('Conference Categories')
                            ->listWithLineBreaks()
                            ->bulleted(),
                    ])
                    ->visible(fn ($record) => $record->categories->isNotEmpty()),

                Infolists\Components\Section::make('Time Information')
                    ->schema([
                        Infolists\Components\TextEntry::make('start_date')
                            ->label('Start Time')
                            ->formatStateUsing(fn ($state) => $state ? date('Y-m-d H:i', $state) : 'N/A'),

                        Infolists\Components\TextEntry::make('end_date')
                            ->label('End Time')
                            ->formatStateUsing(fn ($state) => $state ? date('Y-m-d H:i', $state) : 'N/A'),

                        Infolists\Components\TextEntry::make('sub_date')
                            ->label('Submission Deadline')
                            ->formatStateUsing(fn ($state) => $state ? date('Y-m-d H:i', $state) : 'N/A')
                            ->placeholder('N/A'),
                    ])
                    ->columns(3),

                Infolists\Components\Section::make('Location Information')
                    ->schema([
                        Infolists\Components\TextEntry::make('country.venue')
                            ->label('Venue'),
                            
                        Infolists\Components\TextEntry::make('city')
                            ->label('City')
                            ->placeholder('N/A'),
                            
                        Infolists\Components\TextEntry::make('hotel')
                            ->label('Hotel/Venue')
                            ->placeholder('N/A'),
                    ])
                    ->columns(3),

                Infolists\Components\Section::make('Contact Information')
                    ->schema([
                        Infolists\Components\TextEntry::make('email')
                            ->label('Contact Email')
                            ->icon('heroicon-m-envelope')
                            ->copyable()
                            ->placeholder('N/A'),
                            
                        Infolists\Components\TextEntry::make('web')
                            ->label('Official Website')
                            ->icon('heroicon-m-globe-alt')
                            ->url(fn ($record) => $record->web)
                            ->openUrlInNewTab()
                            ->placeholder('N/A'),
                            
                        Infolists\Components\TextEntry::make('tel')
                            ->label('Contact Phone')
                            ->icon('heroicon-m-phone')
                            ->copyable()
                            ->placeholder('N/A'),
                    ])
                    ->columns(3),

                Infolists\Components\Section::make('Conference Description')
                    ->schema([
                        Infolists\Components\TextEntry::make('summary')
                            ->label('Summary')
                            ->placeholder('N/A')
                            ->columnSpanFull(),
                            
                        Infolists\Components\TextEntry::make('content')
                            ->label('Detailed Description')
                            ->html()
                            ->placeholder('N/A')
                            ->columnSpanFull(),
                    ]),

                Infolists\Components\Section::make('Image Information')
                    ->schema([
                        Infolists\Components\ImageEntry::make('pic')
                            ->label('Conference Image')
                            ->size(200),
                    ])
                    ->visible(fn ($record) => !empty($record->pic)),

                Infolists\Components\Section::make('System Information')
                    ->schema([
                        Infolists\Components\TextEntry::make('addtime')
                            ->label('Publish Time')
                            ->formatStateUsing(fn ($state) => $state ? date('Y-m-d H:i:s', $state) : 'N/A'),
                    ]),
            ]);
    }
}
