#!/bin/bash

echo "========================================"
echo "iConf项目图表导出工具"
echo "========================================"
echo

# 检查是否安装了mermaid-cli
if ! command -v mmdc &> /dev/null; then
    echo "错误: 未找到mermaid-cli工具"
    echo "请先安装: npm install -g @mermaid-js/mermaid-cli"
    echo
    exit 1
fi

# 创建输出目录
mkdir -p charts

echo "开始导出图表..."
echo

# 1. 导出项目架构图
echo "1. 导出项目架构图..."
cat > temp_architecture.mmd << 'EOF'
graph TB
    subgraph "前端展示层"
        A[用户访问] --> B[Bootstrap + jQuery界面]
        B --> C[响应式设计]
    end
    
    subgraph "应用层 - ThinkPHP 3.2"
        D[Home模块 - 前台] --> E[IndexController]
        D --> F[MemberController]
        D --> G[CheckloginController]
        
        H[AdminICF模块 - 后台] --> I[IndexController]
        H --> J[LoginController]
        H --> K[CheckAdminController]
        
        L[Common模块 - 公共] --> M[function.php]
        L --> N[config.php]
        L --> O[seo_config.php]
    end
    
    subgraph "业务逻辑层"
        P[EventModel] --> Q[会议管理]
        R[MemberModel] --> S[用户管理]
        T[CategoryModel] --> U[分类管理]
        V[NewsModel] --> W[新闻管理]
    end
    
    subgraph "数据存储层"
        X[(MySQL数据库)] --> Y[event - 会议表]
        X --> Z[member - 用户表]
        X --> AA[category - 分类表]
        X --> BB[news - 新闻表]
        X --> CC[ad_txt - 广告表]
        X --> DD[country - 地区表]
        X --> EE[list - 关联表]
        
        FF[文件存储] --> GG[Uploads目录]
        GG --> HH[按日期分目录]
        HH --> II[图片文件]
        HH --> JJ[文档文件]
    end
    
    subgraph "核心功能模块"
        KK[会议发布] --> LL[用户提交]
        LL --> MM[管理员审核]
        MM --> NN[前台展示]
        
        OO[分类管理] --> PP[二级分类]
        PP --> QQ[多分类归属]
        
        RR[用户系统] --> SS[注册登录]
        SS --> TT[权限控制]
        TT --> UU[VIP机制]
        
        VV[内容管理] --> WW[新闻发布]
        VV --> XX[广告管理]
        VV --> YY[视频管理]
    end
    
    A --> D
    A --> H
    E --> P
    F --> R
    I --> P
    I --> T
    I --> V
    
    Q --> Y
    S --> Z
    U --> AA
    W --> BB
    
    style A fill:#e1f5fe
    style X fill:#f3e5f5
    style D fill:#e8f5e8
    style H fill:#fff3e0
EOF

if mmdc -i temp_architecture.mmd -o charts/项目架构图.png -w 1200 -H 800; then
    echo "   ✓ 项目架构图导出成功: charts/项目架构图.png"
else
    echo "   ✗ 项目架构图导出失败"
fi

echo

# 2. 导出数据库关系图
echo "2. 导出数据库关系图..."
cat > temp_database.mmd << 'EOF'
erDiagram
    event {
        int id PK
        varchar cid "分类ID(多个)"
        int uid FK
        int venue FK
        varchar city
        varchar hotel
        varchar title
        varchar event
        varchar url
        int start_date
        int end_date
        int sub_date
        varchar email
        varchar web
        varchar tel
        text content
        varchar pic
        tinyint ding "置顶"
        tinyint push "推荐"
        tinyint status "状态"
        int addtime
        varchar summary
        tinyint is_featured "特色推荐"
    }
    
    category {
        int id PK
        int listorder
        int fid "父分类ID"
        varchar name
        varchar url
    }
    
    list {
        int id PK
        int cid FK
        int eid FK
    }
    
    member {
        int id PK
        varchar email
        varchar username
        smallint area
        varchar password
        varchar ip
        varchar regtime
        tinyint vip
        tinyint status
    }
    
    country {
        int id PK
        int listorder
        varchar venue
        varchar url
        int fid "父级ID"
    }
    
    news {
        int id PK
        varchar title
        text content
        timestamp publish_time
        int column_id FK
        varchar cover
        text summary
        int click_rate
        tinyint is_featured
    }
    
    news_type {
        int id PK
        varchar column_name
        int column_sort
    }
    
    ad_txt {
        int id PK
        int listorder
        varchar title
        int cid FK
        varchar link
        varchar pic
        text content
        int endtime
    }
    
    admin {
        int id PK
        varchar username
        varchar password
    }
    
    page {
        int id PK
        varchar title
        text content
    }
    
    vod {
        int id PK
        varchar title
        varchar file_path
        varchar external_link
        text video_code
        varchar cover_image
        int views
        tinyint is_recommended
        varchar vod_type
        timestamp publish_time
    }
    
    %% 关系定义
    event ||--o{ list : "会议分类关联"
    category ||--o{ list : "分类会议关联"
    category ||--o{ category : "父子分类"
    member ||--o{ event : "用户发布会议"
    country ||--o{ event : "会议地点"
    country ||--o{ country : "父子地区"
    news_type ||--o{ news : "新闻分类"
    category ||--o{ ad_txt : "广告分类"
EOF

if mmdc -i temp_database.mmd -o charts/数据库关系图.png -w 1400 -H 1000; then
    echo "   ✓ 数据库关系图导出成功: charts/数据库关系图.png"
else
    echo "   ✗ 数据库关系图导出失败"
fi

echo

# 3. 导出业务流程图
echo "3. 导出业务流程图..."
cat > temp_workflow.mmd << 'EOF'
flowchart TD
    A[用户访问网站] --> B{是否已登录?}
    B -->|否| C[注册/登录]
    B -->|是| D[浏览会议信息]
    
    C --> E[填写注册信息]
    E --> F[邮箱验证]
    F --> G[登录成功]
    G --> D
    
    D --> H[选择会议分类]
    H --> I[查看会议列表]
    I --> J[查看会议详情]
    
    D --> K{用户类型?}
    K -->|普通用户| L[提交会议信息]
    K -->|VIP用户| M[提交会议信息]
    
    L --> N[等待管理员审核]
    N --> O{审核结果?}
    O -->|通过| P[会议发布到前台]
    O -->|拒绝| Q[通知用户修改]
    Q --> L
    
    M --> R[VIP会议直接发布]
    R --> P
    
    P --> S[前台展示]
    S --> T[用户可以浏览]
    
    subgraph "管理员后台"
        U[管理员登录] --> V[会议管理]
        V --> W[审核待审会议]
        W --> X{审核决定?}
        X -->|通过| Y[设置会议状态为通过]
        X -->|拒绝| Z[设置会议状态为拒绝]
        
        V --> AA[分类管理]
        AA --> BB[添加/编辑分类]
        
        V --> CC[内容管理]
        CC --> DD[新闻管理]
        CC --> EE[广告管理]
        CC --> FF[视频管理]
        
        V --> GG[用户管理]
        GG --> HH[查看用户信息]
        GG --> II[设置VIP权限]
    end
    
    Y --> P
    Z --> Q
    
    style A fill:#e1f5fe
    style U fill:#fff3e0
    style V fill:#e8f5e8
EOF

if mmdc -i temp_workflow.mmd -o charts/业务流程图.png -w 1600 -H 1200; then
    echo "   ✓ 业务流程图导出成功: charts/业务流程图.png"
else
    echo "   ✗ 业务流程图导出失败"
fi

echo

# 4. 清理临时文件
echo "4. 清理临时文件..."
rm -f temp_architecture.mmd temp_database.mmd temp_workflow.mmd

echo
echo "========================================"
echo "导出完成！"
echo "图表文件保存在 charts/ 目录中:"
echo "- 项目架构图.png"
echo "- 数据库关系图.png" 
echo "- 业务流程图.png"
echo "========================================"
echo

# 列出生成的文件
if [ -d "charts" ]; then
    echo "生成的文件:"
    ls -la charts/*.png 2>/dev/null || echo "没有找到PNG文件"
fi

echo
echo "使用说明:"
echo "1. 这些图片可以直接插入到文档中"
echo "2. 如需修改图表，请编辑对应的Mermaid代码"
echo "3. 重新运行此脚本可以更新图表"
