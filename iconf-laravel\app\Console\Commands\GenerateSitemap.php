<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\Tags\Url;
use App\Models\Event;
use App\Models\Category;
use App\Models\Country;
use App\Models\News;
use Carbon\Carbon;

class GenerateSitemap extends Command
{
    protected $signature = 'sitemap:generate';
    protected $description = 'Generate the sitemap.';

    public function handle()
    {
        $sitemap = Sitemap::create();

        // Add static pages
        $sitemap->add(Url::create(route('home')));
        $sitemap->add(Url::create(route('conferences.index')));
        $sitemap->add(Url::create(route('news.index')));

        // Add dynamic URLs for Events (newest first by ID)
        Event::where('status', 1)->orderBy('id', 'desc')->each(function (Event $event) use ($sitemap) {
            // Fallback to Carbon::now() if no suitable date field exists, or use a creation date if available
            $lastMod = $event->created_at ?? Carbon::now(); // Assuming 'created_at' exists, otherwise Carbon::now()
            $sitemap->add(Url::create(route('conference.show', $event->url))
                ->setLastModificationDate($lastMod)
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_DAILY)
                ->setPriority(0.9));
        });

        // Add dynamic URLs for Categories (newest first by ID)
        Category::orderBy('id', 'desc')->each(function (Category $category) use ($sitemap) {
            $lastMod = $category->created_at ?? Carbon::now(); // Assuming 'created_at' exists, otherwise Carbon::now()
            $sitemap->add(Url::create(route('categories.lists', $category->url))
                ->setLastModificationDate($lastMod)
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_WEEKLY)
                ->setPriority(0.8));
        });

        // Add dynamic URLs for Countries (newest first by ID)
        Country::orderBy('id', 'desc')->each(function (Country $country) use ($sitemap) {
            $lastMod = $country->created_at ?? Carbon::now(); // Assuming 'created_at' exists, otherwise Carbon::now()
            $sitemap->add(Url::create(route('venues.conferences', $country->url))
                ->setLastModificationDate($lastMod)
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_WEEKLY)
                ->setPriority(0.7));
        });

        // Add dynamic URLs for News (newest first by ID)
        News::orderBy('id', 'desc')->each(function (News $news) use ($sitemap) {
            $lastMod = $news->created_at ?? Carbon::now(); // Assuming 'created_at' exists, otherwise Carbon::now()
            $sitemap->add(Url::create(route('news.show', $news->id))
                ->setLastModificationDate($lastMod)
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_DAILY)
                ->setPriority(0.8));
        });

        $sitemap->writeToFile(public_path('sitemap.xml'));

        $this->info('Sitemap generated successfully!');
    }
}