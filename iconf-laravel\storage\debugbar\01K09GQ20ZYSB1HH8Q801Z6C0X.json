{"__meta": {"id": "01K09GQ20ZYSB1HH8Q801Z6C0X", "datetime": "2025-07-16 11:42:59", "utime": **********.616296, "method": "GET", "uri": "/categories/social_sciences_&amp;_humanities", "ip": "127.0.0.1"}, "messages": {"count": 6, "messages": [{"message": "[11:42:59] LOG.info: Finding category by URL {\n    \"url\": \"social_sciences_&amp;_humanities\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.539161, "xdebug_link": null, "collector": "log"}, {"message": "[11:42:59] LOG.info: Category found {\n    \"id\": 167,\n    \"name\": \"Social Sciences &amp; Humanities\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.541579, "xdebug_link": null, "collector": "log"}, {"message": "[11:42:59] LOG.info: Top category with children {\n    \"category_id\": 167,\n    \"child_ids\": [\n        168,\n        170,\n        171,\n        172,\n        173,\n        174,\n        175,\n        176,\n        177,\n        178,\n        179,\n        180,\n        181,\n        182,\n        183,\n        184\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.542504, "xdebug_link": null, "collector": "log"}, {"message": "[11:42:59] LOG.info: Building conference query {\n    \"category_ids\": [\n        168,\n        170,\n        171,\n        172,\n        173,\n        174,\n        175,\n        176,\n        177,\n        178,\n        179,\n        180,\n        181,\n        182,\n        183,\n        184\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.542576, "xdebug_link": null, "collector": "log"}, {"message": "[11:42:59] LOG.info: Conferences loaded successfully {\n    \"count\": 15,\n    \"total\": 647,\n    \"current_page\": 1,\n    \"last_page\": 44\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.563917, "xdebug_link": null, "collector": "log"}, {"message": "[11:42:59] LOG.info: Featured conferences loaded {\n    \"count\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.570587, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.361969, "end": **********.61631, "duration": 0.25434088706970215, "duration_str": "254ms", "measures": [{"label": "Booting", "start": **********.361969, "relative_start": 0, "end": **********.521177, "relative_end": **********.521177, "duration": 0.*****************, "duration_str": "159ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.521188, "relative_start": 0.*****************, "end": **********.616311, "relative_end": 1.1920928955078125e-06, "duration": 0.****************, "duration_str": "95.12ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.528483, "relative_start": 0.*****************, "end": **********.529714, "relative_end": **********.529714, "duration": 0.0012311935424804688, "duration_str": "1.23ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.573389, "relative_start": 0.*****************, "end": **********.615195, "relative_end": **********.615195, "duration": 0.*****************, "duration_str": "41.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: frontend.home.lists", "start": **********.574347, "relative_start": 0.*****************, "end": **********.574347, "relative_end": **********.574347, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.conference.creative-grid", "start": **********.575527, "relative_start": 0.*****************, "end": **********.575527, "relative_end": **********.575527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.conference.creative-card", "start": **********.575749, "relative_start": 0.21377992630004883, "end": **********.575749, "relative_end": **********.575749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.578569, "relative_start": 0.2165999412536621, "end": **********.578569, "relative_end": **********.578569, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.580087, "relative_start": 0.21811795234680176, "end": **********.580087, "relative_end": **********.580087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.582161, "relative_start": 0.22019195556640625, "end": **********.582161, "relative_end": **********.582161, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.583772, "relative_start": 0.2218029499053955, "end": **********.583772, "relative_end": **********.583772, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.585218, "relative_start": 0.22324895858764648, "end": **********.585218, "relative_end": **********.585218, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.586655, "relative_start": 0.2246859073638916, "end": **********.586655, "relative_end": **********.586655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.588407, "relative_start": 0.22643804550170898, "end": **********.588407, "relative_end": **********.588407, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.589891, "relative_start": 0.2279219627380371, "end": **********.589891, "relative_end": **********.589891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.591322, "relative_start": 0.2293529510498047, "end": **********.591322, "relative_end": **********.591322, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.592861, "relative_start": 0.23089194297790527, "end": **********.592861, "relative_end": **********.592861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.594634, "relative_start": 0.2326650619506836, "end": **********.594634, "relative_end": **********.594634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.596088, "relative_start": 0.23411893844604492, "end": **********.596088, "relative_end": **********.596088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.597667, "relative_start": 0.23569798469543457, "end": **********.597667, "relative_end": **********.597667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.5996, "relative_start": 0.23763108253479004, "end": **********.5996, "relative_end": **********.5996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.601138, "relative_start": 0.23916912078857422, "end": **********.601138, "relative_end": **********.601138, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: pagination::tailwind", "start": **********.601573, "relative_start": 0.23960399627685547, "end": **********.601573, "relative_end": **********.601573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.accordion", "start": **********.607118, "relative_start": 0.2451488971710205, "end": **********.607118, "relative_end": **********.607118, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.advertisement", "start": **********.609638, "relative_start": 0.24766898155212402, "end": **********.609638, "relative_end": **********.609638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.layouts.app", "start": **********.610972, "relative_start": 0.24900293350219727, "end": **********.610972, "relative_end": **********.610972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.navigation", "start": **********.611509, "relative_start": 0.24954009056091309, "end": **********.611509, "relative_end": **********.611509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.friendly-links", "start": **********.614034, "relative_start": 0.25206494331359863, "end": **********.614034, "relative_end": **********.614034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 45679400, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.4.2", "Environment": "local", "Debug Mode": "Enabled", "URL": "iconf.lv", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 24, "nb_templates": 24, "templates": [{"name": "frontend.home.lists", "param_count": null, "params": [], "start": **********.574337, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/frontend/home/<USER>", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Ffrontend%2Fhome%2Flists.blade.php&line=1", "ajax": false, "filename": "lists.blade.php", "line": "?"}}, {"name": "components.conference.creative-grid", "param_count": null, "params": [], "start": **********.57552, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/conference/creative-grid.blade.phpcomponents.conference.creative-grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fconference%2Fcreative-grid.blade.php&line=1", "ajax": false, "filename": "creative-grid.blade.php", "line": "?"}}, {"name": "components.conference.creative-card", "param_count": null, "params": [], "start": **********.575743, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/conference/creative-card.blade.phpcomponents.conference.creative-card", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fconference%2Fcreative-card.blade.php&line=1", "ajax": false, "filename": "creative-card.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.578561, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.58008, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.582151, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.583765, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.585211, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.586649, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.588399, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.589885, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.591315, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.592852, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.594628, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.596081, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.59766, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.599593, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.601131, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "pagination::tailwind", "param_count": null, "params": [], "start": **********.601565, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination/resources/views/tailwind.blade.phppagination::tailwind", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2Fresources%2Fviews%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}}, {"name": "components.category.accordion", "param_count": null, "params": [], "start": **********.607109, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/accordion.blade.phpcomponents.category.accordion", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Faccordion.blade.php&line=1", "ajax": false, "filename": "accordion.blade.php", "line": "?"}}, {"name": "components.advertisement", "param_count": null, "params": [], "start": **********.60961, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/advertisement.blade.phpcomponents.advertisement", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fadvertisement.blade.php&line=1", "ajax": false, "filename": "advertisement.blade.php", "line": "?"}}, {"name": "frontend.layouts.app", "param_count": null, "params": [], "start": **********.610964, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/frontend/layouts/app.blade.phpfrontend.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "components.navigation", "param_count": null, "params": [], "start": **********.611502, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/navigation.blade.phpcomponents.navigation", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fnavigation.blade.php&line=1", "ajax": false, "filename": "navigation.blade.php", "line": "?"}}, {"name": "components.friendly-links", "param_count": null, "params": [], "start": **********.614026, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/friendly-links.blade.phpcomponents.friendly-links", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Ffriendly-links.blade.php&line=1", "ajax": false, "filename": "friendly-links.blade.php", "line": "?"}}]}, "queries": {"count": 59, "nb_statements": 58, "nb_visible_statements": 59, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.027969999999999995, "accumulated_duration_str": "27.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.534302, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'n81JYPLEP0cENw7ZqI8BBaufrhZyV3CgHX5ObznX' limit 1", "type": "query", "params": [], "bindings": ["n81JYPLEP0cENw7ZqI8BBaufrhZyV3CgHX5ObznX"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.534813, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 0, "width_percent": 3.11}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.5403218, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 3.11, "width_percent": 0.787}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.541661, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 3.897, "width_percent": 0.429}, {"sql": "select exists(select * from `list` where `cid` in (168, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184)) as `exists`", "type": "query", "params": [], "bindings": [168, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 367}, {"index": 11, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 234}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 82}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.542794, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:367", "source": {"index": 10, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 367}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=367", "ajax": false, "filename": "HomeService.php", "line": "367"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 4.326, "width_percent": 1.716}, {"sql": "select count(*) as aggregate from `event` inner join `list` on `event`.`id` = `list`.`eid` where `status` = 1 and `list`.`cid` in (168, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184)", "type": "query", "params": [], "bindings": [1, 168, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 387}, {"index": 17, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 234}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 82}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.5447502, "duration": 0.0025499999999999997, "duration_str": "2.55ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:387", "source": {"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 387}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=387", "ajax": false, "filename": "HomeService.php", "line": "387"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 6.042, "width_percent": 9.117}, {"sql": "select distinct `event`.* from `event` inner join `list` on `event`.`id` = `list`.`eid` where `status` = 1 and `list`.`cid` in (168, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184) order by `event`.`end_date` desc limit 15 offset 0", "type": "query", "params": [], "bindings": [1, 168, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 387}, {"index": 17, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 234}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 82}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.547631, "duration": 0.01282, "duration_str": "12.82ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:387", "source": {"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 387}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=387", "ajax": false, "filename": "HomeService.php", "line": "387"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 15.159, "width_percent": 45.835}, {"sql": "select `id`, `venue` from `country` where `country`.`id` in (12, 14, 15, 25, 190)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 387}, {"index": 22, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 234}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 82}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.561523, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:387", "source": {"index": 21, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 387}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=387", "ajax": false, "filename": "HomeService.php", "line": "387"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 60.994, "width_percent": 0.572}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.562428, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 61.566, "width_percent": 0.644}, {"sql": "select `eid`, `cid` from `list` where `eid` in (5456, 5499, 5492, 5448, 5455, 5508, 5394, 5342, 5390, 5333, 5332, 5312, 5381, 5262, 5063)", "type": "query", "params": [], "bindings": [5456, 5499, 5492, 5448, 5455, 5508, 5394, 5342, 5390, 5333, 5332, 5312, 5381, 5262, 5063], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 493}, {"index": 14, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 390}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 234}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 82}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.563321, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:493", "source": {"index": 13, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 493}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=493", "ajax": false, "filename": "HomeService.php", "line": "493"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 62.21, "width_percent": 0.465}, {"sql": "select exists(select * from `event` inner join `list` on `event`.`id` = `list`.`eid` where `event`.`status` = 1 and `event`.`is_featured` = 1 and `event`.`end_date` >= ********** and `list`.`cid` in (168, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184)) as `exists`", "type": "query", "params": [], "bindings": [1, 1, **********, 168, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 438}, {"index": 11, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 237}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 82}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.564059, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:438", "source": {"index": 10, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 438}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=438", "ajax": false, "filename": "HomeService.php", "line": "438"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 62.674, "width_percent": 7.151}, {"sql": "select distinct `event`.* from `event` inner join `list` on `event`.`id` = `list`.`eid` where `status` = 1 and `is_featured` = 1 and `event`.`end_date` >= ********** and `list`.`cid` in (168, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184) order by `event`.`end_date` asc limit 5", "type": "query", "params": [], "bindings": [1, 1, **********, 168, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 455}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 237}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 82}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.566536, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:455", "source": {"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 455}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=455", "ajax": false, "filename": "HomeService.php", "line": "455"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 69.825, "width_percent": 6.829}, {"sql": "select `id`, `venue` from `country` where `country`.`id` in (12)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 455}, {"index": 21, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 237}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 82}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.5688262, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:455", "source": {"index": 20, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 455}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=455", "ajax": false, "filename": "HomeService.php", "line": "455"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 76.654, "width_percent": 0.322}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.5692232, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 76.975, "width_percent": 0.501}, {"sql": "select `eid`, `cid` from `list` where `eid` in (5499)", "type": "query", "params": [], "bindings": [5499], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 493}, {"index": 14, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 458}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 237}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 82}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.5700538, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:493", "source": {"index": 13, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 493}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=493", "ajax": false, "filename": "HomeService.php", "line": "493"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 77.476, "width_percent": 0.286}, {"sql": "select * from `ad_txt` where `cid` = 167 and `endtime` >= ********** order by `id` desc limit 5", "type": "query", "params": [], "bindings": [167, **********], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 525}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 240}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 82}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.571039, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:525", "source": {"index": 15, "namespace": null, "name": "app/Services/Frontend/HomeService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\Frontend\\HomeService.php", "line": 525}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FFrontend%2FHomeService.php&line=525", "ajax": false, "filename": "HomeService.php", "line": "525"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 77.762, "width_percent": 1.931}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_seo_setting_conferences_list')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_seo_setting_conferences_list"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.5720642, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 79.693, "width_percent": 0.393}, {"sql": "select * from `ad_txt` where `endtime` > ********** and `cid` = 167 order by `listorder` asc, `id` desc limit 5", "type": "query", "params": [], "bindings": [**********, 167], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/AdvertisementService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\AdvertisementService.php", "line": 25}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 85}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.572648, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "AdvertisementService.php:25", "source": {"index": 15, "namespace": null, "name": "app/Services/AdvertisementService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\AdvertisementService.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FAdvertisementService.php&line=25", "ajax": false, "filename": "AdvertisementService.php", "line": "25"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 80.086, "width_percent": 0.322}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.5765631, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 80.408, "width_percent": 1.108}, {"sql": "select `cid` from `list` where `eid` = 5456", "type": "query", "params": [], "bindings": [5456], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.577832, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 81.516, "width_percent": 0.358}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.5788722, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 81.873, "width_percent": 0.393}, {"sql": "select `cid` from `list` where `eid` = 5499", "type": "query", "params": [], "bindings": [5499], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.579609, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 82.267, "width_percent": 0.286}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.58041, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 82.553, "width_percent": 0.715}, {"sql": "select `cid` from `list` where `eid` = 5492", "type": "query", "params": [], "bindings": [5492], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.581286, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 83.268, "width_percent": 0.465}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.582478, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 83.733, "width_percent": 0.644}, {"sql": "select `cid` from `list` where `eid` = 5448", "type": "query", "params": [], "bindings": [5448], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.583297, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 84.376, "width_percent": 0.322}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.5840442, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 84.698, "width_percent": 0.358}, {"sql": "select `cid` from `list` where `eid` = 5455", "type": "query", "params": [], "bindings": [5455], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.584755, "duration": 7.000000000000001e-05, "duration_str": "70μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 85.055, "width_percent": 0.25}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.585478, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 85.306, "width_percent": 0.393}, {"sql": "select `cid` from `list` where `eid` = 5508", "type": "query", "params": [], "bindings": [5508], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.58621, "duration": 7.000000000000001e-05, "duration_str": "70μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 85.699, "width_percent": 0.25}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.586884, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 85.949, "width_percent": 0.322}, {"sql": "select `cid` from `list` where `eid` = 5394", "type": "query", "params": [], "bindings": [5394], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.5878031, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 86.271, "width_percent": 0.608}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.58869, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 86.879, "width_percent": 0.465}, {"sql": "select `cid` from `list` where `eid` = 5342", "type": "query", "params": [], "bindings": [5342], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.589434, "duration": 7.000000000000001e-05, "duration_str": "70μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 87.344, "width_percent": 0.25}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.590171, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 87.594, "width_percent": 0.322}, {"sql": "select `cid` from `list` where `eid` = 5390", "type": "query", "params": [], "bindings": [5390], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.590869, "duration": 7.000000000000001e-05, "duration_str": "70μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 87.916, "width_percent": 0.25}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.5915742, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 88.166, "width_percent": 0.322}, {"sql": "select `cid` from `list` where `eid` = 5333", "type": "query", "params": [], "bindings": [5333], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.5923, "duration": 7.000000000000001e-05, "duration_str": "70μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 88.488, "width_percent": 0.25}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.593237, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 88.738, "width_percent": 0.93}, {"sql": "select `cid` from `list` where `eid` = 5332", "type": "query", "params": [], "bindings": [5332], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.59415, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 89.668, "width_percent": 0.322}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.5949092, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 89.989, "width_percent": 0.393}, {"sql": "select `cid` from `list` where `eid` = 5312", "type": "query", "params": [], "bindings": [5312], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.595634, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 90.383, "width_percent": 0.286}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.596354, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 90.669, "width_percent": 0.608}, {"sql": "select `cid` from `list` where `eid` = 5381", "type": "query", "params": [], "bindings": [5381], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.5971692, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 91.276, "width_percent": 0.393}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.5979202, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 91.67, "width_percent": 0.393}, {"sql": "select `cid` from `list` where `eid` = 5262", "type": "query", "params": [], "bindings": [5262], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.598898, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 92.063, "width_percent": 0.858}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.5999131, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 92.921, "width_percent": 0.465}, {"sql": "select `cid` from `list` where `eid` = 5063", "type": "query", "params": [], "bindings": [5063], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.600663, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "ConferenceTags.php:105", "source": {"index": 13, "namespace": null, "name": "app/View/Components/Category/ConferenceTags.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\ConferenceTags.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FConferenceTags.php&line=105", "ajax": false, "filename": "ConferenceTags.php", "line": "105"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 93.386, "width_percent": 0.286}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.top')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.top"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.6023831, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 93.672, "width_percent": 0.358}, {"sql": "select * from `category` where `category`.`fid` = 115 and `category`.`fid` is not null order by `listorder` asc", "type": "query", "params": [], "bindings": [115], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/View/Components/Category/Accordion.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\Accordion.php", "line": 89}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.603399, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "Accordion.php:89", "source": {"index": 21, "namespace": null, "name": "app/View/Components/Category/Accordion.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\Accordion.php", "line": 89}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FAccordion.php&line=89", "ajax": false, "filename": "Accordion.php", "line": "89"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 94.029, "width_percent": 0.501}, {"sql": "select * from `category` where `category`.`fid` = 128 and `category`.`fid` is not null order by `listorder` asc", "type": "query", "params": [], "bindings": [128], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/View/Components/Category/Accordion.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\Accordion.php", "line": 89}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.604019, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Accordion.php:89", "source": {"index": 21, "namespace": null, "name": "app/View/Components/Category/Accordion.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\Accordion.php", "line": 89}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FAccordion.php&line=89", "ajax": false, "filename": "Accordion.php", "line": "89"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 94.53, "width_percent": 1.323}, {"sql": "select * from `category` where `category`.`fid` = 142 and `category`.`fid` is not null order by `listorder` asc", "type": "query", "params": [], "bindings": [142], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/View/Components/Category/Accordion.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\Accordion.php", "line": 89}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.60487, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Accordion.php:89", "source": {"index": 21, "namespace": null, "name": "app/View/Components/Category/Accordion.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\Accordion.php", "line": 89}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FAccordion.php&line=89", "ajax": false, "filename": "Accordion.php", "line": "89"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 95.853, "width_percent": 0.572}, {"sql": "select * from `category` where `category`.`fid` = 158 and `category`.`fid` is not null order by `listorder` asc", "type": "query", "params": [], "bindings": [158], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/View/Components/Category/Accordion.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\Accordion.php", "line": 89}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.605396, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "Accordion.php:89", "source": {"index": 21, "namespace": null, "name": "app/View/Components/Category/Accordion.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\Accordion.php", "line": 89}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FAccordion.php&line=89", "ajax": false, "filename": "Accordion.php", "line": "89"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 96.425, "width_percent": 0.358}, {"sql": "select * from `category` where `category`.`fid` = 167 and `category`.`fid` is not null order by `listorder` asc", "type": "query", "params": [], "bindings": [167], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/View/Components/Category/Accordion.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\Accordion.php", "line": 89}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 821}], "start": **********.605786, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "Accordion.php:89", "source": {"index": 21, "namespace": null, "name": "app/View/Components/Category/Accordion.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Category\\Accordion.php", "line": 89}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FCategory%2FAccordion.php&line=89", "ajax": false, "filename": "Accordion.php", "line": "89"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 96.782, "width_percent": 0.358}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_categories.tree')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_categories.tree"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 463}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.606226, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 97.14, "width_percent": 0.429}, {"sql": "select * from `ad_txt` where `endtime` > ********** and `cid` = 167 order by `listorder` asc, `id` desc limit 5", "type": "query", "params": [], "bindings": [**********, 167], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/AdvertisementService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\AdvertisementService.php", "line": 25}, {"index": 16, "namespace": null, "name": "app/View/Components/Advertisement.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\Advertisement.php", "line": 32}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1062}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 890}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1077}], "start": **********.608756, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "AdvertisementService.php:25", "source": {"index": 15, "namespace": null, "name": "app/Services/AdvertisementService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\AdvertisementService.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FServices%2FAdvertisementService.php&line=25", "ajax": false, "filename": "AdvertisementService.php", "line": "25"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 97.569, "width_percent": 0.501}, {"sql": "select * from `html_fragments` where `is_active` = 1 and `type` = 'js'", "type": "query", "params": [], "bindings": [1, "js"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/View/Composers/HtmlFragmentComposer.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Composers\\HtmlFragmentComposer.php", "line": 19}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 189}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160}], "start": **********.610348, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "HtmlFragmentComposer.php:19", "source": {"index": 15, "namespace": null, "name": "app/View/Composers/HtmlFragmentComposer.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Composers\\HtmlFragmentComposer.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComposers%2FHtmlFragmentComposer.php&line=19", "ajax": false, "filename": "HtmlFragmentComposer.php", "line": "19"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 98.069, "width_percent": 0.715}, {"sql": "select * from `links` where `status` = 1 order by `listorder` asc, `id` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/View/Components/FriendlyLinks.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\FriendlyLinks.php", "line": 23}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 110}, {"index": 17, "namespace": "view", "name": "frontend.layouts.app", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/frontend/layouts/app.blade.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.6133509, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "FriendlyLinks.php:23", "source": {"index": 15, "namespace": null, "name": "app/View/Components/FriendlyLinks.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\FriendlyLinks.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FFriendlyLinks.php&line=23", "ajax": false, "filename": "FriendlyLinks.php", "line": "23"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 98.784, "width_percent": 0.858}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_html_fragment_footer')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_html_fragment_footer"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "app/Services/HtmlFragmentService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\HtmlFragmentService.php", "line": 36}], "start": **********.614479, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 99.642, "width_percent": 0.358}]}, "models": {"data": {"App\\Models\\Category": {"value": 63, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Event": {"value": 16, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "App\\Models\\Link": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FLink.php&line=1", "ajax": false, "filename": "Link.php", "line": "?"}}, "App\\Models\\Country": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FCountry.php&line=1", "ajax": false, "filename": "Country.php", "line": "?"}}, "App\\Models\\HtmlFragment": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FHtmlFragment.php&line=1", "ajax": false, "filename": "HtmlFragment.php", "line": "?"}}}, "count": 94, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://iconf.lv/categories/social_sciences_&amp;_humanities", "action_name": "categories.lists", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@lists", "uri": "GET categories/{url}", "controller": "App\\Http\\Controllers\\Frontend\\HomeController@lists<a href=\"phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=78\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=78\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/HomeController.php:78-91</a>", "middleware": "web", "duration": "255ms", "peak_memory": "50MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-708101296 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-708101296\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-674602655 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-674602655\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-608221267 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"719 characters\">XSRF-TOKEN=eyJpdiI6Ijl6VzBLQ3I1a1pieVk4L1NrTmhQeHc9PSIsInZhbHVlIjoicVRNdThJT2tTZ0w2ZVp0bHdFcTdtTHlQSmdEYmh6TGJneXBQS1drTENaSlV0VEthTHlSbXJqSnFoUkVoU2tVZnhUYmN3ODlTeGZMRjlXRGRMQkdQUkRza2RhcGE5NDNaQUVKYkdDdVg1MDJJcWFiN3JhWlB6SzZ1NnRKVnlMcC8iLCJtYWMiOiI0ZmI4ODJjMjZiNTU5ZTBmYWVjZWJkM2IxNjY4ZmVkNDZmY2JiZjYxZjI3MzAwMTA2ODUyNzRjYTYzYWMxZDNjIiwidGFnIjoiIn0%3D; iconf_meeting_session=eyJpdiI6ImQ5L1BDRWlHMkUyVS9vZ1hMUFdwR0E9PSIsInZhbHVlIjoibThFSVRMMnlYcS9TVFRyM2x4YjhlWnRjS2xWMUhFUmpQNnM0TlVJM1BodUtWM3lWdThySjRyeCtaSWhxRmRoSFdOSllQVTBQd1JNaDZJNFdaUmRNZlVMNUVHK1BJbERTRDhMRzhES0ErREhtL1lIbTR6VC9tSk5JTnBoMmptQS8iLCJtYWMiOiIyZGZjZGUxMGQyZmQxOGJkM2RiYzYzZDhiZWJjNGJmMGQ5YWE3ZjRlM2E4ODhkZjFhMDQzODZiZDgxMGUwZjRkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://iconf.lv/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">iconf.lv</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-608221267\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1639260598 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KzVnHwsb1j7aFLoepnAgBRMhnc1xQgVO8Wwk7tZ4</span>\"\n  \"<span class=sf-dump-key>iconf_meeting_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">n81JYPLEP0cENw7ZqI8BBaufrhZyV3CgHX5ObznX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1639260598\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1778865385 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 16 Jul 2025 11:42:59 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1778865385\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-430363109 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KzVnHwsb1j7aFLoepnAgBRMhnc1xQgVO8Wwk7tZ4</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://iconf.lv/categories/e-learning</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$i.a5gP7/Z07pk3CeR6vNfe9lGm.BtthHlLi1.olj0JJDfdNlCRY8K</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-430363109\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://iconf.lv/categories/social_sciences_&amp;_humanities", "action_name": "categories.lists", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@lists"}, "badge": null}}