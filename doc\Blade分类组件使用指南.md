# Blade分类组件使用指南

## 1. 概述

为提高代码复用性和可维护性，我们将前台分类展示相关的功能封装为Blade组件。这些组件全部基于Laravel 12 class-based组件实现，符合Laravel 13的最佳实践，并充分利用PHP 8.4特性。所有组件均支持以下特性：

- **缓存优先**：优先从CategoryService的缓存中读取数据
- **容错降级**：当缓存异常时自动降级为数据库查询
- **统一排序**：所有层级的分类均按`listorder`字段排序
- **灵活布局**：支持网格布局和列表布局
- **支持CSS自定义**：可以为每个组件添加额外的CSS类

## 2. 组件列表

### 2.1 `<x-category.top-list />` - 顶级分类列表组件

展示所有一级分类的组件，支持网格和列表两种布局方式。

#### 使用方式：
```blade
<x-category.top-list 
    :grid="true" 
    :columns="2" 
    class="额外的CSS类" />
```

#### 参数说明：
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| grid | boolean | true | 布局方式，true为网格布局，false为列表布局 |
| columns | int | 2 | 网格布局时每行显示的分类数量 |
| class | string | '' | 额外的CSS类 |

### 2.2 `<x-category.sub-list />` - 子分类列表组件

展示某个一级分类的所有二级分类，支持网格和列表两种布局。

#### 使用方式：
```blade
<x-category.sub-list 
    :category="$category" 
    :grid="true" 
    :columns="2" 
    class="额外的CSS类" />
```

#### 参数说明：
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| category | Category/int | 必填 | 当前分类对象或分类ID |
| grid | boolean | true | 布局方式，true为网格布局，false为列表布局 |
| columns | int | 2 | 网格布局时每行显示的分类数量 |
| class | string | '' | 额外的CSS类 |

### 2.3 `<x-category.siblings />` - 兄弟分类列表组件

展示某个一级分类的其他一级分类（排除当前分类），支持网格和列表布局。

#### 使用方式：
```blade
<x-category.siblings 
    :category="$category" 
    :grid="true" 
    :columns="2" 
    title="其他分类" 
    class="额外的CSS类" />
```

#### 参数说明：
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| category | Category/int | 必填 | 当前分类对象或分类ID |
| grid | boolean | true | 布局方式，true为网格布局，false为列表布局 |
| columns | int | 2 | 网格布局时每行显示的分类数量 |
| title | string | '其他分类' | 标题文本 |
| class | string | '' | 额外的CSS类 |

### 2.4 `<x-category.conference-tags />` - 会议分类标签组件

以标签形式展示某个会议所属的分类。

#### 使用方式：
```blade
<x-category.conference-tags 
    :conference="$conference" 
    :showTitle="true" 
    title="所属分类" 
    class="额外的CSS类" />
```

#### 参数说明：
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| conference | Event/int | 必填 | 会议对象或会议ID |
| showTitle | boolean | true | 是否显示标题 |
| title | string | '所属分类' | 标题文本 |
| class | string | '' | 额外的CSS类 |

### 2.5 `<x-category.accordion />` - 手风琴分类侧边栏组件

以手风琴效果展示分类层次结构，一级分类作为手风琴标题，二级分类作为可展开内容。支持根据会议ID或栏目URL自动展开对应分类，并高亮显示当前访问的分类。

#### 使用方式：
```blade
<!-- 基于栏目URL激活 -->
<x-category.accordion 
    :active="$category->url" 
    activeType="url" 
    title="分类导航" 
    class="额外的CSS类" />
    
<!-- 基于会议ID激活 -->
<x-category.accordion 
    :active="$conference->id" 
    activeType="event" 
    title="相关分类" 
    class="mb-6" />
    
<!-- 不指定激活项 -->
<x-category.accordion title="浏览分类" />
```

#### 参数说明：
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|----- |
| active | string/int | null | 当前激活的会议ID或栏目URL |
| activeType | string | 'url' | 激活类型，'event'表示会议ID，'url'表示栏目URL |
| showTitle | boolean | true | 是否显示标题 |
| title | string | '会议分类' | 标题文本 |
| class | string | '' | 额外的CSS类 |

## 3. 技术实现

### 3.1 核心依赖

所有组件均依赖`CategoryService`服务，该服务负责分类数据的缓存和提供。组件的数据流向为：

```
CategoryService缓存 → 组件类 → Blade视图
```

当缓存出现异常时，会自动降级为：

```
数据库查询 → 组件类 → Blade视图
```

### 3.2 组件文件结构

组件遵循Laravel标准组件结构：

- 组件类文件位于：`app/View/Components/Category/`目录
- 组件视图文件位于：`resources/views/components/category/`目录

### 3.3 排序机制

所有分类列表均严格按照`listorder`字段升序排列，排序发生在以下几个层次：

1. `CategoryService`缓存层：缓存数据结构中已包含按`listorder`排序的数据
2. 组件类处理层：当从缓存获取数据后再次确保排序
3. 数据库降级层：当回退到数据库查询时，通过`orderBy('listorder')`确保排序

## 4. 使用示例

### 4.1 在首页右侧边栏显示所有一级分类

```blade
<!-- 会议分类 -->
<div class="bg-white rounded-lg shadow-md p-6 mb-8">
    <h2 class="text-xl font-bold text-gray-800 mb-4">会议分类</h2>
    <x-category.top-list :grid="true" :columns="2" />
</div>
```

### 4.2 在分类列表页显示子分类和兄弟分类

```blade
<!-- 子分类 -->
@if($category->fid == 0 && isset($category->children) && $category->children->count() > 0)
<x-category.sub-list :category="$category" :grid="true" :columns="2" class="mb-8" />
@endif

<!-- 其他分类 -->
<x-category.siblings :category="$category" :grid="true" :columns="2" title="其他分类" />
```

### 4.3 在会议详情页显示分类标签

```blade
<x-category.conference-tags :conference="$conference" :showTitle="false" class="mt-4" />
```

### 4.4 在分类页右侧边栏显示手风琴分类导航

```blade
<!-- 分类手风琴导航 -->
<div class="mb-8">
    <x-category.accordion 
        :active="$category->url" 
        activeType="url" 
        title="分类导航" />
</div>
```

### 4.5 在会议详情页显示相关分类手风琴

```blade
<!-- 相关分类 -->
<div class="mb-6">
    <x-category.accordion 
        :active="$conference->id" 
        activeType="event" 
        title="相关分类" />
</div>
```

## 5. 注意事项

1. **组件传参**：
   - 确保传入组件的对象已预加载相关关系，可提高性能
   - 可以传入对象或ID，组件内部会自动处理

2. **性能考虑**：
   - 所有组件均采用缓存优先策略，避免频繁数据库查询
   - 当同一页面多次使用相同组件时，建议在控制器中提前加载必要数据

3. **排序优先级**：
   - 所有分类均按`listorder`排序，后台拖拽排序更新后会自动反映在前台
   - 后台修改排序后需要刷新缓存才能生效

4. **容错机制**：
   - 当缓存出现问题时，组件会自动降级使用数据库查询
   - 日志记录在Laravel日志中，方便排查问题

5. **CSS类**：
   - 所有组件均支持自定义CSS类，便于在不同场景下应用不同样式

## 6. 扩展与维护

如需增加新的分类相关组件或扩展现有组件功能，请遵循以下原则：

1. 保持组件命名一致性：`app/View/Components/Category/新组件.php`
2. 确保组件支持缓存优先和容错降级
3. 所有分类列表需保持按`listorder`排序
4. 为新组件编写详细的头部注释和使用说明
5. 更新本文档，添加新组件的使用方式和参数说明

## 7. 后续计划

- [ ] 编写单元测试，覆盖组件的各种使用场景和边界情况
- [ ] 优化组件的性能，减少不必要的查询和数据处理
- [ ] 丰富组件的布局选项和展示形式
- [ ] 考虑增加更多实用的分类相关组件

## 8. 贡献者

- 开发团队
- 最后更新：2025-06-28
