# iConf项目架构图表文档

## 1. 项目整体架构图

```mermaid
graph TB
    subgraph "前端展示层"
        A[用户访问] --> B[Bootstrap + jQuery界面]
        B --> C[响应式设计]
    end
    
    subgraph "应用层 - ThinkPHP 3.2"
        D[Home模块 - 前台] --> E[IndexController]
        D --> F[MemberController]
        D --> G[CheckloginController]
        
        H[AdminICF模块 - 后台] --> I[IndexController]
        H --> J[LoginController]
        H --> K[CheckAdminController]
        
        L[Common模块 - 公共] --> M[function.php]
        L --> N[config.php]
        L --> O[seo_config.php]
    end
    
    subgraph "业务逻辑层"
        P[EventModel] --> Q[会议管理]
        R[MemberModel] --> S[用户管理]
        T[CategoryModel] --> U[分类管理]
        V[NewsModel] --> W[新闻管理]
    end
    
    subgraph "数据存储层"
        X[(MySQL数据库)] --> Y[event - 会议表]
        X --> Z[member - 用户表]
        X --> AA[category - 分类表]
        X --> BB[news - 新闻表]
        X --> CC[ad_txt - 广告表]
        X --> DD[country - 地区表]
        X --> EE[list - 关联表]
        
        FF[文件存储] --> GG[Uploads目录]
        GG --> HH[按日期分目录]
        HH --> II[图片文件]
        HH --> JJ[文档文件]
    end
    
    subgraph "核心功能模块"
        KK[会议发布] --> LL[用户提交]
        LL --> MM[管理员审核]
        MM --> NN[前台展示]
        
        OO[分类管理] --> PP[二级分类]
        PP --> QQ[多分类归属]
        
        RR[用户系统] --> SS[注册登录]
        SS --> TT[权限控制]
        TT --> UU[VIP机制]
        
        VV[内容管理] --> WW[新闻发布]
        VV --> XX[广告管理]
        VV --> YY[视频管理]
    end
    
    A --> D
    A --> H
    E --> P
    F --> R
    I --> P
    I --> T
    I --> V
    
    Q --> Y
    S --> Z
    U --> AA
    W --> BB
    
    style A fill:#e1f5fe
    style X fill:#f3e5f5
    style D fill:#e8f5e8
    style H fill:#fff3e0
```

### 架构说明

#### 前端展示层
- **用户界面**: 基于Bootstrap 3框架的响应式设计
- **交互技术**: jQuery实现动态交互效果
- **兼容性**: 支持桌面端和移动端访问

#### 应用层
- **Home模块**: 前台用户功能，包括会议浏览、用户注册登录等
- **AdminICF模块**: 后台管理功能，包括会议审核、内容管理等
- **Common模块**: 公共函数库、配置文件、SEO设置等

#### 业务逻辑层
- **模型层**: 使用ThinkPHP的Model类处理数据验证和业务逻辑
- **控制器层**: 处理用户请求和业务流程控制

#### 数据存储层
- **MySQL数据库**: 存储所有业务数据
- **文件系统**: 按日期组织的文件上传存储

## 2. 数据库关系图

```mermaid
erDiagram
    event {
        int id PK
        varchar cid "分类ID(多个)"
        int uid FK
        int venue FK
        varchar city
        varchar hotel
        varchar title
        varchar event
        varchar url
        int start_date
        int end_date
        int sub_date
        varchar email
        varchar web
        varchar tel
        text content
        varchar pic
        tinyint ding "置顶"
        tinyint push "推荐"
        tinyint status "状态"
        int addtime
        varchar summary
        tinyint is_featured "特色推荐"
    }
    
    category {
        int id PK
        int listorder
        int fid "父分类ID"
        varchar name
        varchar url
    }
    
    list {
        int id PK
        int cid FK
        int eid FK
    }
    
    member {
        int id PK
        varchar email
        varchar username
        smallint area
        varchar password
        varchar ip
        varchar regtime
        tinyint vip
        tinyint status
    }
    
    country {
        int id PK
        int listorder
        varchar venue
        varchar url
        int fid "父级ID"
    }
    
    news {
        int id PK
        varchar title
        text content
        timestamp publish_time
        int column_id FK
        varchar cover
        text summary
        int click_rate
        tinyint is_featured
    }
    
    news_type {
        int id PK
        varchar column_name
        int column_sort
    }
    
    ad_txt {
        int id PK
        int listorder
        varchar title
        int cid FK
        varchar link
        varchar pic
        text content
        int endtime
    }
    
    admin {
        int id PK
        varchar username
        varchar password
    }
    
    page {
        int id PK
        varchar title
        text content
    }
    
    vod {
        int id PK
        varchar title
        varchar file_path
        varchar external_link
        text video_code
        varchar cover_image
        int views
        tinyint is_recommended
        varchar vod_type
        timestamp publish_time
    }
    
    %% 关系定义
    event ||--o{ list : "会议分类关联"
    category ||--o{ list : "分类会议关联"
    category ||--o{ category : "父子分类"
    member ||--o{ event : "用户发布会议"
    country ||--o{ event : "会议地点"
    country ||--o{ country : "父子地区"
    news_type ||--o{ news : "新闻分类"
    category ||--o{ ad_txt : "广告分类"
```

### 数据库关系说明

#### 核心关系
1. **会议与分类**: 多对多关系，通过list表关联
2. **分类层级**: 支持二级分类结构，通过fid字段实现
3. **用户与会议**: 一对多关系，用户可以发布多个会议
4. **地区层级**: 支持国家-城市的二级结构

#### 特殊字段说明
- **event.cid**: 存储JSON格式的分类ID数组
- **event.status**: 0=待审，1=通过，2=隐藏，3=拒绝
- **member.vip**: VIP用户会议直接通过审核
- **时间字段**: 使用Unix时间戳存储

## 3. 业务流程图

```mermaid
flowchart TD
    A[用户访问网站] --> B{是否已登录?}
    B -->|否| C[注册/登录]
    B -->|是| D[浏览会议信息]
    
    C --> E[填写注册信息]
    E --> F[邮箱验证]
    F --> G[登录成功]
    G --> D
    
    D --> H[选择会议分类]
    H --> I[查看会议列表]
    I --> J[查看会议详情]
    
    D --> K{用户类型?}
    K -->|普通用户| L[提交会议信息]
    K -->|VIP用户| M[提交会议信息]
    
    L --> N[等待管理员审核]
    N --> O{审核结果?}
    O -->|通过| P[会议发布到前台]
    O -->|拒绝| Q[通知用户修改]
    Q --> L
    
    M --> R[VIP会议直接发布]
    R --> P
    
    P --> S[前台展示]
    S --> T[用户可以浏览]
    
    subgraph "管理员后台"
        U[管理员登录] --> V[会议管理]
        V --> W[审核待审会议]
        W --> X{审核决定?}
        X -->|通过| Y[设置会议状态为通过]
        X -->|拒绝| Z[设置会议状态为拒绝]
        
        V --> AA[分类管理]
        AA --> BB[添加/编辑分类]
        
        V --> CC[内容管理]
        CC --> DD[新闻管理]
        CC --> EE[广告管理]
        CC --> FF[视频管理]
        
        V --> GG[用户管理]
        GG --> HH[查看用户信息]
        GG --> II[设置VIP权限]
    end
    
    subgraph "推荐机制"
        JJ[会议发布] --> KK{设置推荐?}
        KK -->|置顶| LL[首页置顶显示]
        KK -->|推荐| MM[推荐区域显示]
        KK -->|特色推荐| NN[特色推荐区显示]
        KK -->|普通| OO[正常列表显示]
    end
    
    subgraph "搜索功能"
        PP[用户搜索] --> QQ[输入关键词]
        QQ --> RR[搜索会议标题]
        QQ --> SS[搜索会议简称]
        RR --> TT[显示搜索结果]
        SS --> TT
    end
    
    Y --> P
    Z --> Q
    
    style A fill:#e1f5fe
    style U fill:#fff3e0
    style JJ fill:#e8f5e8
    style PP fill:#f3e5f5
```

### 业务流程说明

#### 用户端流程
1. **访问与认证**: 用户可以匿名浏览，注册后可提交会议
2. **会议浏览**: 支持分类浏览、搜索、详情查看
3. **会议提交**: 普通用户需要审核，VIP用户直接发布

#### 管理端流程
1. **会议审核**: 管理员可以通过、拒绝或隐藏会议
2. **内容管理**: 管理新闻、广告、视频等内容
3. **用户管理**: 查看用户信息，设置VIP权限

#### 推荐机制
- **置顶**: 在首页顶部显示
- **推荐**: 在推荐区域显示
- **特色推荐**: 带摘要的特殊推荐区域

## 4. 文件结构图

```mermaid
graph TD
    A[iconf.org] --> B[Application]
    A --> C[Public]
    A --> D[ThinkPHP]
    A --> E[Uploads]
    A --> F[index.php]
    
    B --> G[Home - 前台模块]
    B --> H[AdminICF - 后台模块]
    B --> I[Common - 公共模块]
    B --> J[Runtime - 运行时]
    
    G --> K[Controller]
    G --> L[Model]
    G --> M[View]
    
    H --> N[Controller]
    H --> O[Model]
    H --> P[View]
    
    I --> Q[Common/function.php]
    I --> R[Conf/config.php]
    I --> S[Conf/seo_config.php]
    
    C --> T[a_style - CSS]
    C --> U[js - JavaScript]
    C --> V[images - 图片]
    C --> W[editor - 编辑器]
    
    E --> X[2025-01-03]
    E --> Y[2025-01-04]
    E --> Z[...]
    
    style A fill:#e1f5fe
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style E fill:#f3e5f5
```

## 5. 如何使用这些图表

### 在Markdown中显示
这些图表使用Mermaid语法编写，可以在支持Mermaid的Markdown编辑器中直接显示，如：
- GitHub
- GitLab
- Typora
- VS Code (with Mermaid extension)
- Notion

### 导出为图片
1. **在线工具**: 使用 [Mermaid Live Editor](https://mermaid.live/) 
2. **VS Code**: 安装Mermaid扩展，右键导出
3. **命令行**: 使用mermaid-cli工具
4. **Typora**: 直接右键图表选择导出

### 嵌入到文档
可以将这些图表代码直接复制到任何Markdown文档中，在支持Mermaid的环境中会自动渲染。

---

**文档版本**: v1.0  
**创建日期**: 2025-06-24  
**维护人员**: 开发团队
