<?php

namespace App\Services;

use App\Models\Event;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;

/**
 * 会议卡片服务类
 * 
 * 负责处理会议卡片相关的业务逻辑，包括：
 * - 获取推荐会议
 * - 获取即将召开的会议
 * - 格式化会议数据
 * - 缓存管理
 */
class ConferenceCardService
{
    /**
     * 获取推荐会议列表
     *
     * @param int $limit 限制数量
     * @return Collection
     */
    public function getFeaturedConferences(int $limit = 8): Collection
    {
        $cacheKey = "featured_conferences_{$limit}";
        
        return Cache::remember($cacheKey, 60 * 30, function () use ($limit) { // 缓存30分钟
            return Event::where('status', 1) // 已发布
                ->where('push', true) // 推荐会议（使用现有的push字段）
                ->with(['country', 'categories'])
                ->orderBy('addtime', 'desc')
                ->limit($limit)
                ->get()
                ->map(function ($conference) {
                    return $this->formatConferenceData($conference);
                });
        });
    }

    /**
     * 获取即将召开的会议列表
     *
     * @param int $limit 限制数量
     * @return Collection
     */
    public function getUpcomingConferences(int $limit = 8): Collection
    {
        $cacheKey = "upcoming_conferences_{$limit}";
        
        return Cache::remember($cacheKey, 60 * 30, function () use ($limit) { // 缓存30分钟
            $currentTime = time();
            
            return Event::where('status', 1) // 已发布
                ->where('start_date', '>', $currentTime) // 还未开始
                ->with(['country', 'categories'])
                ->orderBy('start_date', 'asc') // 按开始时间排序
                ->limit($limit)
                ->get()
                ->map(function ($conference) {
                    return $this->formatConferenceData($conference);
                });
        });
    }

    /**
     * 获取相关会议（基于分类）
     *
     * @param Event $currentConference 当前会议
     * @param int $limit 限制数量
     * @return Collection
     */
    public function getRelatedConferences(Event $currentConference, int $limit = 4): Collection
    {
        $cacheKey = "related_conferences_{$currentConference->id}_{$limit}";
        
        return Cache::remember($cacheKey, 60 * 60, function () use ($currentConference, $limit) { // 缓存1小时
            // 获取当前会议的分类ID
            $categoryIds = $currentConference->categoryIds;
            
            if (empty($categoryIds)) {
                // 如果没有分类，返回最新的会议
                return $this->getLatestConferences($limit, [$currentConference->id]);
            }

            // 查找相同分类的会议
            $relatedConferences = Event::where('status', 1)
                ->where('id', '!=', $currentConference->id)
                ->where(function ($query) use ($categoryIds) {
                    foreach ($categoryIds as $categoryId) {
                        $query->orWhere('cid', 'like', "%{$categoryId}%");
                    }
                })
                ->with(['country', 'categories'])
                ->orderBy('addtime', 'desc')
                ->limit($limit)
                ->get();

            // 如果相关会议不够，补充最新会议
            if ($relatedConferences->count() < $limit) {
                $excludeIds = $relatedConferences->pluck('id')->push($currentConference->id)->toArray();
                $additionalConferences = $this->getLatestConferences(
                    $limit - $relatedConferences->count(),
                    $excludeIds
                );
                $relatedConferences = $relatedConferences->merge($additionalConferences);
            }

            return $relatedConferences->map(function ($conference) {
                return $this->formatConferenceData($conference);
            });
        });
    }

    /**
     * 获取最新会议
     *
     * @param int $limit 限制数量
     * @param array $excludeIds 排除的会议ID
     * @return Collection
     */
    public function getLatestConferences(int $limit = 8, array $excludeIds = []): Collection
    {
        $query = Event::where('status', 1)
            ->with(['country', 'categories'])
            ->orderBy('addtime', 'desc');

        if (!empty($excludeIds)) {
            $query->whereNotIn('id', $excludeIds);
        }

        return $query->limit($limit)->get();
    }

    /**
     * 格式化会议数据
     *
     * @param Event $conference 会议模型
     * @return Event 格式化后的会议数据
     */
    private function formatConferenceData(Event $conference): Event
    {
        // 确保会议简称不为空
        if (empty($conference->event)) {
            $conference->event = $this->generateAcronym($conference->title);
        }

        // 格式化网址（确保有协议）
        if ($conference->web && !str_starts_with($conference->web, 'http')) {
            $conference->web = 'https://' . $conference->web;
        }

        // 添加格式化的日期范围
        $conference->formatted_date_range = $this->formatDateRange(
            $conference->start_date,
            $conference->end_date
        );

        // 添加状态信息
        $conference->is_upcoming = $conference->start_date > time();
        $conference->is_ongoing = $conference->start_date <= time() && $conference->end_date >= time();
        $conference->is_past = $conference->end_date < time();

        return $conference;
    }

    /**
     * 生成会议简称
     *
     * @param string $title 会议标题
     * @return string 会议简称
     */
    private function generateAcronym(string $title): string
    {
        // 提取大写字母
        preg_match_all('/[A-Z]/', $title, $matches);
        if (count($matches[0]) >= 2) {
            return implode('', array_slice($matches[0], 0, 6)); // 最多6个字母
        }

        // 提取单词首字母
        $words = explode(' ', $title);
        $acronym = '';
        foreach ($words as $word) {
            if (strlen($word) > 2 && !in_array(strtolower($word), ['of', 'on', 'and', 'the', 'for', 'in', 'to', 'with'])) {
                $acronym .= strtoupper(substr($word, 0, 1));
                if (strlen($acronym) >= 6) break;
            }
        }

        // 如果还是太短，使用标题前几个字符
        if (strlen($acronym) < 2) {
            $acronym = strtoupper(substr(preg_replace('/[^a-zA-Z]/', '', $title), 0, 4));
        }

        return $acronym ?: 'CONF';
    }

    /**
     * 格式化日期范围
     *
     * @param int|null $startDate 开始时间戳
     * @param int|null $endDate 结束时间戳
     * @return string 格式化的日期范围
     */
    private function formatDateRange(?int $startDate, ?int $endDate): string
    {
        if (!$startDate || !$endDate) {
            return 'TBD';
        }

        $start = date('M j', $startDate);
        $end = date('j, Y', $endDate);
        
        // 如果是同一天
        if (date('Y-m-d', $startDate) === date('Y-m-d', $endDate)) {
            return date('M j, Y', $startDate);
        }
        
        // 如果是同一个月
        if (date('Y-m', $startDate) === date('Y-m', $endDate)) {
            return date('M j', $startDate) . '-' . date('j, Y', $endDate);
        }
        
        return $start . '-' . $end;
    }

    /**
     * 清除相关缓存
     *
     * @param Event|null $conference 特定会议（可选）
     * @return void
     */
    public function clearCache(?Event $conference = null): void
    {
        // 清除通用缓存
        Cache::forget('featured_conferences_8');
        Cache::forget('upcoming_conferences_8');
        
        // 如果指定了会议，清除相关缓存
        if ($conference) {
            Cache::forget("related_conferences_{$conference->id}_4");
        }
        
        // 清除所有相关的缓存键（可以根据需要扩展）
        $cacheKeys = [
            'featured_conferences_4',
            'featured_conferences_12',
            'upcoming_conferences_4',
            'upcoming_conferences_12',
        ];
        
        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }
    }

    /**
     * 获取会议统计信息
     *
     * @return array 统计数据
     */
    public function getStatistics(): array
    {
        $cacheKey = 'conference_statistics';
        
        return Cache::remember($cacheKey, 60 * 60 * 24, function () { // 缓存24小时
            $currentTime = time();
            
            return [
                'total' => Event::where('status', 1)->count(),
                'featured' => Event::where('status', 1)->where('push', true)->count(), // 使用push字段
                'upcoming' => Event::where('status', 1)->where('start_date', '>', $currentTime)->count(),
                'ongoing' => Event::where('status', 1)
                    ->where('start_date', '<=', $currentTime)
                    ->where('end_date', '>=', $currentTime)
                    ->count(),
                'this_month' => Event::where('status', 1)
                    ->where('start_date', '>=', strtotime('first day of this month'))
                    ->where('start_date', '<=', strtotime('last day of this month'))
                    ->count(),
            ];
        });
    }
}
