<?php

namespace App\Filament\Concerns;

use App\Helpers\LegacyDataHelper;

/**
 * 处理Event数据的共用trait
 * 可以被admin和member面板的EventResource页面使用
 */
/**
 * @property \App\Models\Event $record
 */
trait HandlesEventData
{
    /**
     * 将Carbon对象转换为Unix时间戳
     */
    protected function convertDateTimesToTimestamps(array $data): array
    {
        $dateFields = ['start_date', 'end_date', 'sub_date'];

        foreach ($dateFields as $field) {
            if (isset($data[$field]) && $data[$field]) {
                $data[$field] = is_string($data[$field])
                    ? strtotime($data[$field])
                    : $data[$field]->timestamp;
            }
        }

        return $data;
    }

    /**
     * 同步cid字段与categories关联表
     */
    protected function syncCategoriesWithCidField(): void
    {
        $record = $this->record;

        // 刷新模型以获取最新的关联数据
        $record->refresh();
        $record->load('categories');

        // 从关联关系获取当前选中的分类ID
        $currentCategoryIds = $record->categories->pluck('id')->toArray();

        // 处理分类关联：同步cid字段与categories关联表
        if (!empty($currentCategoryIds)) {
            // 生成新的cid字段
            $newCid = LegacyDataHelper::categoriesToCid($currentCategoryIds);

            // 只有当cid字段需要更新时才更新
            if ($record->cid !== $newCid) {
                $record->cid = $newCid;
                $record->save();
            }
        } else {
            // 如果没有选择任何分类，清空cid字段
            if (!empty($record->cid)) {
                $record->cid = '';
                $record->save();
            }
        }
    }

    /**
     * 填充表单时同步categories数据
     * 确保从cid字段解析的分类ID与categories关联表保持一致
     */
    protected function syncCategoriesFromCidField(array $data): array
    {
        // 处理分类数据：从cid字段解析分类ID，确保表单正确显示选中状态
        // 这是为了兼容老数据，老数据可能只有cid字段而没有categories关联表数据
        if (!empty($data['cid'])) {
            $categoryIds = LegacyDataHelper::cidToCategories($data['cid']);
            if (!empty($categoryIds)) {
                // 过滤出有效的分类ID（确保在category表中存在）
                $validCategoryIds = \App\Models\Category::whereIn('id', $categoryIds)->pluck('id')->toArray();

                if (!empty($validCategoryIds)) {
                    $data['categories'] = $validCategoryIds;

                    // 同时确保categories关联表数据同步（用于后续操作）
                    $this->record->categories()->sync($validCategoryIds);

                    // 如果有无效的分类ID，更新cid字段以保持数据一致性
                    if (count($validCategoryIds) !== count($categoryIds)) {
                        $newCid = LegacyDataHelper::categoriesToCid($validCategoryIds);
                        $this->record->cid = $newCid;
                        $this->record->save();
                    }
                }
            }
        }

        return $data;
    }

    /**
     * 获取分类选项（用于表单）- 分组显示
     */
    protected function getCategoryOptions(): array
    {
        $categories = \App\Models\Category::all();
        $options = [];

        // 获取一级分类作为分组
        $parentCategories = $categories->where('fid', 0)->sortBy('listorder');

        foreach ($parentCategories as $parent) {
            // 获取该一级分类下的二级分类
            $children = $categories->where('fid', $parent->id)->sortBy('listorder');

            if ($children->isNotEmpty()) {
                // 如果有子分类，创建分组
                $groupOptions = [];
                foreach ($children as $child) {
                    $groupOptions[$child->id] = $child->name . ' (ID: ' . $child->id . ')';
                }
                $options[$parent->name] = $groupOptions;
            }
        }

        return $options;
    }

    /**
     * 获取可选择的分类ID列表（只包含二级分类）
     */
    protected function getSelectableCategoryIds(): array
    {
        return \App\Models\Category::where('fid', '>', 0)->pluck('id')->toArray();
    }
}
