<?php

namespace App\Console\Commands;

use App\Models\News;
use Illuminate\Console\Command;

class TestSpecificNews extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:specific-news {id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试特定新闻的HTML解码';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $id = $this->argument('id');
        $news = News::find($id);

        if (!$news) {
            $this->error("新闻ID {$id} 不存在");
            return;
        }

        $this->info("📰 新闻详情 (ID: {$news->id})");
        $this->info("📝 标题: {$news->title}");
        $this->info("📂 分类: " . ($news->newsType?->column_name ?? '无分类'));
        $this->info("⭐ 推荐: " . ($news->is_featured ? '是' : '否'));
        $this->info("👁️  点击率: {$news->click_rate}");
        $this->info("📅 发布时间: {$news->publish_time->format('Y-m-d H:i:s')}");
        $this->newLine();

        $this->line("🔤 原始内容 (前500字符):");
        $this->line(mb_substr($news->content, 0, 500) . '...');
        $this->newLine();

        $this->line("✨ 解码后内容 (前500字符):");
        $this->line(mb_substr($news->getDecodedContent(), 0, 500) . '...');
        $this->newLine();

        $this->line("📝 摘要:");
        $this->line($news->excerpt);
    }
}
