<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Services\ConferenceCardService;
use Illuminate\Http\Request;
use Illuminate\View\View;

/**
 * 会议卡片控制器
 * 
 * 负责展示会议卡片相关页面
 */
class ConferenceCardController extends Controller
{
    protected ConferenceCardService $conferenceCardService;

    /**
     * 构造函数
     *
     * @param ConferenceCardService $conferenceCardService
     */
    public function __construct(ConferenceCardService $conferenceCardService)
    {
        $this->conferenceCardService = $conferenceCardService;
    }

    /**
     * 展示会议卡片演示页面
     *
     * @return View
     */
    public function demo(): View
    {
        // 获取推荐会议
        $featuredConferences = $this->conferenceCardService->getFeaturedConferences(4);
        
        // 获取即将召开的会议
        $upcomingConferences = $this->conferenceCardService->getUpcomingConferences(4);
        
        // 获取统计信息
        $statistics = $this->conferenceCardService->getStatistics();

        return view('frontend.conference.cards-demo', compact(
            'featuredConferences',
            'upcomingConferences', 
            'statistics'
        ));
    }

    /**
     * AJAX获取更多推荐会议
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function loadMoreFeatured(Request $request)
    {
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 4);
        $offset = ($page - 1) * $limit;

        $conferences = $this->conferenceCardService->getFeaturedConferences($limit + $offset)
            ->slice($offset, $limit);

        return response()->json([
            'success' => true,
            'data' => $conferences->values(),
            'has_more' => $conferences->count() === $limit
        ]);
    }

    /**
     * AJAX获取更多即将召开的会议
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function loadMoreUpcoming(Request $request)
    {
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 4);
        $offset = ($page - 1) * $limit;

        $conferences = $this->conferenceCardService->getUpcomingConferences($limit + $offset)
            ->slice($offset, $limit);

        return response()->json([
            'success' => true,
            'data' => $conferences->values(),
            'has_more' => $conferences->count() === $limit
        ]);
    }

    /**
     * 获取会议统计信息API
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function statistics()
    {
        $statistics = $this->conferenceCardService->getStatistics();

        return response()->json([
            'success' => true,
            'data' => $statistics
        ]);
    }

    /**
     * 清除缓存
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function clearCache()
    {
        $this->conferenceCardService->clearCache();

        return response()->json([
            'success' => true,
            'message' => 'Cache cleared successfully'
        ]);
    }
}
