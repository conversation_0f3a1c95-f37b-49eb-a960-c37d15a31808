<?php

namespace App\Filament\Member\Widgets;

use App\Models\Event;
use App\Enums\ConferenceStatus;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;

class MemberStatsWidget extends BaseWidget
{
    protected static ?string $pollingInterval = '15s';

    protected function getStats(): array
    {
        $member = Auth::guard('member')->user();

        if (!$member) {
            return [];
        }

        // Get member's event statistics
        $totalEvents = $member->events()->count();
        $publishedEvents = $member->events()->published()->count();
        $pendingEvents = $member->events()->where('status', ConferenceStatus::Pending)->count();

        // Number of conferences published this month
        $thisMonthEvents = $member->events()
            ->whereYear('addtime', now()->year)
            ->whereMonth('addtime', now()->month)
            ->count();

        // Number of upcoming events (next 30 days)
        $upcomingEvents = $member->events()
            ->where('status', ConferenceStatus::Published)
            ->where('start_date', '>=', now()->format('Y-m-d'))
            ->where('start_date', '<=', now()->addDays(30)->format('Y-m-d'))
            ->count();

        return [
            Stat::make('My Events', $totalEvents)
                ->description('Total published events')
                ->descriptionIcon('heroicon-m-calendar-days')
                ->color('primary')
                ->chart($this->getEventChart())
                ->chartColor('primary'),

            Stat::make('Published', $publishedEvents)
                ->description('Events that have passed review')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),

            Stat::make('Pending Review', $pendingEvents)
                ->description('Events awaiting review')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning'),

            Stat::make('Published This Month', $thisMonthEvents)
                ->description('New events this month')
                ->descriptionIcon('heroicon-m-calendar')
                ->color('info'),

            Stat::make('Upcoming', $upcomingEvents)
                ->description('Events in the next 30 days')
                ->descriptionIcon('heroicon-m-arrow-trending-up')
                ->color('purple'),

            Stat::make('Member Type', $member->vip ? 'VIP Member' : 'Regular Member')
                ->description($member->vip ? 'Enjoy VIP privileges' : 'Upgrade to VIP for more privileges')
                ->descriptionIcon($member->vip ? 'heroicon-m-star' : 'heroicon-m-user')
                ->color($member->vip ? 'warning' : 'gray'),
        ];
    }

    private function getEventChart(): array
    {
        $member = Auth::guard('member')->user();

        if (!$member) {
            return [];
        }

        // Get the number of conferences published in the last 6 months
        $data = [];
        for ($i = 5; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $count = $member->events()
                ->whereYear('addtime', $date->year)
                ->whereMonth('addtime', $date->month)
                ->count();
            $data[] = $count;
        }

        return $data;
    }
}
