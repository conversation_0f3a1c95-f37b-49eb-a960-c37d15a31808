# iConf项目文档说明

## 📁 文档结构

本目录包含了iConf学术会议网站项目的完整技术文档，为从ThinkPHP 3.2升级到Laravel 12提供详细的参考资料。

### 📋 主要文档

| 文档名称 | 描述 | 用途 |
|---------|------|------|
| `老项目技术说明文档.md` | 老项目的完整技术分析 | 升级参考、技术理解 |
| `升级对比表.md` | 新老项目技术对比 | 升级规划、风险评估 |
| `项目架构图表文档.md` | 可视化架构图表 | 架构理解、文档展示 |
| `图表导出使用指南.md` | 图表使用和导出指南 | 工具使用、图表管理 |
| `项目说明.md` | 项目背景和基本信息 | 项目概述 |

### 🛠️ 工具脚本

| 脚本名称 | 平台 | 功能 |
|---------|------|------|
| `export_charts.bat` | Windows | 自动导出图表为PNG格式 |
| `export_charts.sh` | Linux/macOS | 自动导出图表为PNG格式 |

## 🎯 快速开始

### 1. 查看项目架构
```bash
# 直接查看Markdown文档中的图表
# 推荐使用支持Mermaid的编辑器，如：
# - Typora
# - VS Code (with Mermaid extension)
# - GitHub/GitLab在线查看
```

### 2. 导出图表为图片

#### Windows用户
```cmd
# 确保已安装Node.js和mermaid-cli
npm install -g @mermaid-js/mermaid-cli

# 运行导出脚本
cd doc
export_charts.bat
```

#### Linux/macOS用户
```bash
# 确保已安装Node.js和mermaid-cli
npm install -g @mermaid-js/mermaid-cli

# 运行导出脚本
cd doc
chmod +x export_charts.sh
./export_charts.sh
```

### 3. 在线查看图表
访问 [Mermaid Live Editor](https://mermaid.live/) 并粘贴图表代码进行在线查看和编辑。

## 📊 图表说明

### 项目架构图
- **用途**: 展示整体技术架构
- **包含**: 前端、应用层、业务逻辑层、数据存储层
- **适用**: 项目介绍、技术方案展示

### 数据库关系图
- **用途**: 展示数据表结构和关系
- **包含**: 11个核心数据表及其关联关系
- **适用**: 数据库设计、开发参考

### 业务流程图
- **用途**: 展示用户操作和管理流程
- **包含**: 用户端、管理端、推荐机制、搜索功能
- **适用**: 业务理解、需求分析

## 🔧 环境要求

### 查看图表
- **在线**: 任何现代浏览器
- **本地**: 支持Mermaid的Markdown编辑器

### 导出图表
- **Node.js**: 14.0+
- **mermaid-cli**: 最新版本
- **操作系统**: Windows/Linux/macOS

### 安装mermaid-cli
```bash
# 全局安装
npm install -g @mermaid-js/mermaid-cli

# 验证安装
mmdc --version
```

## 📝 使用建议

### 文档阅读顺序
1. **项目说明.md** - 了解项目背景
2. **老项目技术说明文档.md** - 深入理解老项目
3. **升级对比表.md** - 了解升级方案
4. **项目架构图表文档.md** - 可视化理解架构

### 图表使用场景
- **项目汇报**: 使用架构图展示技术方案
- **开发参考**: 使用数据库关系图指导开发
- **培训材料**: 使用业务流程图说明系统功能
- **文档编写**: 将图表嵌入到技术文档中

### 最佳实践
1. **版本控制**: 将图表源代码纳入版本控制
2. **定期更新**: 随着项目进展更新图表
3. **格式选择**: 
   - 文档嵌入: PNG格式
   - 演示展示: SVG格式
   - 打印输出: PDF格式

## 🚀 升级指导

### 使用文档进行升级
1. **需求分析**: 基于老项目技术文档理解现有功能
2. **架构设计**: 参考升级对比表设计新架构
3. **数据迁移**: 使用数据表映射进行数据迁移
4. **功能实现**: 按照业务流程图实现新功能
5. **测试验证**: 确保所有功能正确迁移

### 关键注意事项
- **数据完整性**: 确保数据迁移过程中不丢失数据
- **URL兼容性**: 保持SEO友好的URL结构
- **用户体验**: 保持或改善现有用户体验
- **性能优化**: 利用新技术提升系统性能

## 🔍 故障排除

### 图表不显示
**问题**: Markdown编辑器中图表不显示
**解决**: 
- 确保编辑器支持Mermaid
- 检查图表语法是否正确
- 尝试在线工具验证

### 导出失败
**问题**: 运行导出脚本失败
**解决**:
- 检查Node.js和mermaid-cli是否正确安装
- 确保网络连接正常
- 查看错误信息并相应处理

### 中文显示问题
**问题**: 导出的图片中文显示异常
**解决**:
- 确保系统安装了中文字体
- 使用在线工具导出
- 调整字体设置

## 📞 技术支持

### 相关资源
- [Mermaid官方文档](https://mermaid-js.github.io/mermaid/)
- [Mermaid Live Editor](https://mermaid.live/)
- [ThinkPHP 3.2文档](http://document.thinkphp.cn/manual_3_2.html)
- [Laravel 12文档](https://laravel.com/docs)

### 常用工具
- **Typora**: 所见即所得Markdown编辑器
- **VS Code**: 代码编辑器 + Mermaid扩展
- **GitHub**: 在线查看和协作
- **Figma**: 高级图表设计工具

## 📄 许可证

本文档遵循项目许可证，仅供内部使用和参考。

---

**文档版本**: v1.0  
**创建日期**: 2025-06-24  
**最后更新**: 2025-06-24  
**维护人员**: 开发团队

## 📋 更新日志

### v1.0 (2025-06-24)
- 创建完整的技术文档体系
- 添加可视化架构图表
- 提供图表导出工具
- 建立升级对比参考
