<?php

namespace App\Filament\Member\Pages;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Pages\Page;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rules\Password;

class EditProfile extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-user-circle';
    
    protected static string $view = 'filament.member.pages.edit-profile';
    
    protected static ?string $title = 'Edit Profile';
    
    protected static ?string $navigationLabel = 'Profile';

    protected static ?string $navigationGroup = null;

    protected static ?int $navigationSort = 2;
    
    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    public ?array $data = [];

    public function mount(): void
    {
        $user = Auth::user();
        $this->form->fill([
            'username' => $user->username,
            'email' => $user->email,
            'area' => $user->area,
        ]);
    }

    public function form(Form $form): Form
    {
        $user = Auth::user();

        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\TextInput::make('username')
                            ->label('Username')
                            ->required()
                            ->maxLength(100)
                            ->unique('member', 'username', $user)
                            ->helperText('Your username will be displayed as your name'),

                        Forms\Components\TextInput::make('email')
                            ->label('Email Address')
                            ->email()
                            ->required()
                            ->maxLength(100)
                            ->unique('member', 'email', $user)
                            ->helperText('Email address is used for login and notifications'),

                        Forms\Components\TextInput::make('area')
                            ->label('Region')
                            ->maxLength(5)
                            ->helperText('Please enter your region'),
                    ])
                    ->columns(1),

                Forms\Components\Section::make('Change Password')
                    ->schema([
                        Forms\Components\TextInput::make('current_password')
                            ->label('Current Password')
                            ->password()
                            ->requiredWith('password')
                            ->helperText('Current password is required when changing password'),

                        Forms\Components\TextInput::make('password')
                            ->label('New Password')
                            ->password()
                            ->rule(Password::default())
                            ->same('password_confirmation')
                            ->helperText('Leave blank to not change password, password must be at least 8 characters'),

                        Forms\Components\TextInput::make('password_confirmation')
                            ->label('Confirm New Password')
                            ->password()
                            ->requiredWith('password')
                            ->helperText('Please re-enter the new password'),
                    ])
                    ->columns(1)
                    ->collapsible()
                    ->collapsed(),
            ])
            ->statePath('data');
    }

    protected function getFormActions(): array
    {
        return [
            Action::make('save')
                ->label('Save Changes')
                ->action('save'),
        ];
    }

    public function save(): void
    {
        $data = $this->form->getState();
        $user = Auth::user();

        // If changing password, validate current password
        if (!empty($data['password'])) {
            if (empty($data['current_password'])) {
                Notification::make()
                    ->title('Please enter current password')
                    ->danger()
                    ->send();
                return;
            }

            if (!Hash::check($data['current_password'], $user->password)) {
                Notification::make()
                    ->title('Current password is incorrect')
                    ->danger()
                    ->send();
                return;
            }
        }

        // Update basic information
        $updateData = [
            'username' => $data['username'],
            'email' => $data['email'],
            'area' => $data['area'] ?? null,
        ];

        // If changing password
        if (!empty($data['password'])) {
            $updateData['password'] = $data['password']; // The model will automatically encrypt
        }

        $user->update($updateData);

        Notification::make()
            ->title('Profile updated successfully')
            ->success()
            ->send();

        // Re-fill the form (clear password fields)
        $this->form->fill([
            'username' => $user->username,
            'email' => $user->email,
            'area' => $user->area,
        ]);
    }
}
