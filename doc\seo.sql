-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        5.7.40 - MySQL Community Server (GPL)
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.3.0.6589
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


-- 导出 iconf_laravel_db 的数据库结构
CREATE DATABASE IF NOT EXISTS `iconf_laravel_db` /*!40100 DEFAULT CHARACTER SET utf8 */;
USE `iconf_laravel_db`;

-- 导出  表 iconf_laravel_db.seo_settings 结构
CREATE TABLE IF NOT EXISTS `seo_settings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `page_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '页面类型标识',
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'SEO标题',
  `keywords` text COLLATE utf8mb4_unicode_ci COMMENT 'SEO关键词',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT 'SEO描述',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `seo_settings_page_type_unique` (`page_type`),
  KEY `seo_settings_page_type_index` (`page_type`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 正在导出表  iconf_laravel_db.seo_settings 的数据：~10 rows (大约)
INSERT INTO `seo_settings` (`id`, `page_type`, `title`, `keywords`, `description`, `created_at`, `updated_at`) VALUES
	(1, 'default', 'ICONF | Academic Conferences Find – Upcoming International Conferences website', 'academic conferences,international conferences,call for papers,cfp,conference list,conference calendar,conference alerts,conference papers,conference papers submission,scopus conferences,ieee conferences,ai conferences,it conferences,flagship conferences', 'Iconf offers everyone the latest academic conference inquiries,international academic conferences,and provides submission methods to comprehensively solve your problems-iconf', '2025-06-24 02:32:23', '2025-06-24 02:36:52'),
	(2, 'home', 'ICONF | Academic Conferences Find – Upcoming International Conferences web', 'academic conferences,international conferences,call for papers,cfp,conference list,conference calendar,conference alerts,conference papers,conference papers submission,scopus conferences,ieee conferences,ai conferences,it conferences,flagship conferences', 'Iconf offers everyone the latest academic conference inquiries,international academic conferences,and provides submission methods to comprehensively solve your problems-iconf', '2025-06-24 02:32:23', '2025-06-28 07:41:11'),
	(3, 'conferences_home', 'Academic Conferences 2025,all conference list,call for paper-iconf ', 'academic conferences,international conferences,call for papers,cfp,conference list,conference calendar,conference alerts,conference papers,conference papers submission,scopus conferences,ieee conferences,ai conferences,it conferences,flagship conferences', 'Discover and explore upcoming global academic conferences. A comprehensive platform offering detailed schedules, registration, and call-for-papers for scholars and researchers-iconf', '2025-06-24 02:32:23', '2025-06-24 02:32:23'),
	(4, 'conferences_list', '{name},Academic Conferences 2025,all conference list,call for paper-iconf ', 'academic conferences,international conferences,call for papers,cfp,conference list,conference calendar,conference alerts,conference papers,conference papers submission,scopus conferences,ieee conferences,ai conferences,it conferences,flagship conferences', 'Discover and explore upcoming global academic conferences. A comprehensive platform offering detailed schedules, registration, and call-for-papers for scholars and researchers-iconf', '2025-06-24 02:32:23', '2025-06-24 02:32:23'),
	(5, 'conferences_show', 'Academic Conferences 2025,call for paper-iconf ', 'academic conferences,international conferences,call for papers,cfp,conference list,conference calendar,conference alerts,conference papers,conference papers submission,scopus conferences,ieee conferences,ai conferences,it conferences,flagship conferences', 'Discover and explore upcoming global academic conferences. A comprehensive platform offering detailed schedules, registration, and call-for-papers for scholars and researchers-iconf', '2025-06-24 02:32:23', '2025-06-24 02:32:23'),
	(6, 'news', 'Academic Conferences Inquiry news', 'Academic conferences headlines, academic conference hot information, academic information ', ' iconf has a large number of academic conferences and academic journals. We specialize in providing convenience and services to professional academic researchers and try our best to meet all your requirements.', '2025-06-24 02:32:24', '2025-06-24 02:32:24'),
	(7, 'newslist', 'Academic Conferences Inquiry news', 'Academic headlines, academic conference hot information, academic information ', ' iconf has a large number of academic conferences and academic journals. We specialize in providing convenience and services to professional academic researchers and try our best to meet all your requirements.', '2025-06-24 02:32:24', '2025-06-24 02:32:24'),
	(8, 'page', ' contact us ', ' contact us ', 'Multi-theme academic conference center, conference live broadcast, academic classroom, academic hot information and other service sections.', '2025-06-24 02:32:24', '2025-06-24 02:32:24'),
	(9, 'variables', '', '', '', '2025-06-28 07:41:04', '2025-06-28 07:41:04'),
	(10, 'best_practices', '', '', '', '2025-06-28 07:41:04', '2025-06-28 07:41:04');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
