{"__meta": {"id": "01K09GCST7QVQ26BHPE759FY28", "datetime": "2025-07-16 11:37:23", "utime": **********.527961, "method": "GET", "uri": "/admin/users", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.538087, "end": **********.527969, "duration": 0.9898819923400879, "duration_str": "990ms", "measures": [{"label": "Booting", "start": **********.538087, "relative_start": 0, "end": **********.740359, "relative_end": **********.740359, "duration": 0.****************, "duration_str": "202ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.740366, "relative_start": 0.*****************, "end": **********.52797, "relative_end": 1.1920928955078125e-06, "duration": 0.****************, "duration_str": "788ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.748496, "relative_start": 0.*****************, "end": **********.749851, "relative_end": **********.749851, "duration": 0.0013549327850341797, "duration_str": "1.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::bd31e88145d24c6980a842fbcee446e7", "start": **********.805319, "relative_start": 0.*****************, "end": **********.805319, "relative_end": **********.805319, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "start": **********.810067, "relative_start": 0.*****************, "end": **********.810067, "relative_end": **********.810067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.815011, "relative_start": 0.27692413330078125, "end": **********.815011, "relative_end": **********.815011, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.821514, "relative_start": 0.28342700004577637, "end": **********.821514, "relative_end": **********.821514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.903008, "relative_start": 0.36492109298706055, "end": **********.903008, "relative_end": **********.903008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9b0aa906eb507785d5e713f2ff316d37", "start": **********.093956, "relative_start": 0.5558691024780273, "end": **********.093956, "relative_end": **********.093956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9b0aa906eb507785d5e713f2ff316d37", "start": **********.219597, "relative_start": 0.6815102100372314, "end": **********.219597, "relative_end": **********.219597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9b0aa906eb507785d5e713f2ff316d37", "start": **********.220701, "relative_start": 0.6826140880584717, "end": **********.220701, "relative_end": **********.220701, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.421948, "relative_start": 0.8838610649108887, "end": **********.421948, "relative_end": **********.421948, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.424679, "relative_start": 0.8865921497344971, "end": **********.424679, "relative_end": **********.424679, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.439449, "relative_start": 0.9013621807098389, "end": **********.439449, "relative_end": **********.439449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.449183, "relative_start": 0.9110960960388184, "end": **********.449183, "relative_end": **********.449183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.454616, "relative_start": 0.9165291786193848, "end": **********.454616, "relative_end": **********.454616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.500976, "relative_start": 0.9628891944885254, "end": **********.501095, "relative_end": **********.501095, "duration": 0.00011897087097167969, "duration_str": "119μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.526673, "relative_start": 0.9885861873626709, "end": **********.526707, "relative_end": **********.526707, "duration": 3.3855438232421875e-05, "duration_str": "34μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 49903696, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.4.2", "Environment": "local", "Debug Mode": "Enabled", "URL": "iconf.lv", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 13, "nb_templates": 13, "templates": [{"name": "__components::bd31e88145d24c6980a842fbcee446e7", "param_count": null, "params": [], "start": **********.80531, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\storage\\framework\\views/bd31e88145d24c6980a842fbcee446e7.blade.php__components::bd31e88145d24c6980a842fbcee446e7", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fstorage%2Fframework%2Fviews%2Fbd31e88145d24c6980a842fbcee446e7.blade.php&line=1", "ajax": false, "filename": "bd31e88145d24c6980a842fbcee446e7.blade.php", "line": "?"}}, {"name": "__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "param_count": null, "params": [], "start": **********.810059, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\storage\\framework\\views/9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fstorage%2Fframework%2Fviews%2F9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php&line=1", "ajax": false, "filename": "9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php", "line": "?"}}, {"name": "__components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.815002, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.821506, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.902998, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.093947, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.219588, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.220693, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.421938, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.424671, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.43944, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.449175, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.454607, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}]}, "queries": {"count": 28, "nb_statements": 27, "nb_visible_statements": 28, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.036399999999999995, "accumulated_duration_str": "36.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.755552, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'n81JYPLEP0cENw7ZqI8BBaufrhZyV3CgHX5ObznX' limit 1", "type": "query", "params": [], "bindings": ["n81JYPLEP0cENw7ZqI8BBaufrhZyV3CgHX5ObznX"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.756234, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 0, "width_percent": 3.599}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.760652, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 3.599, "width_percent": 0.495}, {"sql": "select count(*) as aggregate from `users`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.7937632, "duration": 0.00713, "duration_str": "7.13ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 4.093, "width_percent": 19.588}, {"sql": "select * from `users` order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.801848, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 23.681, "width_percent": 0.467}, {"sql": "select count(*) as aggregate from `ad_txt`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/AdvertisementResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\AdvertisementResource.php", "line": 312}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.456187, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "AdvertisementResource.php:312", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/AdvertisementResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\AdvertisementResource.php", "line": 312}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FFilament%2FResources%2FAdvertisementResource.php&line=312", "ajax": false, "filename": "AdvertisementResource.php", "line": "312"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 24.148, "width_percent": 1.593}, {"sql": "select count(*) as aggregate from `ad_txt` where `endtime` <= **********", "type": "query", "params": [], "bindings": [**********], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/AdvertisementResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\AdvertisementResource.php", "line": 320}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.457501, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "AdvertisementResource.php:320", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/AdvertisementResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\AdvertisementResource.php", "line": 320}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FFilament%2FResources%2FAdvertisementResource.php&line=320", "ajax": false, "filename": "AdvertisementResource.php", "line": "320"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 25.742, "width_percent": 0.467}, {"sql": "select count(*) as aggregate from `ad_txt`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/AdvertisementResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\AdvertisementResource.php", "line": 321}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.4580119, "duration": 5.9999999999999995e-05, "duration_str": "60μs", "memory": 0, "memory_str": null, "filename": "AdvertisementResource.php:321", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/AdvertisementResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\AdvertisementResource.php", "line": 321}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FFilament%2FResources%2FAdvertisementResource.php&line=321", "ajax": false, "filename": "AdvertisementResource.php", "line": "321"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 26.209, "width_percent": 0.165}, {"sql": "select count(*) as aggregate from `category`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/CategoryResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\CategoryResource.php", "line": 218}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.458769, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "CategoryResource.php:218", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/CategoryResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\CategoryResource.php", "line": 218}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FFilament%2FResources%2FCategoryResource.php&line=218", "ajax": false, "filename": "CategoryResource.php", "line": "218"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 26.374, "width_percent": 0.742}, {"sql": "select count(*) as aggregate from `country`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/CountryResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\CountryResource.php", "line": 215}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.459509, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "CountryResource.php:215", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/CountryResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\CountryResource.php", "line": 215}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FFilament%2FResources%2FCountryResource.php&line=215", "ajax": false, "filename": "CountryResource.php", "line": "215"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 27.115, "width_percent": 0.467}, {"sql": "select count(*) as aggregate from `event`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/EventResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\EventResource.php", "line": 429}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.460654, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "EventResource.php:429", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/EventResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\EventResource.php", "line": 429}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FFilament%2FResources%2FEventResource.php&line=429", "ajax": false, "filename": "EventResource.php", "line": "429"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 27.582, "width_percent": 0.604}, {"sql": "select count(*) as aggregate from `event`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/EventResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\EventResource.php", "line": 437}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.461195, "duration": 5e-05, "duration_str": "50μs", "memory": 0, "memory_str": null, "filename": "EventResource.php:437", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/EventResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\EventResource.php", "line": 437}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FFilament%2FResources%2FEventResource.php&line=437", "ajax": false, "filename": "EventResource.php", "line": "437"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 28.187, "width_percent": 0.137}, {"sql": "select count(*) as aggregate from `member`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/MemberResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\MemberResource.php", "line": 363}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.4624531, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "MemberResource.php:363", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/MemberResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\MemberResource.php", "line": 363}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FFilament%2FResources%2FMemberResource.php&line=363", "ajax": false, "filename": "MemberResource.php", "line": "363"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 28.324, "width_percent": 1.374}, {"sql": "select count(*) as aggregate from `news`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/NewsResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\NewsResource.php", "line": 258}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.463816, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "NewsResource.php:258", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/NewsResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\NewsResource.php", "line": 258}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FFilament%2FResources%2FNewsResource.php&line=258", "ajax": false, "filename": "NewsResource.php", "line": "258"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 29.698, "width_percent": 0.22}, {"sql": "select count(*) as aggregate from `news`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/NewsResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\NewsResource.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.4642498, "duration": 5e-05, "duration_str": "50μs", "memory": 0, "memory_str": null, "filename": "NewsResource.php:266", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/NewsResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\NewsResource.php", "line": 266}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FFilament%2FResources%2FNewsResource.php&line=266", "ajax": false, "filename": "NewsResource.php", "line": "266"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 29.918, "width_percent": 0.137}, {"sql": "select count(*) as aggregate from `news_type`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/NewsTypeResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\NewsTypeResource.php", "line": 131}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.4648478, "duration": 5.9999999999999995e-05, "duration_str": "60μs", "memory": 0, "memory_str": null, "filename": "NewsTypeResource.php:131", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/NewsTypeResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\NewsTypeResource.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FFilament%2FResources%2FNewsTypeResource.php&line=131", "ajax": false, "filename": "NewsTypeResource.php", "line": "131"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 30.055, "width_percent": 0.165}, {"sql": "select count(*) as aggregate from `ad_txt`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/AdvertisementResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\AdvertisementResource.php", "line": 312}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.47491, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "AdvertisementResource.php:312", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/AdvertisementResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\AdvertisementResource.php", "line": 312}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FFilament%2FResources%2FAdvertisementResource.php&line=312", "ajax": false, "filename": "AdvertisementResource.php", "line": "312"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 30.22, "width_percent": 0.385}, {"sql": "select count(*) as aggregate from `ad_txt` where `endtime` <= **********", "type": "query", "params": [], "bindings": [**********], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/AdvertisementResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\AdvertisementResource.php", "line": 320}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.475454, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "AdvertisementResource.php:320", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/AdvertisementResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\AdvertisementResource.php", "line": 320}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FFilament%2FResources%2FAdvertisementResource.php&line=320", "ajax": false, "filename": "AdvertisementResource.php", "line": "320"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 30.604, "width_percent": 0.357}, {"sql": "select count(*) as aggregate from `ad_txt`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/AdvertisementResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\AdvertisementResource.php", "line": 321}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.475898, "duration": 5e-05, "duration_str": "50μs", "memory": 0, "memory_str": null, "filename": "AdvertisementResource.php:321", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/AdvertisementResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\AdvertisementResource.php", "line": 321}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FFilament%2FResources%2FAdvertisementResource.php&line=321", "ajax": false, "filename": "AdvertisementResource.php", "line": "321"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 30.962, "width_percent": 0.137}, {"sql": "select count(*) as aggregate from `category`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/CategoryResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\CategoryResource.php", "line": 218}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.476348, "duration": 5e-05, "duration_str": "50μs", "memory": 0, "memory_str": null, "filename": "CategoryResource.php:218", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/CategoryResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\CategoryResource.php", "line": 218}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FFilament%2FResources%2FCategoryResource.php&line=218", "ajax": false, "filename": "CategoryResource.php", "line": "218"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 31.099, "width_percent": 0.137}, {"sql": "select count(*) as aggregate from `country`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/CountryResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\CountryResource.php", "line": 215}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.476793, "duration": 5e-05, "duration_str": "50μs", "memory": 0, "memory_str": null, "filename": "CountryResource.php:215", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/CountryResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\CountryResource.php", "line": 215}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FFilament%2FResources%2FCountryResource.php&line=215", "ajax": false, "filename": "CountryResource.php", "line": "215"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 31.236, "width_percent": 0.137}, {"sql": "select count(*) as aggregate from `event`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/EventResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\EventResource.php", "line": 429}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.477215, "duration": 4e-05, "duration_str": "40μs", "memory": 0, "memory_str": null, "filename": "EventResource.php:429", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/EventResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\EventResource.php", "line": 429}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FFilament%2FResources%2FEventResource.php&line=429", "ajax": false, "filename": "EventResource.php", "line": "429"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 31.374, "width_percent": 0.11}, {"sql": "select count(*) as aggregate from `event`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/EventResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\EventResource.php", "line": 437}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.477539, "duration": 4e-05, "duration_str": "40μs", "memory": 0, "memory_str": null, "filename": "EventResource.php:437", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/EventResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\EventResource.php", "line": 437}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FFilament%2FResources%2FEventResource.php&line=437", "ajax": false, "filename": "EventResource.php", "line": "437"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 31.484, "width_percent": 0.11}, {"sql": "select count(*) as aggregate from `member`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/MemberResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\MemberResource.php", "line": 363}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.4780588, "duration": 5e-05, "duration_str": "50μs", "memory": 0, "memory_str": null, "filename": "MemberResource.php:363", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/MemberResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\MemberResource.php", "line": 363}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FFilament%2FResources%2FMemberResource.php&line=363", "ajax": false, "filename": "MemberResource.php", "line": "363"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 31.593, "width_percent": 0.137}, {"sql": "select count(*) as aggregate from `news`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/NewsResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\NewsResource.php", "line": 258}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.478484, "duration": 5e-05, "duration_str": "50μs", "memory": 0, "memory_str": null, "filename": "NewsResource.php:258", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/NewsResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\NewsResource.php", "line": 258}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FFilament%2FResources%2FNewsResource.php&line=258", "ajax": false, "filename": "NewsResource.php", "line": "258"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 31.731, "width_percent": 0.137}, {"sql": "select count(*) as aggregate from `news`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/NewsResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\NewsResource.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.478853, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "NewsResource.php:266", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/NewsResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\NewsResource.php", "line": 266}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FFilament%2FResources%2FNewsResource.php&line=266", "ajax": false, "filename": "NewsResource.php", "line": "266"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 31.868, "width_percent": 0.302}, {"sql": "select count(*) as aggregate from `news_type`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/NewsTypeResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\NewsTypeResource.php", "line": 131}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.4793808, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "NewsTypeResource.php:131", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/NewsTypeResource.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Filament\\Resources\\NewsTypeResource.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FFilament%2FResources%2FNewsTypeResource.php&line=131", "ajax": false, "filename": "NewsTypeResource.php", "line": "131"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 32.17, "width_percent": 0.22}, {"sql": "update `sessions` set `payload` = 'YTo3OntzOjY6Il90b2tlbiI7czo0MDoiS3pWbkh3c2IxajdhRkxvZXBuQWdCUk1obmMxeFFnVk84V3drN3RaNCI7czoyMjoiUEhQREVCVUdCQVJfU1RBQ0tfREFUQSI7YTowOnt9czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjc6Imh0dHA6Ly9pY29uZi5sdi9hZG1pbi91c2VycyI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjE7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRpLmE1Z1A3L1owN3BrM0NlUjZ2TmZlOWxHbS5CdHRoSGxMaTEub2xqMEpKRGZkTmxDUlk4SyI7czo4OiJmaWxhbWVudCI7YTowOnt9fQ==', `last_activity` = **********, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' where `id` = 'n81JYPLEP0cENw7ZqI8BBaufrhZyV3CgHX5ObznX'", "type": "query", "params": [], "bindings": ["YTo3OntzOjY6Il90b2tlbiI7czo0MDoiS3pWbkh3c2IxajdhRkxvZXBuQWdCUk1obmMxeFFnVk84V3drN3RaNCI7czoyMjoiUEhQREVCVUdCQVJfU1RBQ0tfREFUQSI7YTowOnt9czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjc6Imh0dHA6Ly9pY29uZi5sdi9hZG1pbi91c2VycyI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjE7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRpLmE1Z1A3L1owN3BrM0NlUjZ2TmZlOWxHbS5CdHRoSGxMaTEub2xqMEpKRGZkTmxDUlk4SyI7czo4OiJmaWxhbWVudCI7YTowOnt9fQ==", **********, 1, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "n81JYPLEP0cENw7ZqI8BBaufrhZyV3CgHX5ObznX"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": **********.501687, "duration": 0.02461, "duration_str": "24.61ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 32.39, "width_percent": 67.61}]}, "models": {"data": {"App\\Models\\User": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 3, "is_counter": true}, "livewire": {"data": {"app.filament.resources.user-resource.pages.list-users #7B8P4A6PHpTnMUnSED9G": "array:4 [\n  \"data\" => array:39 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:1 [\n      \"is_founder\" => array:1 [\n        \"value\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areFormStateUpdateHooksDisabledForTesting\" => false\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => array:3 [\n      \"email_verified_at\" => true\n      \"created_at\" => false\n      \"updated_at\" => false\n    ]\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.resources.user-resource.pages.list-users\"\n  \"component\" => \"App\\Filament\\Resources\\UserResource\\Pages\\ListUsers\"\n  \"id\" => \"7B8P4A6PHpTnMUnSED9G\"\n]", "filament.livewire.notifications #onWWFfT5zBkEgsvUhwyc": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2853\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"onWWFfT5zBkEgsvUhwyc\"\n]"}, "count": 2}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://iconf.lv/admin/users", "action_name": "filament.admin.resources.users.index", "controller_action": "App\\Filament\\Resources\\UserResource\\Pages\\ListUsers", "uri": "GET admin/users", "controller": "App\\Filament\\Resources\\UserResource\\Pages\\ListUsers@render<a href=\"phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/users", "file": "<a href=\"phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate", "duration": "990ms", "peak_memory": "54MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"719 characters\">XSRF-TOKEN=eyJpdiI6IlhEU1lJenVGcFlmamMvSm1oajBJYkE9PSIsInZhbHVlIjoiamF5aEZrRzJQVCtPemJjSkt4T1FHUHdUMHc2RFJMcHhFcUY0TS9Gd1dUTHM4TXBCTmpoUlROaXVtc1kzYkxZWDhVOVlXSmg2c2tZMmRCb2paVko3NTI5d1QvdVZEYURWYXcrNmNMNU1YSzhSNmZreVFuTHJObXpnUlh0bXl0dUIiLCJtYWMiOiIyYzRmMWRiNjcwZDQ1MjE3NzEyNzU1NGZlMjY3ZDNiNjEzNDc2M2QyMjI3MDA2NjNlMjk0YjE3OGIwZTA3MGFjIiwidGFnIjoiIn0%3D; iconf_meeting_session=eyJpdiI6InVBcWJBN1RKTDZYbEFocGp5U2xIdVE9PSIsInZhbHVlIjoieGYyM3djWGhPc1dmcHF2dU54N2NLMjhhSStUdzNwVVRWSklrSjlTNHlIVHVQTFdhWXhEdjdGMmdnMm1DSGlBSGZjcko5UXB4bXBmRHdYSTBTeEtrMUhqNnVybDRKNUt4RXJYZEp0eGswckFnNFZJQTNnR21IU2d6NGtXY1BQZWciLCJtYWMiOiIyNzA2NWU2NTAzMWEyMzY3MzEwNmFiNWJkZGJiODkxYTcwY2RlMDdlMjVmYjkwY2JlMTExNzgyNGQ1MDQ3ZWY0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://iconf.lv/admin/categories</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">iconf.lv</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2082363692 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KzVnHwsb1j7aFLoepnAgBRMhnc1xQgVO8Wwk7tZ4</span>\"\n  \"<span class=sf-dump-key>iconf_meeting_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">n81JYPLEP0cENw7ZqI8BBaufrhZyV3CgHX5ObznX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2082363692\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1381441470 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 16 Jul 2025 11:37:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlFyQ2R2WEZzNFovN2JKdjgxb3FwWWc9PSIsInZhbHVlIjoiWkd0NTRjR3d1MTlOSXFNUkR0WXArNmdkK0hnNmd4WVdVdGorQkcreTFFSFIrQ0V6R0VENGZuM2NINVhIdktGWldwazA2U0pVYjZHanJlMXZJZmdNUjhuS01ud1FZSzQwdm4xbytHajdkY3ZiM3RtU2JNcktNcStlM2ZSb3owY0QiLCJtYWMiOiJmNzcwNzc4NWVjOGZmZTk2ODhjMmYxNmM5MTJiZjY4YjYzYWJlZTdlMDVhMWQ1ZGFmMGNlZjljMzg5N2Q5ODVmIiwidGFnIjoiIn0%3D; expires=Wed, 16 Jul 2025 13:37:23 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">iconf_meeting_session=eyJpdiI6Im5vK3VhOXp3WkhydHYrUzZ3dW5sanc9PSIsInZhbHVlIjoiZGdEOGN0QktoNTFyQUZMcjFuaVVYOXRMVGxndks0bDVnaDV4K0hwc2hDaUlVQ2lKcDl4YUxaZkhpNHdJWHpYbjkrNlpzeEFrU2lZczYyTytEUDZEZmNhcENoSm9xOUhJdWZsY1pmYUE3ZWFmVjBFRFlvS2puUDA3ZjJHbjMxeEUiLCJtYWMiOiI4OTIzODc2NDU3MDM4YzMzNzBhNTBkMjA5YmExYjVlZmI0ZDk1ZjVkZWM3YmZmZjY4YzFiMzg2OTE4MjYwNDViIiwidGFnIjoiIn0%3D; expires=Wed, 16 Jul 2025 13:37:23 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlFyQ2R2WEZzNFovN2JKdjgxb3FwWWc9PSIsInZhbHVlIjoiWkd0NTRjR3d1MTlOSXFNUkR0WXArNmdkK0hnNmd4WVdVdGorQkcreTFFSFIrQ0V6R0VENGZuM2NINVhIdktGWldwazA2U0pVYjZHanJlMXZJZmdNUjhuS01ud1FZSzQwdm4xbytHajdkY3ZiM3RtU2JNcktNcStlM2ZSb3owY0QiLCJtYWMiOiJmNzcwNzc4NWVjOGZmZTk2ODhjMmYxNmM5MTJiZjY4YjYzYWJlZTdlMDVhMWQ1ZGFmMGNlZjljMzg5N2Q5ODVmIiwidGFnIjoiIn0%3D; expires=Wed, 16-Jul-2025 13:37:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">iconf_meeting_session=eyJpdiI6Im5vK3VhOXp3WkhydHYrUzZ3dW5sanc9PSIsInZhbHVlIjoiZGdEOGN0QktoNTFyQUZMcjFuaVVYOXRMVGxndks0bDVnaDV4K0hwc2hDaUlVQ2lKcDl4YUxaZkhpNHdJWHpYbjkrNlpzeEFrU2lZczYyTytEUDZEZmNhcENoSm9xOUhJdWZsY1pmYUE3ZWFmVjBFRFlvS2puUDA3ZjJHbjMxeEUiLCJtYWMiOiI4OTIzODc2NDU3MDM4YzMzNzBhNTBkMjA5YmExYjVlZmI0ZDk1ZjVkZWM3YmZmZjY4YzFiMzg2OTE4MjYwNDViIiwidGFnIjoiIn0%3D; expires=Wed, 16-Jul-2025 13:37:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1381441470\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1598105003 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KzVnHwsb1j7aFLoepnAgBRMhnc1xQgVO8Wwk7tZ4</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://iconf.lv/admin/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$i.a5gP7/Z07pk3CeR6vNfe9lGm.BtthHlLi1.olj0JJDfdNlCRY8K</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1598105003\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://iconf.lv/admin/users", "action_name": "filament.admin.resources.users.index", "controller_action": "App\\Filament\\Resources\\UserResource\\Pages\\ListUsers"}, "badge": null}}