{"__meta": {"id": "01K09FWKK73BREJM9GGYR5YW6A", "datetime": "2025-07-16 11:28:32", "utime": **********.872193, "method": "GET", "uri": "/conferences?category=&country=&year=", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.603888, "end": **********.872201, "duration": 0.268312931060791, "duration_str": "268ms", "measures": [{"label": "Booting", "start": **********.603888, "relative_start": 0, "end": **********.805478, "relative_end": **********.805478, "duration": 0.*****************, "duration_str": "202ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.805485, "relative_start": 0.*****************, "end": **********.872202, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "66.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.813568, "relative_start": 0.*****************, "end": **********.81501, "relative_end": **********.81501, "duration": 0.*****************, "duration_str": "1.44ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.842716, "relative_start": 0.*****************, "end": **********.871056, "relative_end": **********.871056, "duration": 0.*****************, "duration_str": "28.34ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: frontend.home.conferences", "start": **********.843935, "relative_start": 0.*****************, "end": **********.843935, "relative_end": **********.843935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.conference-filter", "start": **********.853877, "relative_start": 0.*****************, "end": **********.853877, "relative_end": **********.853877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.855649, "relative_start": 0.25176095962524414, "end": **********.855649, "relative_end": **********.855649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.855962, "relative_start": 0.2520740032196045, "end": **********.855962, "relative_end": **********.855962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.856195, "relative_start": 0.2523069381713867, "end": **********.856195, "relative_end": **********.856195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.856411, "relative_start": 0.25252294540405273, "end": **********.856411, "relative_end": **********.856411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.856625, "relative_start": 0.25273704528808594, "end": **********.856625, "relative_end": **********.856625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.856815, "relative_start": 0.2529270648956299, "end": **********.856815, "relative_end": **********.856815, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.857009, "relative_start": 0.25312089920043945, "end": **********.857009, "relative_end": **********.857009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.857223, "relative_start": 0.25333499908447266, "end": **********.857223, "relative_end": **********.857223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.857424, "relative_start": 0.2535359859466553, "end": **********.857424, "relative_end": **********.857424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.category.conference-tags", "start": **********.857624, "relative_start": 0.2537360191345215, "end": **********.857624, "relative_end": **********.857624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire::tailwind", "start": **********.857955, "relative_start": 0.25406694412231445, "end": **********.857955, "relative_end": **********.857955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.conference.creative-grid", "start": **********.861553, "relative_start": 0.25766491889953613, "end": **********.861553, "relative_end": **********.861553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.conference.creative-card", "start": **********.86183, "relative_start": 0.25794196128845215, "end": **********.86183, "relative_end": **********.86183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.conference.creative-card", "start": **********.862146, "relative_start": 0.2582578659057617, "end": **********.862146, "relative_end": **********.862146, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.conference.creative-card", "start": **********.862385, "relative_start": 0.2584969997406006, "end": **********.862385, "relative_end": **********.862385, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.conference.creative-card", "start": **********.862572, "relative_start": 0.2586839199066162, "end": **********.862572, "relative_end": **********.862572, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.conference.creative-card", "start": **********.862745, "relative_start": 0.25885701179504395, "end": **********.862745, "relative_end": **********.862745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.conference.creative-card", "start": **********.862916, "relative_start": 0.25902795791625977, "end": **********.862916, "relative_end": **********.862916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.conference.creative-card", "start": **********.863104, "relative_start": 0.2592160701751709, "end": **********.863104, "relative_end": **********.863104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.conference.creative-card", "start": **********.863284, "relative_start": 0.2593960762023926, "end": **********.863284, "relative_end": **********.863284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.conference.creative-grid", "start": **********.863456, "relative_start": 0.2595679759979248, "end": **********.863456, "relative_end": **********.863456, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.conference.creative-card", "start": **********.863597, "relative_start": 0.25970888137817383, "end": **********.863597, "relative_end": **********.863597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.conference.creative-card", "start": **********.863773, "relative_start": 0.2598850727081299, "end": **********.863773, "relative_end": **********.863773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.conference.creative-card", "start": **********.863946, "relative_start": 0.2600579261779785, "end": **********.863946, "relative_end": **********.863946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.conference.creative-card", "start": **********.864107, "relative_start": 0.26021885871887207, "end": **********.864107, "relative_end": **********.864107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.conference.creative-card", "start": **********.864258, "relative_start": 0.26037001609802246, "end": **********.864258, "relative_end": **********.864258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.conference.creative-card", "start": **********.864405, "relative_start": 0.260516881942749, "end": **********.864405, "relative_end": **********.864405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.conference.creative-card", "start": **********.86456, "relative_start": 0.26067185401916504, "end": **********.86456, "relative_end": **********.86456, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.conference.creative-card", "start": **********.864721, "relative_start": 0.2608330249786377, "end": **********.864721, "relative_end": **********.864721, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend.layouts.app", "start": **********.866464, "relative_start": 0.2625758647918701, "end": **********.866464, "relative_end": **********.866464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.navigation", "start": **********.867039, "relative_start": 0.2631509304046631, "end": **********.867039, "relative_end": **********.867039, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.friendly-links", "start": **********.86998, "relative_start": 0.26609206199645996, "end": **********.86998, "relative_end": **********.86998, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 46421832, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.4.2", "Environment": "local", "Debug Mode": "Enabled", "URL": "iconf.lv", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 34, "nb_templates": 34, "templates": [{"name": "frontend.home.conferences", "param_count": null, "params": [], "start": **********.843922, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/frontend/home/<USER>", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Ffrontend%2Fhome%2Fconferences.blade.php&line=1", "ajax": false, "filename": "conferences.blade.php", "line": "?"}}, {"name": "livewire.conference-filter", "param_count": null, "params": [], "start": **********.853868, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/livewire/conference-filter.blade.phplivewire.conference-filter", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Flivewire%2Fconference-filter.blade.php&line=1", "ajax": false, "filename": "conference-filter.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.855641, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.855955, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.856189, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.856405, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.856619, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.856809, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.857003, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.857217, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.857418, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "components.category.conference-tags", "param_count": null, "params": [], "start": **********.857619, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/category/conference-tags.blade.phpcomponents.category.conference-tags", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fcategory%2Fconference-tags.blade.php&line=1", "ajax": false, "filename": "conference-tags.blade.php", "line": "?"}}, {"name": "livewire::tailwind", "param_count": null, "params": [], "start": **********.857948, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination/views/tailwind.blade.phplivewire::tailwind", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2Fviews%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}}, {"name": "components.conference.creative-grid", "param_count": null, "params": [], "start": **********.861545, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/conference/creative-grid.blade.phpcomponents.conference.creative-grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fconference%2Fcreative-grid.blade.php&line=1", "ajax": false, "filename": "creative-grid.blade.php", "line": "?"}}, {"name": "components.conference.creative-card", "param_count": null, "params": [], "start": **********.861822, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/conference/creative-card.blade.phpcomponents.conference.creative-card", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fconference%2Fcreative-card.blade.php&line=1", "ajax": false, "filename": "creative-card.blade.php", "line": "?"}}, {"name": "components.conference.creative-card", "param_count": null, "params": [], "start": **********.862138, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/conference/creative-card.blade.phpcomponents.conference.creative-card", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fconference%2Fcreative-card.blade.php&line=1", "ajax": false, "filename": "creative-card.blade.php", "line": "?"}}, {"name": "components.conference.creative-card", "param_count": null, "params": [], "start": **********.862379, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/conference/creative-card.blade.phpcomponents.conference.creative-card", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fconference%2Fcreative-card.blade.php&line=1", "ajax": false, "filename": "creative-card.blade.php", "line": "?"}}, {"name": "components.conference.creative-card", "param_count": null, "params": [], "start": **********.862567, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/conference/creative-card.blade.phpcomponents.conference.creative-card", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fconference%2Fcreative-card.blade.php&line=1", "ajax": false, "filename": "creative-card.blade.php", "line": "?"}}, {"name": "components.conference.creative-card", "param_count": null, "params": [], "start": **********.862739, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/conference/creative-card.blade.phpcomponents.conference.creative-card", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fconference%2Fcreative-card.blade.php&line=1", "ajax": false, "filename": "creative-card.blade.php", "line": "?"}}, {"name": "components.conference.creative-card", "param_count": null, "params": [], "start": **********.862911, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/conference/creative-card.blade.phpcomponents.conference.creative-card", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fconference%2Fcreative-card.blade.php&line=1", "ajax": false, "filename": "creative-card.blade.php", "line": "?"}}, {"name": "components.conference.creative-card", "param_count": null, "params": [], "start": **********.863096, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/conference/creative-card.blade.phpcomponents.conference.creative-card", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fconference%2Fcreative-card.blade.php&line=1", "ajax": false, "filename": "creative-card.blade.php", "line": "?"}}, {"name": "components.conference.creative-card", "param_count": null, "params": [], "start": **********.863278, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/conference/creative-card.blade.phpcomponents.conference.creative-card", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fconference%2Fcreative-card.blade.php&line=1", "ajax": false, "filename": "creative-card.blade.php", "line": "?"}}, {"name": "components.conference.creative-grid", "param_count": null, "params": [], "start": **********.863451, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/conference/creative-grid.blade.phpcomponents.conference.creative-grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fconference%2Fcreative-grid.blade.php&line=1", "ajax": false, "filename": "creative-grid.blade.php", "line": "?"}}, {"name": "components.conference.creative-card", "param_count": null, "params": [], "start": **********.863592, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/conference/creative-card.blade.phpcomponents.conference.creative-card", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fconference%2Fcreative-card.blade.php&line=1", "ajax": false, "filename": "creative-card.blade.php", "line": "?"}}, {"name": "components.conference.creative-card", "param_count": null, "params": [], "start": **********.863768, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/conference/creative-card.blade.phpcomponents.conference.creative-card", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fconference%2Fcreative-card.blade.php&line=1", "ajax": false, "filename": "creative-card.blade.php", "line": "?"}}, {"name": "components.conference.creative-card", "param_count": null, "params": [], "start": **********.86394, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/conference/creative-card.blade.phpcomponents.conference.creative-card", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fconference%2Fcreative-card.blade.php&line=1", "ajax": false, "filename": "creative-card.blade.php", "line": "?"}}, {"name": "components.conference.creative-card", "param_count": null, "params": [], "start": **********.864102, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/conference/creative-card.blade.phpcomponents.conference.creative-card", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fconference%2Fcreative-card.blade.php&line=1", "ajax": false, "filename": "creative-card.blade.php", "line": "?"}}, {"name": "components.conference.creative-card", "param_count": null, "params": [], "start": **********.864253, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/conference/creative-card.blade.phpcomponents.conference.creative-card", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fconference%2Fcreative-card.blade.php&line=1", "ajax": false, "filename": "creative-card.blade.php", "line": "?"}}, {"name": "components.conference.creative-card", "param_count": null, "params": [], "start": **********.8644, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/conference/creative-card.blade.phpcomponents.conference.creative-card", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fconference%2Fcreative-card.blade.php&line=1", "ajax": false, "filename": "creative-card.blade.php", "line": "?"}}, {"name": "components.conference.creative-card", "param_count": null, "params": [], "start": **********.864555, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/conference/creative-card.blade.phpcomponents.conference.creative-card", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fconference%2Fcreative-card.blade.php&line=1", "ajax": false, "filename": "creative-card.blade.php", "line": "?"}}, {"name": "components.conference.creative-card", "param_count": null, "params": [], "start": **********.864716, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/conference/creative-card.blade.phpcomponents.conference.creative-card", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fconference%2Fcreative-card.blade.php&line=1", "ajax": false, "filename": "creative-card.blade.php", "line": "?"}}, {"name": "frontend.layouts.app", "param_count": null, "params": [], "start": **********.866457, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/frontend/layouts/app.blade.phpfrontend.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "components.navigation", "param_count": null, "params": [], "start": **********.867032, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/navigation.blade.phpcomponents.navigation", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Fnavigation.blade.php&line=1", "ajax": false, "filename": "navigation.blade.php", "line": "?"}}, {"name": "components.friendly-links", "param_count": null, "params": [], "start": **********.869972, "type": "blade", "hash": "bladeD:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/components/friendly-links.blade.phpcomponents.friendly-links", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fresources%2Fviews%2Fcomponents%2Ffriendly-links.blade.php&line=1", "ajax": false, "filename": "friendly-links.blade.php", "line": "?"}}]}, "queries": {"count": 13, "nb_statements": 12, "nb_visible_statements": 13, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.016340000000000004, "accumulated_duration_str": "16.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.820701, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'n81JYPLEP0cENw7ZqI8BBaufrhZyV3CgHX5ObznX' limit 1", "type": "query", "params": [], "bindings": ["n81JYPLEP0cENw7ZqI8BBaufrhZyV3CgHX5ObznX"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.821448, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 0, "width_percent": 7.528}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_featured_conferences_8')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_featured_conferences_8"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.826627, "duration": 0.01071, "duration_str": "10.71ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 7.528, "width_percent": 65.545}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_upcoming_conferences_8')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_upcoming_conferences_8"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.840003, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 73.072, "width_percent": 10.22}, {"sql": "select count(*) as aggregate from `event` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 231}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.8462012, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ConferenceFilter.php:231", "source": {"index": 16, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 231}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FLivewire%2FConferenceFilter.php&line=231", "ajax": false, "filename": "ConferenceFilter.php", "line": "231"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 83.293, "width_percent": 2.815}, {"sql": "select * from `event` where `status` = 1 order by `start_date` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 231}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.847172, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ConferenceFilter.php:231", "source": {"index": 16, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 231}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FLivewire%2FConferenceFilter.php&line=231", "ajax": false, "filename": "ConferenceFilter.php", "line": "231"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 86.108, "width_percent": 1.958}, {"sql": "select * from `country` where `country`.`id` in (12, 14, 15)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 231}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.848372, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ConferenceFilter.php:231", "source": {"index": 21, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 231}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FLivewire%2FConferenceFilter.php&line=231", "ajax": false, "filename": "ConferenceFilter.php", "line": "231"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 88.066, "width_percent": 1.04}, {"sql": "select `category`.*, `list`.`eid` as `pivot_eid`, `list`.`cid` as `pivot_cid` from `category` inner join `list` on `category`.`id` = `list`.`cid` where `list`.`eid` in (5429, 5430, 5465, 5483, 5488, 5492, 5499, 5500, 5504, 5505)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 231}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.8503342, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ConferenceFilter.php:231", "source": {"index": 20, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 231}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FLivewire%2FConferenceFilter.php&line=231", "ajax": false, "filename": "ConferenceFilter.php", "line": "231"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 89.106, "width_percent": 1.714}, {"sql": "select * from `category` where `fid` = 0 order by `listorder` asc", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 234}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.8514812, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "ConferenceFilter.php:234", "source": {"index": 15, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 234}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FLivewire%2FConferenceFilter.php&line=234", "ajax": false, "filename": "ConferenceFilter.php", "line": "234"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 90.82, "width_percent": 0.673}, {"sql": "select * from `country` where `fid` = 0 order by `listorder` asc", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 235}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.851901, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "ConferenceFilter.php:235", "source": {"index": 15, "namespace": null, "name": "app/Livewire/ConferenceFilter.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Livewire\\ConferenceFilter.php", "line": 235}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FLivewire%2FConferenceFilter.php&line=235", "ajax": false, "filename": "ConferenceFilter.php", "line": "235"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 91.493, "width_percent": 0.551}, {"sql": "select * from `html_fragments` where `is_active` = 1 and `type` = 'js'", "type": "query", "params": [], "bindings": [1, "js"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/View/Composers/HtmlFragmentComposer.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Composers\\HtmlFragmentComposer.php", "line": 19}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 189}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160}], "start": **********.865279, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HtmlFragmentComposer.php:19", "source": {"index": 15, "namespace": null, "name": "app/View/Composers/HtmlFragmentComposer.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Composers\\HtmlFragmentComposer.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComposers%2FHtmlFragmentComposer.php&line=19", "ajax": false, "filename": "HtmlFragmentComposer.php", "line": "19"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 92.044, "width_percent": 4.468}, {"sql": "select * from `links` where `status` = 1 order by `listorder` asc, `id` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/View/Components/FriendlyLinks.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\FriendlyLinks.php", "line": 23}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 110}, {"index": 17, "namespace": "view", "name": "frontend.layouts.app", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\resources\\views/frontend/layouts/app.blade.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.8690472, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "FriendlyLinks.php:23", "source": {"index": 15, "namespace": null, "name": "app/View/Components/FriendlyLinks.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\View\\Components\\FriendlyLinks.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FView%2FComponents%2FFriendlyLinks.php&line=23", "ajax": false, "filename": "FriendlyLinks.php", "line": "23"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 96.512, "width_percent": 2.876}, {"sql": "select * from `cache` where `key` in ('iconf_meeting_cache_html_fragment_footer')", "type": "query", "params": [], "bindings": ["iconf_meeting_cache_html_fragment_footer"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "app/Services/HtmlFragmentService.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\app\\Services\\HtmlFragmentService.php", "line": 36}], "start": **********.870434, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\phpEnv\\www\\iconf_org_by_laravel\\iconf-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "iconf_laravel_db", "explain": null, "start_percent": 99.388, "width_percent": 0.612}]}, "models": {"data": {"App\\Models\\Category": {"value": 30, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Event": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "App\\Models\\Country": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FCountry.php&line=1", "ajax": false, "filename": "Country.php", "line": "?"}}, "App\\Models\\Link": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FLink.php&line=1", "ajax": false, "filename": "Link.php", "line": "?"}}, "App\\Models\\HtmlFragment": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FModels%2FHtmlFragment.php&line=1", "ajax": false, "filename": "HtmlFragment.php", "line": "?"}}}, "count": 58, "is_counter": true}, "livewire": {"data": {"conference-filter #Ol0pSkUopLcdFb0iJNj6": "array:4 [\n  \"data\" => array:9 [\n    \"keyword\" => \"\"\n    \"selectedCategory\" => null\n    \"selectedCountry\" => null\n    \"selectedYear\" => null\n    \"selectedTopCategoryId\" => null\n    \"selectedContinentId\" => null\n    \"subCategories\" => Illuminate\\Database\\Eloquent\\Collection {#2639\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n    \"countriesInContinent\" => Illuminate\\Database\\Eloquent\\Collection {#2638\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"conference-filter\"\n  \"component\" => \"App\\Livewire\\ConferenceFilter\"\n  \"id\" => \"Ol0pSkUopLcdFb0iJNj6\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://iconf.lv/conferences?category=&country=&year=", "action_name": "conferences.index", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@conferences", "uri": "GET conferences", "controller": "App\\Http\\Controllers\\Frontend\\HomeController@conferences<a href=\"phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=99\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2FphpEnv%2Fwww%2Ficonf_org_by_laravel%2Ficonf-laravel%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=99\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/HomeController.php:99-107</a>", "middleware": "web", "duration": "269ms", "peak_memory": "50MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-769548461 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>category</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>country</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>year</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-769548461\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-746297066 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-746297066\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1791762695 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"719 characters\">XSRF-TOKEN=eyJpdiI6ImNVVExxZStTRE9Tb1BIc0IrRXJRSGc9PSIsInZhbHVlIjoidURYYnovYVZtOXU3dGYzZnVuZnBYNGw3UmdnTUpsdzBQWWlGZEZneTUxVmt0WUdyNHVXOGcwRmZPV2dmQUZRY2hVa2ZDQis1a0ttV2xRQ1NvdjkzWEdHQlMzV00zR2YzdXZwdmVGOFBmbERyd3pFeGwrRXorTG5zWS9mTWcvMWsiLCJtYWMiOiI2ZjYxNzI0ZDFhMTdlNjgzODYwMGVjYWRkMzI2MGQ0OWY5NmZmMDZmYzJlMTEzN2U5MTE2OWVkYmJkOTcyYjdiIiwidGFnIjoiIn0%3D; iconf_meeting_session=eyJpdiI6InZFOTFxbnNENVdRUG41RDYvaFZsQ1E9PSIsInZhbHVlIjoiWWgxK2VWSSsvSHZwVXNJUWlJNi9adEpQYkFqVFdzV1RiNGlGeVNCbzJycFovZkUwdytxU1dvTjFnS2ZVQzVDZWdrMmhzd3BGQ2xIcmdpaEtvSlJEQWNHU21rc0RXRlVsc0QxWGFyVHhQdTc0UFI2ZjRNQ1lrdXV2clkzNTI2YXIiLCJtYWMiOiJiMDliNWQ0ZjQ2Y2E1YzQ5ZjljYjIzNmFhZTk5OGVmMGU4ZTE1MjMyZDA5MjkwZGYwYWM0MGZiZjlmOWM2MzM4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://iconf.lv/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">iconf.lv</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1791762695\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-546377077 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KzVnHwsb1j7aFLoepnAgBRMhnc1xQgVO8Wwk7tZ4</span>\"\n  \"<span class=sf-dump-key>iconf_meeting_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">n81JYPLEP0cENw7ZqI8BBaufrhZyV3CgHX5ObznX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-546377077\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1478723086 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 16 Jul 2025 11:28:32 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1478723086\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-346887093 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KzVnHwsb1j7aFLoepnAgBRMhnc1xQgVO8Wwk7tZ4</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"50 characters\">http://iconf.lv/categories/artificial_intelligence</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$i.a5gP7/Z07pk3CeR6vNfe9lGm.BtthHlLi1.olj0JJDfdNlCRY8K</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-346887093\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://iconf.lv/conferences?category=&country=&year=", "action_name": "conferences.index", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@conferences"}, "badge": null}}