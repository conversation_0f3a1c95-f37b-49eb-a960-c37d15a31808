# 国际化学术会议网站二级筛选功能实现文档

## 功能概述

在/conferences会议首页的筛选器上方，增加了二级筛选功能，包括：

- 一级分类点击后显示对应二级分类
- 大洲点击后显示对应国家
- 更详细的日期筛选（包括自定义日期范围）

## 技术实现

### 前端实现

1. 创建了新的`filter-bar-advanced.blade.php`组件，替代原有的`filter-bar.blade.php`组件
2. 使用原生JavaScript和AJAX实现动态加载二级分类和国家列表
3. 实现了可切换的日期范围筛选UI，支持按年份或自定义日期范围筛选

### 后端实现

1. 创建了`FilterController`控制器，提供两个API端点：
   - `/api/filters/subcategories/{parentId}`：获取指定父分类下的子分类
   - `/api/filters/countries/{continentId}`：获取指定大洲下的国家列表

2. 扩展了`HomeController@conferences`方法，支持接收新增的筛选参数：
   - `filter_subcategory`：二级分类
   - `filter_continent`：大洲
   - `filter_start_date`和`filter_end_date`：日期范围

3. 增强了`HomeService@getAllConferences`方法的筛选逻辑：
   - 优先使用二级分类筛选，如果提供了二级分类，则忽略一级分类
   - 支持按大洲筛选，如果提供了大洲，则筛选该大洲下所有国家的会议
   - 实现了更精确的日期范围筛选，支持筛选指定日期范围内的会议

## 数据结构

### 分类结构

分类表（Category）使用`fid`字段表示父子关系：
- `fid = 0`：一级分类
- `fid > 0`：二级分类，其中`fid`值为父分类的ID

### 国家结构

国家表（Country）同样使用`fid`字段表示大洲和国家的关系：
- `fid = 0`：大洲
- `fid > 0`：国家，其中`fid`值为所属大洲的ID

## API接口

### 获取子分类

```
GET /api/filters/subcategories/{parentId}
```

**参数：**
- `parentId`：父分类ID

**响应：**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "子分类名称",
      "url": "subcategory-url"
    }
  ]
}
```

### 获取国家列表

```
GET /api/filters/countries/{continentId}
```

**参数：**
- `continentId`：大洲ID

**响应：**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "venue": "国家名称",
      "url": "country-url"
    }
  ]
}
```

## 单元测试

为确保功能稳定性，编写了以下单元测试：

1. `FilterControllerTest`：测试API端点的正确性
   - `test_get_subcategories`：测试获取子分类API
   - `test_get_countries`：测试获取国家列表API
   - `test_get_subcategories_empty`：测试当没有子分类时的响应
   - `test_get_countries_empty`：测试当没有国家时的响应

2. `HomeServiceTest`：测试筛选逻辑的正确性
   - `test_basic_filtering`：测试基本筛选功能（关键词、分类、国家、年份）
   - `test_advanced_filtering`：测试高级筛选功能（二级分类、大洲、日期范围）
   - `test_combined_filtering`：测试组合筛选功能

## 使用说明

1. 在/conferences页面，用户可以点击一级分类，查看并选择对应的二级分类
2. 用户可以点击大洲，查看并选择对应的国家
3. 用户可以选择按年份筛选，或切换到自定义日期范围进行更精确的筛选
4. 所有筛选条件可以组合使用，实现精确的会议查询

## 注意事项

1. 二级筛选功能依赖于数据库中分类和国家的正确层级关系
2. 日期范围筛选会查找开始日期、结束日期或整个会议时间段与筛选范围有交集的会议
3. 当同时提供了一级分类和二级分类时，系统会优先使用二级分类进行筛选
