<?php

namespace App\Console\Commands;

use App\Models\News;
use App\Models\NewsType;
use Illuminate\Console\Command;

class TestNewsManagement extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:news-management';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试新闻管理功能';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 开始测试新闻管理功能...');
        $this->newLine();

        // 测试新闻分类
        $this->testNewsTypes();
        $this->newLine();

        // 测试新闻
        $this->testNews();
        $this->newLine();

        // 测试关联关系
        $this->testRelationships();
        $this->newLine();

        $this->info('✅ 新闻管理功能测试完成！');
    }

    private function testNewsTypes()
    {
        $this->info('📂 测试新闻分类功能：');

        $newsTypes = NewsType::ordered()->get();
        $this->table(
            ['ID', '分类名称', '排序', '新闻数量'],
            $newsTypes->map(function ($type) {
                return [
                    $type->id,
                    $type->column_name,
                    $type->column_sort,
                    $type->getNewsCount(),
                ];
            })
        );

        $this->info("📊 新闻分类总数：{$newsTypes->count()}");
    }

    private function testNews()
    {
        $this->info('📰 测试新闻功能：');

        $news = News::with('newsType')->orderBy('publish_time', 'desc')->get();
        $this->table(
            ['ID', '标题', '分类', '推荐', '点击率', '发布时间'],
            $news->map(function ($item) {
                return [
                    $item->id,
                    mb_substr($item->title, 0, 30) . (mb_strlen($item->title) > 30 ? '...' : ''),
                    $item->newsType?->column_name ?? '无分类',
                    $item->is_featured ? '是' : '否',
                    $item->click_rate,
                    $item->publish_time->format('Y-m-d H:i'),
                ];
            })
        );

        $this->info("📊 新闻总数：{$news->count()}");
        $this->info("⭐ 推荐新闻：{$news->where('is_featured', true)->count()}");
        $this->info("📈 总点击率：{$news->sum('click_rate')}");
    }

    private function testRelationships()
    {
        $this->info('🔗 测试关联关系：');

        // 测试新闻分类关联
        $newsTypes = NewsType::with('news')->get();
        foreach ($newsTypes as $type) {
            $newsCount = $type->news->count();
            $featuredCount = $type->getFeaturedNews()->count();
            $this->line("  📂 {$type->column_name}: {$newsCount}篇新闻，{$featuredCount}篇推荐");
        }

        // 测试新闻访问器
        $this->newLine();
        $this->info('🔍 测试新闻访问器：');
        $firstNews = News::first();
        if ($firstNews) {
            $this->line("  📰 标题：{$firstNews->title}");
            $this->line("  📝 摘要：{$firstNews->excerpt}");
            $this->line("  🖼️  封面：" . ($firstNews->hasCover() ? '有' : '无'));
            $this->line("  📅 发布时间：{$firstNews->publish_time->format('Y-m-d H:i:s')}");
        }
    }
}
