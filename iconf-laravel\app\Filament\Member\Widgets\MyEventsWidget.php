<?php

namespace App\Filament\Member\Widgets;

use App\Models\Event;
use App\Enums\ConferenceStatus;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Support\Facades\Auth;

class MyEventsWidget extends BaseWidget
{
    protected static ?string $heading = 'My Latest Conferences';
    
    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return $table
            ->query(
                Event::query()
                    ->where('uid', Auth::guard('member')->id())
                    ->with(['country', 'categories']) // Preload related data to avoid N+1 queries
                    ->latest('addtime')
                    ->limit(5)
            )
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->label('Conference Title')
                    ->searchable()
                    ->limit(50),
                    
                Tables\Columns\TextColumn::make('city')
                    ->label('City')
                    ->searchable(),
                    
                Tables\Columns\TextColumn::make('start_date')
                    ->label('Start Date')
                    ->date()
                    ->sortable(),
                    
                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->formatStateUsing(fn (ConferenceStatus $state): string => $state->label())
                    ->badge()
                    ->color(fn (ConferenceStatus $state): string => $state->color()),
                    
                Tables\Columns\TextColumn::make('addtime')
                    ->label('Publish Time')
                    ->dateTime()
                    ->sortable(),
            ])
            ->actions([
                Tables\Actions\Action::make('view')
                    ->label('View')
                    ->icon('heroicon-m-eye')
                    ->url(fn (Event $record): string => route('filament.member.resources.events.view', ['record' => $record])),

                Tables\Actions\Action::make('edit')
                    ->label('Edit')
                    ->icon('heroicon-m-pencil-square')
                    ->url(fn (Event $record): string => route('filament.member.resources.events.edit', ['record' => $record])),
            ])
            ->emptyStateHeading('No Conferences Yet')
            ->emptyStateDescription('You have not published any conferences yet. Click the button below to publish your first conference.')
            ->emptyStateActions([
                Tables\Actions\Action::make('create')
                    ->label('Publish Conference')
                    ->icon('heroicon-m-plus')
                    ->url(fn(): string => route('filament.member.resources.events.create')),
            ]);
    }
}
