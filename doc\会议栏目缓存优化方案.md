# 会议栏目缓存优化方案

**核心程序**：Laravel 12框架 + Filament Admin

## 基本背景

- 一个学术会议发布系统，每天的日均IP 在1000次左右
- 目前有70多个会议分类，包括一级栏目和二级栏目
- 会议分类相对固定，不会频繁的增加和更新

## 目前问题
- 首页、栏目页，列表页都均需要调用会议分类。目前直接通过数据库模型查询。比如访问一次首页，就要调用40多次category的查询
- 效率底下，将来如果访问量大了，会影响速度

## 优化构想

- 将栏目数据和结构缓存起来（利用文件或Redis）。每次修改栏目，新增栏目，删除栏目后，自动更新缓存
- 设置手动更新缓存的按钮
- 前台调用时不再查询数据库，直接读取缓存
- 增加更细粒度的缓存键，便于按需获取数据
- 使用标签(Tags)系统管理相关缓存，便于批量操作
- 增加缓存读取失败的容错机制
- 将栏目数据序列化为JSON，直接嵌入前端页面，减少API调用

## 实施方案

### 构思总览

我们将分六步走，完美地实现需求：

1. **缓存策略与技术选型**：
   * **驱动**：优先使用Laravel默认的`file`缓存。对于日均1000 IP的量级，文件缓存绰绰有余，无需引入Redis增加系统复杂性
   * **缓存键(Cache Key)**：定义唯一的、语义化的键，例如`categories.tree`，方便管理
   * **缓存周期**：使用永久缓存(`Cache::rememberForever`)，因为栏目数据只有在后台操作时才需要更新

2. **核心逻辑封装（服务类Service Class）**：
   * 创建`CategoryService`类，封装所有与栏目相关的逻辑

3. **缓存自动更新（模型观察者Model Observer）**：
   * 利用Laravel的模型观察者监视`Category`模型的事件，自动更新缓存

4. **手动更新与前台调用**：
   * 在Filament Admin中添加手动刷新按钮
   * 前台通过视图构造器(View Composer)调用缓存数据

5. **更细粒度的缓存键与标签系统**：
   * 实现多级缓存键，支持按需获取数据
   * 使用标签系统管理相关缓存，便于批量操作

6. **容错机制与前端优化**：
   * 增加缓存读取失败的容错机制
   * 将栏目数据序列化为JSON，直接嵌入前端页面

### 具体实施步骤

#### 第1步：创建核心服务类 `CategoryService`

1. **创建文件**：
   ```bash
   mkdir -p app/Services
   php artisan make:class Services/CategoryService 
   ```

2. **编写代码**：
   ```php
   // app/Services/CategoryService.php

   namespace App\Services;

   use App\Models\Category;
   use Illuminate\Support\Collection;
   use Illuminate\Support\Facades\Cache;
   use Illuminate\Support\Facades\Log;

   class CategoryService
   {
       /**
        * 缓存标签
        * @var array
        */
       protected array $cacheTags = ['categories'];

       /**
        * 缓存键
        * @var string
        */
       protected string $cacheKey = 'categories.tree';

       /**
        * 获取树状结构的栏目数据（优先从缓存读取）
        *
        * @return Collection
        */
       public function getTree(): Collection
       {
           try {
               return Cache::tags($this->cacheTags)->rememberForever($this->cacheKey, function () {
                   return $this->buildTree();
               });
           } catch (\Exception $e) {
               // 记录错误日志
               Log::error('Category cache error: ' . $e->getMessage());
               // 降级为直接从数据库读取
               return $this->buildTree();
           }
       }

       /**
        * 获取所有顶级栏目
        *
        * @return Collection
        */
       public function getTopCategories(): Collection
       {
           try {
               return Cache::tags($this->cacheTags)->rememberForever('categories.top', function () {
                   return Category::where('fid', 0)->orderBy('listorder')->get();
               });
           } catch (\Exception $e) {
               Log::error('Category cache error (top): ' . $e->getMessage());
               return Category::where('fid', 0)->orderBy('listorder')->get();
           }
       }

       /**
        * 获取指定父级栏目的所有子栏目
        *
        * @param int $parentId 父级栏目ID
        * @return Collection
        */
       public function getSubCategories(int $parentId): Collection
       {
           try {
               return Cache::tags($this->cacheTags)->rememberForever("categories.sub.{$parentId}", function () use ($parentId) {
                   return Category::where('fid', $parentId)->orderBy('listorder')->get();
               });
           } catch (\Exception $e) {
               Log::error("Category cache error (sub.{$parentId}): " . $e->getMessage());
               return Category::where('fid', $parentId)->orderBy('listorder')->get();
           }
       }

       /**
        * 获取单个栏目的详细信息
        *
        * @param int $categoryId 栏目ID
        * @return Category|null
        */
       public function getCategory(int $categoryId): ?Category
       {
           try {
               return Cache::tags($this->cacheTags)->rememberForever("categories.single.{$categoryId}", function () use ($categoryId) {
                   return Category::find($categoryId);
               });
           } catch (\Exception $e) {
               Log::error("Category cache error (single.{$categoryId}): " . $e->getMessage());
               return Category::find($categoryId);
           }
       }

       /**
        * 刷新栏目缓存
        *
        * @return Collection
        */
       public function refreshCache(): Collection
       {
           // 清除所有categories标签的缓存
           Cache::tags($this->cacheTags)->flush();
           
           // 重新生成并缓存数据
           return $this->getTree();
       }

       /**
        * 从数据库获取数据并构建树状结构
        *
        * @param int $parentId 父级ID
        * @return Collection
        */
       protected function buildTree(int $parentId = 0): Collection
       {
           // 1. 获取所有栏目，并按 listorder 排序
           $categories = Category::orderBy('listorder', 'asc')->get();

           // 2. 将集合按父ID (fid) 分组，方便查找子集
           $grouped = $categories->groupBy('fid');

           // 3. 从根节点（fid=0）开始递归构建树
           // 我们直接在所有分类上添加 'children' 属性，这样更高效
           $categories->each(function ($category) use ($grouped) {
               // 查找当前分类的所有子分类
               $children = $grouped->get($category->id, collect());
               $category->children = $children;
           });
           
           // 4. 返回顶级栏目（fid=0）集合，每个顶级栏目都已包含其子孙栏目
           return $grouped->get(0, collect());
       }

       /**
        * 获取JSON格式的栏目树数据（用于前端嵌入）
        *
        * @return string
        */
       public function getTreeAsJson(): string
       {
           return $this->getTree()->toJson();
       }
   }
   ```

#### 第2步：创建模型观察者 `CategoryObserver` 实现自动更新

1. **创建Observer**：
   ```bash
   php artisan make:observer CategoryObserver --model=Category
   ```

2. **编写Observer代码**：
   ```php
   // app/Observers/CategoryObserver.php

   namespace App\Observers;

   use App\Models\Category;
   use App\Services\CategoryService;

   class CategoryObserver
   {
       /**
        * 处理 Category "created" 事件.
        */
       public function created(Category $category): void
       {
           $this->clearCache();
       }

       /**
        * 处理 Category "updated" 事件.
        */
       public function updated(Category $category): void
       {
           $this->clearCache();
       }

       /**
        * 处理 Category "deleted" 事件.
        */
       public function deleted(Category $category): void
       {
           $this->clearCache();
       }

       /**
        * 统一调用刷新缓存的方法
        */
       protected function clearCache(): void
       {
           // 使用 app() 辅助函数获取服务实例并调用方法
           app(CategoryService::class)->refreshCache();
       }
   }
   ```

3. **注册Observer**：
   ```php
   // app/Providers/EventServiceProvider.php

   namespace App\Providers;

   use App\Models\Category;
   use App\Observers\CategoryObserver;
   use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

   class EventServiceProvider extends ServiceProvider
   {
       // ...

       public function boot(): void
       {
           Category::observe(CategoryObserver::class);
       }

       // ...
   }
   ```

#### 第3步：在Filament Admin中添加手动更新按钮

1. **编辑CategoryResource的列表页面类**：
   ```php
   // app/Filament/Resources/CategoryResource/Pages/ListCategories.php

   namespace App\Filament\Resources\CategoryResource\Pages;

   use App\Filament\Resources\CategoryResource;
   use App\Services\CategoryService;
   use Filament\Actions;
   use Filament\Notifications\Notification;
   use Filament\Resources\Pages\ListRecords;

   class ListCategories extends ListRecords
   {
       protected static string $resource = CategoryResource::class;

       protected function getHeaderActions(): array
       {
           return [
               Actions\CreateAction::make(),
               // 添加手动刷新按钮
               Actions\Action::make('refreshCache')
                   ->label('更新栏目缓存')
                   ->icon('heroicon-o-arrow-path')
                   ->color('success')
                   ->action(function () {
                       // 调用服务类的方法
                       app(CategoryService::class)->refreshCache();

                       // 给用户一个成功的反馈
                       Notification::make()
                           ->title('操作成功')
                           ->body('栏目缓存已成功更新！')
                           ->success()
                           ->send();
                   }),
           ];
       }
   }
   ```

#### 第4步：前台调用缓存数据

1. **创建ViewServiceProvider**：
   ```bash
   php artisan make:provider ViewServiceProvider
   ```

2. **在ViewServiceProvider中共享数据**：
   ```php
   // app/Providers/ViewServiceProvider.php

   namespace App\Providers;

   use App\Services\CategoryService;
   use Illuminate\Support\Facades\View;
   use Illuminate\Support\ServiceProvider;

   class ViewServiceProvider extends ServiceProvider
   {
       public function boot(CategoryService $categoryService): void
       {
           // 当渲染这些视图时，自动绑定变量
           View::composer(['layouts.app', 'pages.home', 'pages.category-list'], function ($view) use ($categoryService) {
               // 获取栏目树并传递给视图
               $categoryTree = $categoryService->getTree();
               $view->with('categoryTree', $categoryTree);
               
               // 将栏目数据序列化为JSON，直接嵌入前端页面
               $categoryJson = $categoryService->getTreeAsJson();
               $view->with('categoryJson', $categoryJson);
           });
       }
   }
   ```

3. **注册ViewServiceProvider**：
   在`config/app.php`的`providers`数组中添加：
   ```php
   App\Providers\ViewServiceProvider::class,
   ```

4. **在Blade模板中使用**：
   ```blade
   {{-- 在布局文件中，如layouts/app.blade.php --}}
   <script>
       window.appData = {
           categories: {!! $categoryJson !!}
       };
   </script>

   {{-- 在导航栏视图中 --}}
   <ul>
       @foreach ($categoryTree as $topCategory)
           <li>
               <a href="{{ url($topCategory->url) }}">{{ $topCategory->name }}</a>
               {{-- 如果有二级栏目 --}}
               @if ($topCategory->children->isNotEmpty())
                   <ul>
                       @foreach ($topCategory->children as $subCategory)
                           <li>
                               <a href="{{ url($subCategory->url) }}">{{ $subCategory->name }}</a>
                           </li>
                       @endforeach
                   </ul>
               @endif
           </li>
       @endforeach
   </ul>
   ```

#### 第5步：添加定时任务确保缓存一致性

```php
// app/Console/Kernel.php
protected function schedule(Schedule $schedule)
{
    // 每天凌晨3点自动刷新缓存，确保数据一致性
    $schedule->call(function () {
        app(CategoryService::class)->refreshCache();
    })->daily()->at('03:00');
}
```

## 方案优势

1. **性能优化**：
   * 将40多次数据库查询减少为0次，显著提升页面加载速度
   * 通过细粒度缓存键，支持按需获取数据，避免不必要的数据加载

2. **高可用性**：
   * 增加缓存读取失败的容错机制，确保系统稳定性
   * 提供手动刷新按钮，应对特殊情况

3. **前端优化**：
   * 将栏目数据序列化为JSON直接嵌入页面，减少API调用
   * 前端JavaScript可直接访问栏目数据，提升用户体验

4. **可维护性**：
   * 遵循Laravel最佳实践，通过服务类、观察者、服务提供者等模式，代码职责分明
   * 使用标签系统管理缓存，便于批量操作和维护

5. **可扩展性**：
   * 当访问量增长时，只需在`.env`文件中将缓存驱动从`file`改为`redis`，无需修改代码
   * 架构设计支持未来功能扩展，如多语言支持等

## 总结

这套方案完美地平衡了性能、简洁性和可维护性，遵循Laravel的设计哲学，不过度设计，技术栈相对简单。通过缓存机制，将大量重复的数据库查询转为高效的缓存读取，同时保证了数据的一致性和系统的稳定性。

随着未来访问量的增长，这个方案可以平滑升级，持续为系统提供稳定高效的服务。
