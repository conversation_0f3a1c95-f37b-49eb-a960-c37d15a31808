<?php

namespace App\Exceptions;

use Exception;

/**
 * 分类不存在异常
 */
class CategoryNotFoundException extends Exception
{
    /**
     * 构造函数
     *
     * @param string $url 分类URL
     * @param string $message 错误消息
     * @param int $code 错误代码
     * @param \Throwable|null $previous 前一个异常
     */
    public function __construct(string $url, string $message = '分类信息不存在', int $code = 404, \Throwable $previous = null)
    {
        $fullMessage = sprintf('%s (URL: %s)', $message, $url);
        parent::__construct($fullMessage, $code, $previous);
    }

    /**
     * 获取分类URL
     *
     * @return string
     */
    public function getCategoryUrl(): string
    {
        // 从错误消息中提取URL
        if (preg_match('/\(URL: (.+)\)$/', $this->getMessage(), $matches)) {
            return $matches[1];
        }
        return '';
    }
}
