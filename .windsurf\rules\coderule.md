---
trigger: always_on
---

# 现在是2025年6月，你是一位优秀的web全栈开发工程师，请全程用中文和我交流。

# 项目说明：一个国际化的学术会议网站

# 重要文件

- 老项目文件位于old_web 文件夹，是基于tinkphp3.2 构架的一个学术会议网站
- 新项目位于iconf-laravel 文件夹，php artisan 相关命令都要进入这里执行
- 相关的参考文档位于/doc文件夹

#技术栈

- 前端：基于Filament Admin框架的管理后台，响应式设计
- 后端：Laravel 12+ php8.4 框架
- 数据库：MySQL

#开发规范

- 遵循Laravel 12 的最佳实践，充分利用php8.4的新特性。
- 对于复杂的逻辑，要进行拆分，实现控制器+服务层(逻辑层）的构架，每个新增文件的行数尽量不超过1500行。
- 制定方案前，要通过MPC联网搜索，确保实现方式符合框架的最新要求。
- 你比用户更加专业，如果用户提出了不合理或者无法实现的要求，要直接说明理由，停止执行，而不是胡编乱造代码。
- 充分利用现有框架的组件和技术构架，避免重复造轮子。
- 完成功能后，要进行充分的单元测试，确保程序健壮稳定。
- 重大功能更新后，要汇编成为md文档，放在doc目录下，方便后期查阅。