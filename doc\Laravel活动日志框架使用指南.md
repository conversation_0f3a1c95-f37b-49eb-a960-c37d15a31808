# Laravel 活动日志框架使用指南

> 文档创建日期：2025年6月26日  
> 适用框架：Laravel 12 + Filament Admin

## 目录

- [1. 框架介绍](#1-框架介绍)
- [2. 安装步骤](#2-安装步骤)
- [3. 基础配置](#3-基础配置)
- [4. 在模型中使用](#4-在模型中使用)
- [5. 手动记录日志](#5-手动记录日志)
- [6. Filament 管理界面配置](#6-filament-管理界面配置)
- [7. 高级用法](#7-高级用法)
- [8. 常见问题](#8-常见问题)

## 1. 框架介绍

### 1.1 框架组成

本方案由两个主要组件构成：

1. **Spatie/laravel-activitylog**：核心日志记录功能，负责记录和存储活动日志
2. **rmsramos/activitylog**：Filament Admin 集成插件，提供管理界面

### 1.2 主要特性

- 自动记录模型变更
- 支持自定义日志描述（语义化）
- 可记录额外属性和关联
- 支持多种日志驱动
- 完整的 Filament Admin 集成
- 多语言支持

### 1.3 为什么选择这个方案

- 轻量级，开箱即用
- 配置简单，灵活性高
- 可同时记录前台用户和后台管理员操作
- 持续维护更新，支持 Laravel 12
- 与 Filament Admin 无缝集成

## 2. 安装步骤

### 2.1 安装核心包

```bash
# 安装 Spatie 的 Activity Log 包
composer require spatie/laravel-activitylog

# 安装 Filament 集成插件
composer require rmsramos/activitylog

# 运行安装命令（自动发布配置和迁移）
php artisan activitylog:install

# 执行数据库迁移
php artisan migrate
```

### 2.2 验证安装

安装完成后，数据库中应该有一个 `activity_log` 表，包含以下字段：

- `id`：日志 ID
- `log_name`：日志名称
- `description`：日志描述
- `subject_type`：关联模型类型
- `subject_id`：关联模型 ID
- `causer_type`：操作者类型
- `causer_id`：操作者 ID
- `properties`：额外属性（JSON）
- `created_at`：创建时间
- `updated_at`：更新时间

## 3. 基础配置

### 3.1 发布配置文件

如果安装命令没有自动发布配置文件，可以手动发布：

```bash
php artisan vendor:publish --tag="activitylog-config"
```

### 3.2 配置 Filament 资源

编辑 `config/activitylog.php` 文件，根据需求调整：

```php
return [
    'resources' => [
        'label' => '活动日志',
        'plural_label' => '活动日志',
        'navigation_item' => true,
        'navigation_group' => '系统管理',
        'navigation_icon' => 'heroicon-o-clipboard-document-list',
        'navigation_sort' => 1,
        'default_sort_column' => 'id',
        'default_sort_direction' => 'desc',
        'navigation_count_badge' => false,
        'resource' => \Rmsramos\Activitylog\Resources\ActivitylogResource::class,
    ],
    'datetime_format' => 'Y-m-d H:i:s',
];
```

### 3.3 自定义视图（可选）

如果需要自定义界面，可以发布视图文件：

```bash
php artisan vendor:publish --tag="activitylog-views"
```

## 4. 在模型中使用

### 4.1 基本用法

在需要记录日志的模型中添加 `LogsActivity` trait：

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Article extends Model
{
    use LogsActivity;
    
    protected $fillable = ['title', 'content', 'status'];
    
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['title', 'content', 'status'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
}
```

### 4.2 自定义日志名称

```php
protected static $logName = 'articles';
```

### 4.3 自定义日志描述

```php
public function getDescriptionForEvent(string $eventName): string
{
    return "用户 {$this->causer->name} {$eventName}了文章 {$this->title}";
}
```

### 4.4 记录所有属性

```php
public function getActivitylogOptions(): LogOptions
{
    return LogOptions::defaults()
        ->logAll()
        ->logOnlyDirty();
}
```

### 4.5 记录关联模型

```php
public function getActivitylogOptions(): LogOptions
{
    return LogOptions::defaults()
        ->logOnly(['title', 'content', 'status'])
        ->logOnlyDirty()
        ->dontSubmitEmptyLogs()
        ->logExcept(['password', 'remember_token'])
        ->logOnlyDirty()
        ->dontLogIfAttributesChangedOnly(['updated_at'])
        ->dontSubmitEmptyLogs();
}
```

## 5. 手动记录日志

### 5.1 简单记录

```php
activity()->log('用户查看了仪表盘');
```

### 5.2 详细记录

```php
activity()
    ->performedOn($article)       // 关联模型
    ->causedBy(auth()->user())    // 执行者
    ->withProperties([            // 额外属性
        'custom' => '自定义值',
        'ip' => request()->ip()
    ])
    ->log('查看了文章');
```

### 5.3 在控制器中使用

```php
<?php

namespace App\Http\Controllers;

use App\Models\Article;
use Illuminate\Http\Request;

class ArticleController extends Controller
{
    public function show(Article $article)
    {
        activity()
            ->performedOn($article)
            ->causedBy(auth()->user())
            ->withProperties(['ip' => request()->ip()])
            ->log('查看了文章');
            
        return view('articles.show', compact('article'));
    }
}
```

### 5.4 在 Filament 资源中使用

```php
<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ArticleResource\Pages;
use App\Models\Article;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;

class ArticleResource extends Resource
{
    // ...其他代码...
    
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListArticles::route('/'),
            'create' => Pages\CreateArticle::route('/create'),
            'edit' => Pages\EditArticle::route('/{record}/edit'),
        ];
    }
}

// 在 Pages 目录下的页面类中
namespace App\Filament\Resources\ArticleResource\Pages;

use App\Filament\Resources\ArticleResource;
use Filament\Resources\Pages\CreateRecord;

class CreateArticle extends CreateRecord
{
    protected static string $resource = ArticleResource::class;
    
    protected function afterCreate(): void
    {
        // 手动记录日志
        activity()
            ->performedOn($this->record)
            ->causedBy(auth()->user())
            ->log('创建了新文章');
    }
}
```

## 6. Filament 管理界面配置

### 6.1 基本配置

安装 rmsramos/activitylog 后，会自动注册一个 Filament 资源。确保在 `config/activitylog.php` 中启用了导航项：

```php
'navigation_item' => true,
```

### 6.2 自定义资源

如果需要自定义活动日志资源，可以创建自己的资源类：

```php
<?php

namespace App\Filament\Resources;

use Rmsramos\Activitylog\Resources\ActivitylogResource as BaseActivitylogResource;

class CustomActivitylogResource extends BaseActivitylogResource
{
    // 自定义代码
}
```

然后在配置文件中指定：

```php
'resource' => \App\Filament\Resources\CustomActivitylogResource::class,
```

### 6.3 权限配置

如果使用 Filament Shield 或其他权限系统，可以配置谁可以查看活动日志：

```php
public static function canViewAny(): bool
{
    return auth()->user()->can('view_activity_logs');
}
```

## 7. 高级用法

### 7.1 清理旧日志

创建一个命令来定期清理旧日志：

```php
<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Activitylog\Models\Activity;
use Carbon\Carbon;

class CleanActivityLogs extends Command
{
    protected $signature = 'logs:clean {--days=30}';
    protected $description = '清理旧的活动日志';

    public function handle()
    {
        $days = $this->option('days');
        $date = Carbon::now()->subDays($days);
        
        $count = Activity::where('created_at', '<', $date)->delete();
        
        $this->info("已删除 {$count} 条旧日志");
    }
}
```

在 `app/Console/Kernel.php` 中注册定时任务：

```php
protected function schedule(Schedule $schedule)
{
    $schedule->command('logs:clean')->monthly();
}
```

### 7.2 自定义活动日志模型

如果需要扩展活动日志模型，可以创建自己的模型：

```php
<?php

namespace App\Models;

use Spatie\Activitylog\Models\Activity as SpatieActivity;

class Activity extends SpatieActivity
{
    // 自定义代码
}
```

然后在 `config/activitylog.php` 中指定：

```php
'activity_model' => \App\Models\Activity::class,
```

### 7.3 记录特定事件

```php
public function getActivitylogOptions(): LogOptions
{
    return LogOptions::defaults()
        ->logOnly(['title', 'content'])
        ->logOnlyDirty()
        ->dontSubmitEmptyLogs()
        ->logOnly(['title', 'content'])
        ->logOnlyOnEvents(['created', 'updated'])
        ->dontLogIfAttributesChangedOnly(['views_count']);
}
```

## 8. 常见问题

### 8.1 日志没有记录

- 检查模型是否正确使用了 `LogsActivity` trait
- 确认 `getActivitylogOptions` 方法返回了正确的配置
- 验证数据库迁移是否正确执行

### 8.2 自定义描述不生效

- 确保 `getDescriptionForEvent` 方法名称拼写正确
- 检查方法返回的是字符串

### 8.3 Filament 资源不显示

- 检查 `config/activitylog.php` 中的 `navigation_item` 是否设置为 `true`
- 确认用户有查看权限
- 检查导航组是否正确配置

### 8.4 性能优化

- 定期清理旧日志
- 只记录必要的字段变更
- 使用 `logOnlyDirty()` 只记录变更的字段
- 使用 `dontSubmitEmptyLogs()` 避免记录空日志

---

## 参考资源

- [Spatie Activity Log 官方文档](https://spatie.be/docs/laravel-activitylog)
- [rmsramos/activitylog GitHub](https://github.com/rmsramos/activitylog)
- [Filament 官方文档](https://filamentphp.com/docs)
