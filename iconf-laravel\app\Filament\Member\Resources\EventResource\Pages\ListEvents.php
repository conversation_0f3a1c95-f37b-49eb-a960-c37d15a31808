<?php

namespace App\Filament\Member\Resources\EventResource\Pages;

use App\Filament\Member\Resources\EventResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use App\Enums\ConferenceStatus;
use Illuminate\Database\Eloquent\Builder;

class ListEvents extends ListRecords
{
    protected static string $resource = EventResource::class;

    protected static ?string $title = 'My Conferences';

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('Publish Conference')
                ->icon('heroicon-o-plus')
                ->color('primary'),
        ];
    }

    /**
     * Get Tabs
     */
    public function getTabs(): array
    {
        // Use cache to get status counts, avoiding duplicate queries
        $memberId = auth('member')->id();
        $cacheKey = "member:{$memberId}:event_status_counts";
        
        $statusCounts = \Illuminate\Support\Facades\Cache::remember(
            $cacheKey,
            now()->addMinutes(5),
            function () use ($memberId) {
                // Use a single query to get counts for all statuses
                return $this->getModel()::query()
                    ->where('uid', $memberId)
                    ->selectRaw('status, count(*) as count')
                    ->groupBy('status')
                    ->pluck('count', 'status')
                    ->toArray();
            }
        );
        
        return [
            'all' => Tab::make('All')
                ->icon('heroicon-o-queue-list'),

            'published' => Tab::make('Published')
                ->icon('heroicon-o-check-circle')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', ConferenceStatus::Published))
                ->badge(fn () => $statusCounts[ConferenceStatus::Published->value] ?? 0),

            'pending' => Tab::make('Pending')
                ->icon('heroicon-o-clock')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', ConferenceStatus::Pending))
                ->badge(fn () => $statusCounts[ConferenceStatus::Pending->value] ?? 0),

            'draft' => Tab::make('Draft')
                ->icon('heroicon-o-document')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', ConferenceStatus::Draft))
                ->badge(fn () => $statusCounts[ConferenceStatus::Draft->value] ?? 0),

            'rejected' => Tab::make('Rejected')
                ->icon('heroicon-o-x-circle')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', ConferenceStatus::Rejected))
                ->badge(fn () => $statusCounts[ConferenceStatus::Rejected->value] ?? 0),
        ];
    }

    /**
     * Get Header Widgets
     */
    protected function getHeaderWidgets(): array
    {
        return [
            // Statistics widgets can be added here
        ];
    }

    /**
     * Custom Empty State
     */
    protected function getEmptyStateHeading(): ?string
    {
        return 'No conferences have been published yet';
    }

    protected function getEmptyStateDescription(): ?string
    {
        return 'Click the "Publish Conference" button to start publishing your first academic conference information.';
    }

    protected function getEmptyStateIcon(): ?string
    {
        return 'heroicon-o-calendar-days';
    }
}
