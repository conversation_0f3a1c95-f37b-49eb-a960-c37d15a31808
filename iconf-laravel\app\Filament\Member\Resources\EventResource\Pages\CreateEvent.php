<?php

namespace App\Filament\Member\Resources\EventResource\Pages;

use App\Filament\Member\Resources\EventResource;
use App\Filament\Concerns\HandlesEventData;
use App\Services\EventUrlService;
use App\Enums\ConferenceStatus;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Auth;

class CreateEvent extends CreateRecord
{
    use HandlesEventData;

    protected static string $resource = EventResource::class;

    protected static ?string $title = 'Create Conference';

    /**
     * Process data before creating a record
     */
    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $member = Auth::guard('member')->user();

        // Ensure user ID is set
        $data['uid'] = $member->id;
        $data['creator_type'] = 'member';

        // Set approval status based on user's VIP status
        $data['status'] = $member->vip ? ConferenceStatus::Published : ConferenceStatus::Pending;

        // Set creation time
        $data['addtime'] = time();

        // Process date fields: convert Carbon objects to Unix timestamps
        $data = $this->convertDateTimesToTimestamps($data);

        // Process URL field - generate automatically if empty
        if (empty($data['url']) && !empty($data['event'])) {
            $urlService = app(EventUrlService::class);
            $data['url'] = $urlService->generateUniqueUrl($data['event']);
        } elseif (!empty($data['url'])) {
            // If user provided a URL, ensure it's unique
            $urlService = app(EventUrlService::class);
            $data['url'] = $urlService->generateUniqueUrl($data['url']);
        }

        return $data;
    }

    /**
     * Handle after record creation
     */
    protected function afterCreate(): void
    {
        // Sync cid field with categories relationship
        $this->syncCategoriesWithCidField();

        // Note: list table record creation is handled by EventObserver
        // For VIP users with Published status, the observer will automatically create list records
        // For regular users with Pending status, the observer won't create list records until approved
    }

    /**
     * Get notification message after successful creation
     */
    protected function getCreatedNotificationTitle(): ?string
    {
        $member = Auth::guard('member')->user();

        if ($member->vip) {
            return 'Conference published successfully! Your conference is now live.';
        } else {
            return 'Conference submitted successfully! Please wait for admin approval.';
        }
    }

    /**
     * Notification description after successful creation
     */
    protected function getCreatedNotificationBody(): ?string
    {
        $member = Auth::guard('member')->user();

        if ($member->vip) {
            return 'As a VIP user, your conference has been automatically approved and published. Users can now see your conference information on the website.';
        } else {
            return 'Your conference has been submitted and is pending admin approval. It will be displayed on the website once approved.';
        }
    }

    /**
     * Redirect after successful creation
     */
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    /**
     * Get form action buttons
     */
    protected function getFormActions(): array
    {
        $member = Auth::guard('member')->user();
        
        return [
            $this->getCreateFormAction()
                ->label($member->vip ? 'Publish Conference' : 'Submit for Review'),
            
            $this->getCreateAnotherFormAction()
                ->label('Publish and Add Another'),
                
            $this->getCancelFormAction(),
        ];
    }

    /**
     * Custom page subtitle
     */
    public function getSubheading(): ?string
    {
        $member = Auth::guard('member')->user();

        if ($member->vip) {
            return 'As a VIP user, your conference will be published immediately.';
        } else {
            return 'Your conference will be displayed on the website after admin approval.';
        }
    }

}
