<?php

namespace App\Filament\Member\Widgets;

use App\Enums\ConferenceStatus;
use App\Models\Category;
use App\Models\Event;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class FocusAreaEventsWidget extends Widget
{
    protected static string $view = 'filament.member.widgets.focus-area-events';

    protected int|string|array $columnSpan = [
        'md' => 2,
        'xl' => 1,
    ];

    protected static ?int $sort = 5;

    /**
     * Get up to 10 unfinished conferences in the current member's focus area
     * Optimization points:
     * 1. Use a cache tag system for precise control over cache invalidation
     * 2. Use query builder instead of ORM for improved query efficiency
     * 3. Pre-format date data to reduce processing logic in the view
     * 4. Use JOIN queries to avoid N+1 issues
     */
    public function getFocusedEvents(): array
    {
        $member = Auth::guard('member')->user();

        if (!$member || empty($member->area)) {
            return [];
        }

        // Use cache key to ensure uniqueness
        $cacheKey = "member:{$member->id}:focus_events";
        
        // Use normal cache, compatible with all cache drivers
        return Cache::remember($cacheKey, now()->addMinutes(15), function () use ($member) {
            // Get category IDs, do not use cache, because category changes are infrequent and queries are simple
            $categoryIds = Category::query()
                ->where('fid', (int)$member->area)
                ->orWhere('id', (int)$member->area)
                ->pluck('id')
                ->toArray();

            if (empty($categoryIds)) {
                return [];
            }

            // Use query builder instead of ORM to improve query efficiency
            $now = now()->timestamp;
            $events = DB::table('event')
                ->select([
                    'event.id',
                    'event.title',
                    'event.city',
                    'event.start_date',
                    'event.end_date'
                ])
                ->join('list', function($join) use ($categoryIds) {
                    $join->on('event.id', '=', 'list.eid')
                         ->whereIn('list.cid', $categoryIds);
                })
                ->where('event.status', ConferenceStatus::Published->value)
                ->where('event.end_date', '>=', $now)
                ->orderBy('event.start_date')
                ->distinct()
                ->limit(10)
                ->get()
                ->map(function($event) {
                    // Format dates directly in the query results to avoid repeated parsing in the view
                    $startDate = Carbon::createFromTimestamp($event->start_date);
                    $endDate = Carbon::createFromTimestamp($event->end_date);
                    
                    return [
                        'id' => $event->id,
                        'title' => $event->title,
                        'start_date' => $startDate->toDateTimeString(),
                        'formatted_start_date' => [
                            'month' => $startDate->format('M'),
                            'day' => $startDate->format('d'),
                            'is_past' => $startDate->isPast(),
                            'diff' => $startDate->diffForHumans(null, false, false, 2),
                        ],
                        'end_date' => $endDate->toDateTimeString(),
                        'city' => $event->city,
                        'url' => '#',
                    ];
                });

            return $events->toArray();
        });
    }
}
